import React, { PureComponent } from 'react';
import { Steps } from 'zent';
import {
  IAuthSteps,
  AUTH_STEP_INDEX_MAP,
  AUTH_STEP_TITLE_MAP,
  AUTH_STEP_COMPONENT_MAP,
} from '../../constants/auth-step';

import { isShowCrossBorderSettlement } from '../../constants/global';

import './style.scss';

type IProps = ROUTER<
  {},
  {
    step: IAuthSteps;
  }
>;

export default class Authorize extends PureComponent<IProps> {
  private get authStep() {
    return this.props.params.step;
  }

  /**
   * 步骤条渲染
   */
  private renderSteps() {
    const { authStep } = this;
    const current = AUTH_STEP_INDEX_MAP[authStep];
    let stepsList = ['验证店铺', '缴纳保证金', '开通跨境结算', '完成'];
    if (!isShowCrossBorderSettlement) {
      stepsList = stepsList.filter(item => item !== '开通跨境结算');
    }
    return (
      <Steps type="breadcrumb" current={current}>
        {stepsList.map((item, index) => (
          <Steps.Step key={index} title={item} />
        ))}
      </Steps>
    );
  }

  /**
   * 根据 url 对应的步骤渲染当前步骤的组件
   */
  private renderContent() {
    const { authStep } = this;
    const Component = AUTH_STEP_COMPONENT_MAP[authStep];
    return (
      <div className="authorize-step__content">
        <Component />
      </div>
    );
  }

  public render() {
    return (
      <div className="cross-border-authorize">
        {this.renderSteps()}
        <div className="authorize-step__title">
          <h3>{AUTH_STEP_TITLE_MAP[this.authStep]}</h3>
        </div>
        {this.renderContent()}
      </div>
    );
  }
}
