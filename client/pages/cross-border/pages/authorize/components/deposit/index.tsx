import React, { PureComponent } from 'react';
import { withRouter, WithRouterProps } from 'react-router';
import { Card, Button, Notify, BlockLoading } from 'zent';
import formatMoney from '@youzan/utils/money/format';
import * as api from '../../../../api';
import openPayDialog from './components/pay-dialog';
import { isShowCrossBorderSettlement } from '../../../../constants/global';

import './style.scss';

type IProps = WithRouterProps;
interface IState {
  depositNum: number;
  shouldPay: number;
  depositLoading: boolean;
}

class AuthDeposit extends PureComponent<IProps, IState> {
  public state: IState = {
    depositNum: 0,
    shouldPay: 1000000,
    depositLoading: false,
  };

  public componentDidMount() {
    this.getDepositNum();
  }

  /**
   * 获取保证金余额
   */
  private getDepositNum() {
    this.setState({ depositLoading: true });
    Promise.all([api.getDepositShould(), api.getDepositRecord()])
      .then(([shouldRes, res]) => {
        const { crossBorderDepositAmount = 1000000 } = shouldRes;
        this.setState({
          depositNum: (res && res.depositAvl) || 0,
          // 预发环境验证时使用1分钱
          shouldPay: _global.nodeEnv === 'pre' ? 1 : crossBorderDepositAmount,
        });
      })
      .catch((err) => {
        Notify.error(err);
      })
      .finally(() => {
        this.setState({ depositLoading: false });
      });
  }

  /**
   * 支付成功回调
   */
  private onChargeSuccess = () => {
    // 支付成功，重新获取保证金余额
    this.getDepositNum();
  };

  /**
   * 打开充值弹框
   */
  private openCashierDialog = () => {
    const { shouldPay } = this.state;
    openPayDialog({
      onSuccess: this.onChargeSuccess,
      depositNeed: shouldPay - this.state.depositNum,
    });
  };

  private enterService = () => {
    if (isShowCrossBorderSettlement) {
      this.props.router.push('/authorize/crossBorderSettlement');
      return;
    }
    this.props.router.push('/authorize/success');
  };

  public render() {
    const { depositNum, depositLoading, shouldPay } = this.state;
    return (
      <BlockLoading loading={depositLoading}>
        <div className="cross-border-authorize-deposit">
          <Card className="deposit-card">
            <p>当前您的跨境服务保证金余额：</p>
            <p className="deposit-num-line">
              ￥<span className="deposit-num">{formatMoney(depositNum, true, false)}</span>
            </p>
          </Card>
          <p className="deposit-should-pay">
            为了保证消费者体验，有赞对跨境服务商家收取保证金，应缴纳金额：
            <span className="should-pay-num">¥{formatMoney(shouldPay, true, false)}</span>
          </p>
          <p className="deposit-hint">
            有赞跨境商家经营必须缴纳保证金，保证金主要用于保证商家按照有赞的规范进行经营，并且在商家有违规行为时根据《有赞跨境服务商家服务协议》及相关规则规定用于向有赞及消费者支付违约金。
          </p>
          <div className="deposit-actions">
            {depositNum < shouldPay ? (
              <Button type="primary" onClick={this.openCashierDialog}>
                立即缴纳
              </Button>
            ) : (
              <Button type="primary" onClick={this.enterService}>
                {isShowCrossBorderSettlement ? '下一步' : '立即入驻'}
              </Button>
            )}
          </div>
        </div>
      </BlockLoading>
    );
  }
}

export default withRouter<{}>(AuthDeposit);
