import React, { PureComponent } from 'react';
import { withRouter, WithRouterProps } from 'react-router';
import { Icon, Checkbox, Button, Notify, BlockLoading, Dialog } from 'zent';
import * as api from '../../../../api';
import './style.scss';
import { newProcess } from 'pages/cross-border/constants/global';
import { setUrlDomain } from '@youzan/retail-utils';

import { handlePublish } from '../../../overview';

type IProps = WithRouterProps;
const { openDialog } = Dialog;
class AuthSuccess extends PureComponent<IProps, {}> {
  public state = {
    secured: false,
    agree: true,
    successLoading: false,
    crossBorderSettlementMessage: '',
  };

  /**
   * 跳转到概况页
   */
  private goToOverview = () => {
    ZanSpa.navigateTo('/v4/trade/cross-border');
  };

  public componentDidMount() {
    this.getAuthCondition();
    this.getSuccessMessage();
  }

  getSuccessMessage() {
    // 获取hash路由的参数
    const { location } = this.props;

    // 解析hash路由中的查询参数
    let isFromCrossBorderSettlement = 0;

    // 如果是从路由中获取参数
    if (location.query && location.query.isFromCrossBorderSettlement) {
      isFromCrossBorderSettlement = Number(location.query.isFromCrossBorderSettlement);
    }

    const messages = {
      1: '当前店铺的跨境结算服务预计需要1-2个工作日审核，您可前往店铺后台-资产-资产总览-跨境资金钱包查看开通结果，如有疑问请联系有赞客户成功经理或者在线客服热线0571-89988848。',
      2: '当前店铺尚未开通有赞跨境结算服务，这将影响资金出境，请您尽快开通。开通路径：店铺后台-资产-资产总览-跨境资金钱包发起申请，如有疑问请联系有赞客户成功经理或者在线客服热线0571-89988848。',
    };

    // 根据不同类型，展示不同文案
    if (isFromCrossBorderSettlement && messages[isFromCrossBorderSettlement]) {
      this.setState({ crossBorderSettlementMessage: messages[isFromCrossBorderSettlement] });
    }
  }

  private getAuthCondition() {
    this.setState({ successLoading: true });
    api
      .getAuthCondition()
      .then((res) => {
        this.setState({
          secured: res.secured,
        });
      })
      .finally(() => {
        this.setState({ successLoading: false });
      });
  }

  private changeAgree = (e: any) => {
    this.setState({ agree: e.target.checked });
  };

  private confirmOrder = () => {
    const callback = () => {
      // eslint-disable-next-line @youzan/location-check/location-check
      window.location.href = setUrlDomain('/v4/assets/security/guarantee', 'base');
    };

    api
      .upgradeNormal()
      .then(() => {
        Notify.success('恭喜你，已成功加入有赞放心购', 3000, callback);
      })
      .catch(() => {
        Notify.error('申请失败，请重试', 3000);
      });
  };

  private openIntroDialog() {
    openDialog({
      title: '有赞放心购',
      children: (
        <div>
          <img src="https://img01.yzcdn.cn/upload_files/2024/03/05/FjglNxaykm9Dahnbuqeb4cAt2pS_.png" />
        </div>
      ),
    });
  }

  public render() {
    const { secured, agree, successLoading, crossBorderSettlementMessage } = this.state;

    return (
      <BlockLoading loading={successLoading}>
        <div className="cross-border-authorize-success">
          <Icon type="check-circle" className="success__icon" />
          <h3 className="success__title">入驻成功，跨境订单免费享受有赞放心购服务</h3>
          <p className="success_tips">
            {crossBorderSettlementMessage ? (
              crossBorderSettlementMessage
            ) : (
              <>
                有赞将会每月两次向国家外汇管理局申报跨境订单，申报通过后商家可以将跨境资金提现到境外账户。为了满足合规要求，请您联系有赞的客户成功经理或者在线客服或热线0571-89988848提交银行备案资料。具体资料内容请参考：
                <a
                  className="success__to-overview"
                  href="https://bbs.youzan.com/thread-675362-1-1.html"
                >
                  BBS点击这里
                </a>
                。在备案成功之后，才可以操作跨境提现。
                {!secured && newProcess ? (
                  <a className="success__to-overview" onClick={this.goToOverview}>
                    立即前往有赞跨境服务主页
                  </a>
                ) : null}
              </>
            )}
          </p>
          {!secured && newProcess ? null : (
            <p className="success__desc">
              <a className="success__to-overview" onClick={this.goToOverview}>
                立即前往有赞跨境服务主页
              </a>
            </p>
          )}
          {_global.haitaoEditSetting && (
            <p className="success__to-publish">
              跨境商品专属商详需发布小程序才可生效。{' '}
              <span onClick={handlePublish}>一键发布小程序</span>
            </p>
          )}
          {!secured && newProcess ? (
            <>
              <div className="condition__agree">
                <Checkbox
                  className="condition__agree-checkbox"
                  checked={agree}
                  onChange={this.changeAgree}
                />
                非跨境订单开通有赞放心购&nbsp;&nbsp;
                <b>0.5%服务费/单</b>&nbsp;<a onClick={this.openIntroDialog}>进一步了解</a>
              </div>
              <div className="cofirmBtn_warp">
                <Button type="primary" disabled={!agree} onClick={this.confirmOrder}>
                  确认开通
                </Button>
              </div>
            </>
          ) : null}
        </div>
      </BlockLoading>
    );
  }
}

export default withRouter<{}>(AuthSuccess);
