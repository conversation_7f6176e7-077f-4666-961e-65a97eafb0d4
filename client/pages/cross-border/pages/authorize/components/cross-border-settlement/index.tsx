import React, { PureComponent } from 'react';
import { withRouter, WithRouterProps } from 'react-router';
import { Button, Sweetalert } from 'zent';

import {
  isCrossBorderSettlementApplying,
  isCrossBorderSettlementOpened,
  isCrossBorderSettlementNotOpen,
} from '../../../../constants/global';

import './style.scss';

type IProps = WithRouterProps;

class CrossBorderSettlement extends PureComponent<IProps, {}> {
  private next = () => {
    // 如果跨境结算状态为“申请中”，则跳转资产总览页
    if (isCrossBorderSettlementApplying) {
      window.open('https://www.youzan.com/v4/assets/dashboard', '_blank');
      return;
    }
    // 如果跨境结算状态为“已开通”，则直接跳转到第四步
    if (isCrossBorderSettlementOpened) {
      this.props.router.push('/authorize/success');
      return;
    }

    // 未开通
    // 打开跨境结算开通页面（这里使用window.open在新标签页打开，您可以根据实际情况修改URL）
    window.open('https://www.youzan.com/v4/cert/cross-settlement/intro', '_blank');

    // 显示确认弹窗
    Sweetalert.confirm({
      title: '确认开通状态',
      content: '您是否已完成跨境结算开通？',
      confirmText: '已开通',
      cancelText: '未开通',
      onConfirm: () => {
        // 如选择“已开通”，则跳转到第四步
        this.props.router.push('/authorize/success?isFromCrossBorderSettlement=1');
      },
      onCancel: () => {
        // 如选择“未开通”，则关闭弹窗，停留在原页面
        // 弹窗会自动关闭，无需额外操作
      },
    });
  };

  /**
   * 跳过开通，直接进入跨境服务
   */
  private skipToService = () => {
    this.props.router.push('/authorize/success?isFromCrossBorderSettlement=2');
  };

  private renderButtonText() {
    if (isCrossBorderSettlementApplying) {
      return '跨境结算开通中，查看进度';
    }
    if (isCrossBorderSettlementOpened) {
      return '您已开通跨境结算，下一步';
    }
    return '申请开通跨境结算';
  }

  public render() {
    return (
      <div className="cross-border-authorize-cross-border-settlement">
        <div className="cross-border-settlement-info">
          <h2 className="cross-border-settlement-title">开通有赞跨境结算，让经营更合规</h2>
          <p className="cross-border-settlement-subtitle">
            按照法律法规要求，跨境商家需要开通跨境结算，确保资金出境
          </p>

          <div className="cross-border-settlement-advantages">
            <h3 className="advantages-title">有赞跨境结算具备如下优势：</h3>
            <ul className="advantages-list">
              <li>
                <span className="bullet">•</span> 线上自主提交开通，审批快，效率高
              </li>
              <li>
                <span className="bullet">•</span> 无忧提现，快速到账
              </li>
              <li>
                <span className="bullet">•</span> 支持全球主流货币
              </li>
            </ul>
          </div>
        </div>

        <div className="cross-border-settlement-actions">
          <Button type="primary" onClick={this.next} className="apply-button">
            {this.renderButtonText()}
          </Button> 
          {/* 未开通的情况才展示 */}
          {isCrossBorderSettlementNotOpen && (
            <p className="skip-link" onClick={this.skipToService}>
              暂不开通，直接进入跨境服务
            </p>
          )}
        </div>
      </div>
    );
  }
}

export default withRouter<{}>(CrossBorderSettlement);
