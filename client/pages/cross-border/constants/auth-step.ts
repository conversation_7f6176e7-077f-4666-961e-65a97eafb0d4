import AuthCondition from '../pages/authorize/components/condition';
import AuthDeposit from '../pages/authorize/components/deposit';
import AuthSuccess from '../pages/authorize/components/success';
import CrossBorderSettlement from '../pages/authorize/components/cross-border-settlement';

export type IAuthSteps = 'condition' | 'deposit' | 'crossBorderSettlement' | 'success';

export const AUTH_STEP_INDEX_MAP: { [key in IAuthSteps]: number } = {
  condition: 1,
  deposit: 2,
  crossBorderSettlement: 3,
  success: 4,
};

export const AUTH_STEP_TITLE_MAP: { [key in IAuthSteps]: string } = {
  condition: '入驻条件',
  deposit: '缴纳保证金',
  crossBorderSettlement: '开通跨境结算',
  success: '入驻完成',
};

export const AUTH_STEP_COMPONENT_MAP: { [key in IAuthSteps]: React.ComponentClass<{}> } = {
  condition: AuthCondition,
  deposit: AuthDeposit,
  crossBorderSettlement: CrossBorderSettlement,
  success: AuthSuccess,
};
