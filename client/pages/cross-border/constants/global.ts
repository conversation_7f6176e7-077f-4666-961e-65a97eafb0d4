import { IServiceStatus, IElectronicPort } from 'definitions/cross-border';

interface ICrossBorderServiceGlobal extends IWscPcTradeGlobal {
  serviceStatus: IServiceStatus;
  electronicPortList: IElectronicPort[];
}

export const { serviceStatus } = _global as ICrossBorderServiceGlobal;
export const { electronicPortList } = _global as ICrossBorderServiceGlobal;
// 跨境入驻新流程
export const { newProcess } = _global;

/**
 * 三种情况需要展示概况页，
 * 1. 入驻开关打开
 * 2. 入驻开关关闭，处于正常退出流程中（isApplyingQuit 为 true）
 * 3. 入驻开关关闭，但是是异常退出，需要重新认证
 */
export const showOverview =
  serviceStatus.switchOpen === true ||
  (serviceStatus.switchOpen === false && serviceStatus.isApplyingQuit === true) ||
  (serviceStatus.switchOpen === false &&
    serviceStatus.isApplyingQuit === false &&
    serviceStatus.isCertReject === true);

// @ts-ignore
const { crossBorderSettlement } = _global || {};
const { certStatus, settleChannel, lastCertStatus } = crossBorderSettlement || {};

// settleChannel：HKCCB香港建行、BOCOM交通银行浙分
// 只有查询出来是交行才需要跳转到跨境结算开通环节
export const isShowCrossBorderSettlement = settleChannel === 'BOCOM';

/**
 * 跨境服务认证状态枚举
 */
export enum CrossBorderCertStatus {
  // 跨境服务未认证，暂不支持开通
  UNSUPPORT = -1,
  // 未开通
  NOT_APPLY,
  // 审核中
  WAIT_CONFIRM,
  // 审核通过
  PASS,
  // 审核不通过
  REJECT,
  // 过期失效
  INVALID,

}

// 判断是否需要开通的状态：未认证、未开通、开通失败、过期失效  
export const isCrossBorderSettlementNotOpen =
  certStatus === CrossBorderCertStatus.UNSUPPORT ||
  certStatus === CrossBorderCertStatus.NOT_APPLY ||
  certStatus === CrossBorderCertStatus.REJECT ||
  certStatus === CrossBorderCertStatus.INVALID;

// 跨境结算已经开通
export const isCrossBorderSettlementOpened =
  certStatus === CrossBorderCertStatus.PASS || lastCertStatus === CrossBorderCertStatus.PASS;

// 跨境结算开通中
export const isCrossBorderSettlementApplying = certStatus === CrossBorderCertStatus.WAIT_CONFIRM;
