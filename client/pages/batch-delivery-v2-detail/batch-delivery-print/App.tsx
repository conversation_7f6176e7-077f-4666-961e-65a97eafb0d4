import React, { useEffect, useMemo, useState } from 'react';
import { Button, Form, FormSelectField, FormStrategy, Grid, IGridColumn, Notify } from 'zent';
import queryString from '@youzan/utils/url/queryString';
import { getBatchDeliveryDetail } from './api';
import { openReprintDialog, closeReprintDialog } from 'components/reprint-dialog';
import { openBatchPrintTaskProgressDialog } from '@youzan/order-domain-pc-components/es/batch-express-delivery/batch-print-dialog/index';
import '@youzan/order-domain-pc-components/css/batch-express-delivery/index.css';
import { batchItemStatusOptions, batchItemStatusAllValue } from './constants';
import {
  BatchDeliveryPrintTaskItemStatus,
  BatchDeliveryPrinterTaskType,
  ExpressCompanyIdEnum,
  PaymentTypeEnum,
} from 'constants/express';
import PopContainer from 'components/electron-way-bill/pop-container';
import { handleBatchItemPrint } from '@youzan/order-domain-pc-components/es/batch-express-delivery/batch-print-dialog/batch-print-task';
import { isWscSingleStore, isRetailShop, isFrontWarehouse } from '@youzan/utils-shop';

const TaskDetail = () => {
  const form = Form.useForm(FormStrategy.View);
  const { batchNo } = queryString.parse(window.location.search.replace('?', ''));
  const [data, setData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);

  const reelectTemplate = useMemo(() => {
    const item = data[0];
    if (!item) {
      return true;
    }
    // 顺丰字结算返回 false
    if (
      item.paymentType === PaymentTypeEnum.oneself &&
      item.expressId === ExpressCompanyIdEnum.SF
    ) {
      return false;
    }
    return true;
  }, [data]);

  function formatData(data) {
    return data.map((item) => {
      return {
        ...item,
        id: item.batchItemNo,
      };
    });
  }

  function fetchDetail(filterOptions = {}) {
    setLoading(true);
    return getBatchDeliveryDetail({ batchNo, pageNum: 1, pageSize: 100, ...filterOptions })
      .then((res) => {
        setData(formatData(res.list));
      })
      .catch((err) => {
        Notify.error(err?.msg || err?.message || err);
      })
      .finally(() => setLoading(false));
  }

  function handleSearch() {
    const itemStatus = form.getValue().itemStatus?.key || '';
    const filterOptions: any = {};
    if (itemStatus !== batchItemStatusAllValue) {
      filterOptions.itemStatus = itemStatus;
    }

    fetchDetail(filterOptions);
  }

  function handleBatchPrint() {
    if (!selectedRowKeys.length) {
      return Notify.warn('请先选择订单');
    }
    const { expressId, paymentType } = data[0];
    openReprintDialog({
      expressId,
      paymentType,
      reelectTemplate,
      onConfirm(values: { printer: string; templateUrl?: string }) {
        closeReprintDialog();
        openBatchPrintTaskProgressDialog({
          title: '重打面单进度',
          type: BatchDeliveryPrinterTaskType.Reprint,
          ...values,
          batchNo,
          supportBatchItemNoList: selectedRowKeys,
          onClose() {
            fetchDetail();
          },
        });
      },
    });
  }

  function handleReprintByItem(item) {
    const { expressId, paymentType } = item;
    openReprintDialog({
      expressId,
      paymentType,
      reelectTemplate,
      onConfirm(values: { printer: string; templateUrl?: string }) {
        closeReprintDialog();
        const { batchItemNo } = item;
        const { printer, templateUrl } = values;
        handleBatchItemPrint({
          type: BatchDeliveryPrinterTaskType.Reprint,
          batchNo,
          batchItemNo,
          printer,
          templateUrl,
        }).then((res) => {
          if (res.success) {
            Notify.success('重打面单成功');
            fetchDetail();
          } else {
            Notify.error(res.message || '重打面单失败');
          }
        });
      },
    });
  }

  useEffect(() => {
    fetchDetail();
  }, []);

  const columns: IGridColumn[] = [
    {
      title: '订单号',
      name: 'orderNo',
      bodyRender({ orderNo }) {
        // 前置仓不能跳转到订单详情页面，直接显示订单号
        if (isFrontWarehouse) {
          return orderNo;
        }
        const href = isRetailShop
          ? `/v2/order/orderdetail#/?order_no=${orderNo}`
          : `/v4/trade/order/detail?orderNo=${orderNo}`;
        return (
          <a target="_blank" rel="noreferrer" href={href}>
            {orderNo}
          </a>
        );
      },
    },
    ...(!isWscSingleStore ? [{
      title: '发货单号',
      name: 'fulfillNo',
      bodyRender({ fulfillNo }) {
        return (
          <a
            href={`/v2/order/order-shipment-detail#/?fulfillNo=${fulfillNo}`}
            target="_blank"
            rel="noreferrer"
          >
            {fulfillNo}
          </a>
        );
      }
    }] : []),
    {
      title: '物流公司',
      name: 'expressName',
    },
    {
      title: '运单号',
      name: 'expressNo',
    },
    {
      title: '状态',
      name: 'deliveryStatus',
      bodyRender({ deliveryStatus, errorMsg }) {
        if (deliveryStatus === BatchDeliveryPrintTaskItemStatus.Process) {
          return '进行中';
        }
        const statusItem = batchItemStatusOptions.find((item) => item.value === deliveryStatus);
        if (!statusItem) {
          return null;
        }
        return (
          <div>
            <span className={['delivery-status', statusItem.statusType || ''].join(' ')}>
              {statusItem.text}
            </span>
            {errorMsg ? (
              <PopContainer
                icon="help-circle"
                popContent={<div>{errorMsg}</div>}
                iconStyle={{ color: '#BFBFBF', marginLeft: 4 }}
              />
            ) : null}
          </div>
        );
      },
    },
    {
      title: '操作',
      textAlign: 'right',
      bodyRender(item) {
        // 无物流单号，无法打印，直接 return
        if (!item.expressNo) {
          return null;
        }
        return (
          <span className="text-button" onClick={() => handleReprintByItem(item)}>
            重打面单
          </span>
        );
      },
    },
  ];

  const batchItemStatusOptionsV2 = useMemo(() => {
    return batchItemStatusOptions.map((item) => {
      return {
        ...item,
        key: item.value,
      };
    });
  }, [batchItemStatusOptions]);
  const batchItemStatusAllValueItem = useMemo(() => {
    return batchItemStatusOptionsV2.find((item) => item.value === batchItemStatusAllValue);
  }, [batchItemStatusAllValue]);

  return (
    <div className="batch-delivery-print-detail">
      <Form layout="horizontal" form={form} className="filter-form">
        <FormSelectField
          name="itemStatus"
          label="状态："
          defaultValue={batchItemStatusAllValueItem}
          props={{
            options: batchItemStatusOptionsV2,
          }}
        />
        <div className="filter-btn">
          <Button onClick={handleSearch} type="primary" loading={loading}>
            筛选
          </Button>
        </div>
      </Form>
      <div className="table-control-wrap">
        <span>已选中 {selectedRowKeys.length}项</span>
        <Button onClick={handleBatchPrint} className="batch-reprint-btn">
          批量重打面单
        </Button>
      </div>
      <Grid
        datasets={data}
        columns={columns}
        selection={{
          selectedRowKeys,
          onSelect: (nextSelectedRowKeys) => {
            setSelectedRowKeys(nextSelectedRowKeys);
          },
          getSelectionProps: (data) => ({
            disabled: !data.expressNo,
          }),
        }}
      />
    </div>
  );
};

export default TaskDetail;
