import React, { Component, MutableRefObject } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Notify, Pop } from 'zent';
import { Form } from '@zent/compat';
import { BlankLink } from '@youzan/react-components';
import accDiv from '@youzan/utils/number/accDiv';
import DatePickerField from 'components/form-field/date-picker-field';
import AsyncSelctorField from 'components/async-selector';
import { generateSelectData, isOrderNo } from '../../utils';
import { showRechargeDialog } from '../recharge-dialog';
import { openExportDialog } from '../new-export-dialog';
import YZLocalStorage from 'zan-utils/browser/local_storage';
import { NAME_MAP } from '../../config';
import api from '../../api';
import { sixtyMinsChooseDay } from './helper';
import { isEduChainStore, isEduHqStoreV4, isWscHqStore } from '@youzan/utils-shop';
import { CloudSlot } from '@youzan/ranta-cloud-react';
import { isOfflineAdmin, isQttShop, storeId as globalStoreId } from 'constants/role';
import withBranchShopSelector, {
  IWithBranchShopSelectorProps,
} from 'components/order/chain-branch-store-selector';
import { isEduBasicVersion } from 'components/check-version';
import {
  IOrderSearchQuery,
  IStoreListItem,
  ITimeChangeOptions,
  IOrderSourceItem,
} from '../../types';
import { IS_WHOLESALE } from '../../constants';
import searchStoreList, { ISelectItem } from 'fns/search-store-list';
import { debounce, unionWith, isEqual, omit, find, get } from 'lodash';
import { getTextFromVal } from 'fns/get-text-from-val';
import './index.scss';
import { IOrderOftenSearchConfigDTO } from 'definitions/api/service/search/config/OrderSearchConfigService';
import { AggregateSearch, OrderOftenSearchConfig } from './components';
import { renderToString } from 'react-dom/server';
import { SatisfactionDialog } from '@youzan/react-components';
import { startOfToday, endOfToday, subYears } from 'date-fns';

const { reportActions } = SatisfactionDialog;
const ORDER_EXPORT_CES_NAME = 'wsc_order_export_ces';
React.useLayoutEffect = React.useEffect;

interface IProps extends IWithBranchShopSelectorProps, ZENTFORM<{}>, IZentCompatFormProps {
  filterOptions: IOrderSearchQuery;
  getFilterParams: (filterOptions: IOrderSearchQuery) => IOrderSearchQuery;
  onFilterChange: (name: string | { [key: string]: any }, value?: any) => void;
  onFilterConfirm: (filterOptions: IOrderSearchQuery) => void;
  loading: boolean;
  showMultiStore: boolean;
  storeList: IStoreListItem[];
  handleSearchMethodChange: (t: string) => void;
  handleClearFilterOptions: Function;
  onBeforeFilterReset: Function;
}

interface IState {
  chooseDays: number;
  deliveryTimeChooseDays: number;
  orderSourceList: IOrderSourceItem[];
  subShopList: ISelectItem[];
  allShops: ISelectItem[];
  storeKeyword: string;
  showMoreFilters: boolean;
  showOrderExportCes: boolean;
}

const { createForm, FormInputField, FormSelectField, FormDatePickerField } = Form;
const userId = window._global.userId;

class Filter extends Component<IProps, IState> {
  orderOftenSearchConfigRef = React.createRef<HTMLDivElement>();

  constructor(props) {
    super(props);
    // showMoreFilters默认为true
    const cacheMoreValue = YZLocalStorage.getItem(`get_AggregateSearch_${userId}`);
    const showMoreFilters = cacheMoreValue !== null ? JSON.parse(cacheMoreValue) : true;
    this.state = {
      chooseDays: -1,
      deliveryTimeChooseDays: -1,
      orderSourceList: [{ desc: '全部', code: 'all' }],
      subShopList: [{ text: '全部', value: 0 }],
      allShops: [{ text: '全部', value: 0 }],
      storeKeyword: '',
      showMoreFilters,
      showOrderExportCes: false,
    };
  }

  componentDidMount(): void {
    this.searchStores('');
    // 群团团店铺覆盖订单来源字段，后端是取的Apollo配置不好改
    if (isQttShop) {
      this.setState({
        orderSourceList: [
          { desc: '全部', code: 'all' },
          { desc: '群团团订单', code: 'fx_zpp_order' },
        ],
      });
      return;
    }
    api
      .getOrderSourceList()
      .then((orderSourceList) => {
        let list = orderSourceList ? [...orderSourceList] : [];
        if (!_global.abcEPay) {
          // 不在农行白名单，订单来源过滤农行掌银App
          list = list.filter((item) => item.code !== 'bankabc');
        }
        this.setState({
          orderSourceList: [{ desc: '全部', code: 'all' }, ...list],
        });
      })
      .catch((err) => {
        Notify.error(err);
      });
  }

  getOrderOftenSearchConfigDetail(
    config: IOrderOftenSearchConfigDTO
  ): Array<{ label: string; key: string; value: string | null }> {
    const { storeList } = this.props;
    const { orderSourceList, subShopList } = this.state;

    const store = find(storeList, { id: config?.keyword?.storeId });
    const subShop: any = find(subShopList, { value: config?.subShopKdtId });

    const orderSource = find(orderSourceList, { code: config?.orderSource }) || {};
    const timeTypeVal =
      config?.keyword?.startTime && config?.keyword?.endTime
        ? `${config?.keyword?.startTime} 至 ${config?.keyword?.endTime}`
        : null;

    const result = [
      { label: '聚合搜索', key: 'aggregateSearchText', value: config?.aggregateSearchText },
      {
        label: '订单搜索',
        key: 'order_label',
        value: get(config, `keyword.${config?.orderLabel}`),
      },
      { label: '时间搜索', key: 'timeType', value: timeTypeVal },
      {
        label: '订单状态',
        key: 'state',
        value: get(NAME_MAP, `state.${config?.type}.${config?.state}`),
      },
      { label: '订单类型', key: 'type', value: get(NAME_MAP, `type.${config?.type}`) },
      {
        label: '推广方式',
        key: 'marketing_channel',
        value: get(NAME_MAP, `marketing_channel.${config?.marketingChannel}`, null),
      },
      {
        label: '售后状态',
        key: 'feedback',
        value: get(NAME_MAP, `state.feedback.${config?.feedback}`, null),
      },
      {
        label: '配送方式',
        key: 'express_type',
        value: get(NAME_MAP, `express_type.${config?.expressType}`, null),
      },
      { label: '订单来源', key: 'orderSource', value: orderSource?.desc },
      {
        label: '付款方式',
        key: 'buy_way',
        value: get(NAME_MAP, `buy_way.${config?.buyWay}`, null),
      },
      { label: '归属网点', key: 'store_id', value: store?.name },
      {
        label: _global.isYZEdu ? '所属校区' : '网店',
        key: 'sub_shop_kdt_id',
        value: subShop?.text,
      },
      {
        label: '送达日期',
        key: 'period_send_time',
        value: get(config, 'keyword.periodSendTime', null),
      },
      { label: '送达时间', key: 'book_time', value: undefined },
    ];

    return result.filter((item) => !!item.value);
  }

  componentDidUpdate(_, prevState: IState) {
    if (this.state.storeKeyword !== prevState.storeKeyword) {
      this.searchStores(this.state.storeKeyword);
    }
  }

  setStoreKeyword = (storeKeyword) => {
    this.setState({ storeKeyword });
  };

  searchStores = debounce((storeKeyword: string = '') => {
    searchStoreList(storeKeyword)
      .then((subShopList) => {
        const { allShops } = this.state;
        this.setState({
          subShopList,
          allShops: unionWith(allShops, subShopList, isEqual),
        });
      })
      .finally(() => {
        const { subShopList, allShops } = this.state;
        const opts = this.props.filterOptions;
        let defaultOptions: ISelectItem[] = [];
        if (opts.sub_shop_name && opts.sub_shop_kdt_id) {
          defaultOptions = [
            { text: '全部', value: 0 },
            { text: opts.sub_shop_name, value: Number(opts.sub_shop_kdt_id) },
          ];
        }
        this.setState({
          subShopList: unionWith(defaultOptions, subShopList, isEqual),
          allShops: unionWith(defaultOptions, allShops, isEqual),
        });
      });
  }, 500);

  checkStartAndEndTime = (filterOptions: IOrderSearchQuery) => {
    if (filterOptions.state === 'delivery_over_time') {
      return true;
    }
    if (!filterOptions.start_time || !filterOptions.end_time) {
      Notify.error('请选择起止时间');
      return false;
    }
    const startTime = new Date(filterOptions.start_time.replace('-', '/')).getTime();
    const endTime = new Date(filterOptions.end_time.replace('-', '/')).getTime();
    const DURATION = 12 * 31 * 24 * 60 * 60 * 1000;
    if (Math.abs(startTime - endTime) > DURATION) {
      Notify.error('日期间隔不能大于 12 个月');
      return false;
    }
    return true;
  };

  handleExportClick = () => {
    const { allShops } = this.state;
    const { filterOptions, getFilterParams } = this.props;
    const optionMaps = { subShopList: allShops };
    if (
      isOrderNo(filterOptions?.aggregateSearchText) ||
      this.checkStartAndEndTime(this.props.filterOptions)
    ) {
      api
        .getAccountBalance()
        .then((data) => {
          const { available } = data;
          // 把单位转为元
          const availableYuan = accDiv(available, 100);
          if (availableYuan < 0) {
            return showRechargeDialog(availableYuan);
          }
          this.setState({
            showOrderExportCes: false,
          });
          openExportDialog({
            orderSourceList: this.state.orderSourceList,
            filterOptions,
            getFilterParams,
            optionMaps,
            callback: () => {
              reportActions({ rule: ORDER_EXPORT_CES_NAME }).then(() => {
                this.setState({
                  showOrderExportCes: true,
                });
              });
            },
          });
        })
        .catch((msg) => Notify.error(msg));
    }
  };

  // 重置表单
  resetForm = () => {
    this.props.zentForm.resetFieldsValue();
  };

  // 快速时间选择
  handleChangeDatePicker = ({ value, chooseDays }: ITimeChangeOptions) => {
    const { onFilterChange } = this.props;
    onFilterChange({
      // eslint-disable-next-line @typescript-eslint/camelcase
      start_time: value[0],
      // eslint-disable-next-line @typescript-eslint/camelcase
      end_time: value[1],
    });
    this.setState({
      chooseDays,
    });
  };

  // 同城送快速时间选择
  handleChangeDeliveryDatePicker = ({ value, chooseDays }: ITimeChangeOptions) => {
    const { onFilterChange } = this.props;
    onFilterChange({
      // eslint-disable-next-line @typescript-eslint/camelcase
      delivery_start_time: value[0],
      // eslint-disable-next-line @typescript-eslint/camelcase
      delivery_end_time: value[1],
    });
    this.setState({
      deliveryTimeChooseDays: chooseDays,
    });
  };

  getStaffListOptions = (query: string, pageRequest: { current: number }) => {
    const getStaffList = isEduChainStore ? api.getChainShopStaffList : api.getSingleShopStaffList;
    // @ts-ignore
    return getStaffList({ keyword: query, pageNo: pageRequest.current }).then((data) => {
      const items = get(data, 'items');
      let options: Array<{ text: string; value: any }> = [];
      if (items && Array.isArray(items)) {
        options = items.map((item) => ({ text: item.name, value: item.adminId }));
      }

      return {
        options,
        pageInfo: {
          current: get(data, 'paginator.page'),
          total: get(data, 'paginator.totalCount'),
        },
      };
    });
  };

  renderSalerSelector = () => {
    const { filterOptions, onFilterChange } = this.props;
    return (
      <AsyncSelctorField
        filter
        mode="async"
        fetchOnMounted
        label="课程顾问："
        name="seller_id"
        placeholder="全部"
        throttleConf={{
          wait: 350,
        }}
        value={filterOptions.seller_id}
        fetchOptions={this.getStaffListOptions}
        // 添加默认选项，帮助组件回填
        defaultOptions={[{ text: filterOptions.seller_name, value: filterOptions.seller_id }]}
        onSelect={(value, selectedOpts) => {
          const selectedOne = selectedOpts?.filter((opt) => opt.value === value)[0];
          onFilterChange('seller_id', selectedOne);
        }}
      />
    );
  };

  renderCashierSelector = () => {
    const { filterOptions, onFilterChange } = this.props;
    return (
      <AsyncSelctorField
        filter
        mode="async"
        fetchOnMounted
        label="收银员："
        name="cashier_id"
        placeholder="全部"
        throttleConf={{
          wait: 350,
        }}
        value={filterOptions.cashier_id}
        fetchOptions={this.getStaffListOptions}
        // 添加默认选项，帮助组件回填
        defaultOptions={[{ text: filterOptions.cashier_name, value: filterOptions.cashier_id }]}
        onSelect={(value, selectedOpts) => {
          const selectedOne = selectedOpts?.filter((opt) => opt.value === value)[0];
          onFilterChange('cashier_id', selectedOne);
        }}
      />
    );
  };

  renderEduAdditionalFilters() {
    const { filterOptions, onFilterChange } = this.props;

    return (
      <>
        <div className="filter-item">
          <div className="filter-item__field">
            <FormSelectField
              name="eduSignUpStatus"
              label="报名状态："
              placeholder="全部"
              data={generateSelectData(NAME_MAP.edu_signup_label)}
              value={filterOptions.eduSignUpStatus}
              onChange={(value) => {
                if (value === 'all') {
                  onFilterChange({
                    eduSignUpStatus: '',
                    // eslint-disable-next-line @typescript-eslint/camelcase
                    order_source: '',
                    type: '',
                  });
                  return;
                }
                onFilterChange({
                  eduSignUpStatus: value,
                  // eslint-disable-next-line @typescript-eslint/camelcase
                  order_source: 'offline_sign',
                  type: 'knowledge',
                });
              }}
            />
            {this.renderSalerSelector()}
            {this.renderCashierSelector()}
          </div>
        </div>
        <div className="filter-item">
          <div className="filter-item__field">
            <FormSelectField
              name="eduSignUpType"
              label="报名类型："
              value={filterOptions.eduSignUpType}
              data={generateSelectData(NAME_MAP.eduSignUpType)}
              onChange={(value) => {
                if (value && value !== '0') {
                  onFilterChange('eduSignUpType', value);
                } else {
                  onFilterChange('eduSignUpType', '');
                }
              }}
              placeholder="全部"
            />
          </div>
        </div>
      </>
    );
  }

  renderFields = () => {
    const { chooseDays, deliveryTimeChooseDays, subShopList, allShops, showMoreFilters } =
      this.state;
    if (!showMoreFilters) {
      return;
    }
    const { filterOptions, onFilterChange, storeList, showMultiStore } = this.props;

    const isCityOrder = filterOptions.express_type === 'city';
    const isSchedule = filterOptions.state === 'schedule';
    // 同城订单 && 选中了定时达 tab
    const showCityExpressDatePickerField = isCityOrder && isSchedule;

    // 同城送订单在交易是 express_type 物流方式这个维度来判断的
    // 以前同城送订单没有对应的订单状态，现在要新增对应的订单状态
    // 如果 express_type === 'city'，就显示同城订单的状态
    const stateType =
      filterOptions.express_type === 'city' ? 'city' : (filterOptions.type as string);
    const stateSelectData = generateSelectData(NAME_MAP.state[stateType] || NAME_MAP.state.all);
    const isQttTitleLabel = filterOptions.order_label === 'qtt_note_title';

    return (
      <div className="filter-fields-wrap">
        <div className="filter-item">
          <div className="filter-item__field">
            <FormSelectField
              label="订单搜索："
              className="order-label"
              name="order_label"
              data={generateSelectData(NAME_MAP.order_label)}
              value={filterOptions.order_label}
              onChange={(value) => onFilterChange('order_label', value)}
            />
            <FormInputField
              className="search-value"
              name="keyword"
              value={filterOptions[filterOptions.order_label as string]}
              onChange={(e) => onFilterChange([filterOptions.order_label], e.target.value)}
              placeholder="请输入"
              maxLength={isQttTitleLabel ? 50 : null}
            />
            {showMultiStore && (
              <FormSelectField
                disabled={isOfflineAdmin}
                name="store_id"
                label="归属网点："
                data={[{ id: '', name: '全部网点' }, ...storeList]}
                onChange={(value) => onFilterChange('store_id', String(value))}
                optionValue="id"
                optionText="name"
                value={isOfflineAdmin ? globalStoreId : filterOptions.store_id}
                filter={(item, keyword) => item.name.indexOf(keyword) > -1}
                placeholder="全部网点"
              />
            )}
          </div>
        </div>

        <div className="filter-item">
          <div className="filter-item__field">
            <FormSelectField
              label="时间搜索："
              className="time-label"
              name="timeType"
              data={generateSelectData(NAME_MAP.timeType)}
              value={filterOptions.timeType}
              onChange={(value) => {
                window.Logger &&
                  window.Logger.log({
                    et: 'click', // 事件类型
                    ei: 'time_search_click', // 事件标识
                    en: '时间搜索', // 事件名称
                    pt: 'orderlist', // 页面类型
                    params: {
                      filter: NAME_MAP.timeType[value],
                      kdtid: window._global.kdtId,
                    }, // 事件参数
                  });
                onFilterChange('timeType', value);
              }}
            />
            {/*
            //@ts-ignore */}
            <DatePickerField
              className="time-value"
              name="book_time"
              value={[filterOptions.start_time, filterOptions.end_time]}
              chooseDays={chooseDays}
              handleChange={this.handleChangeDatePicker}
              dateFormat="YYYY-MM-DD HH:mm:ss"
              defaultTime={['00:00:00', '23:59:59']}
              width={184}
              preset={[
                {
                  text: '今天',
                  value: 0,
                },
                {
                  text: '昨天',
                  value: 1,
                },
                {
                  text: '近7天',
                  value: 7,
                },
                {
                  text: '近30天',
                  value: 30,
                },
                {
                  text: '最近1年',
                  value: [subYears(startOfToday(), 1), endOfToday()],
                },
              ]}
            />
          </div>
        </div>

        <div className="filter-item">
          <div className="filter-item__field">
            <FormInputField
              className="inline"
              name="goods_title"
              label="商品名称："
              value={filterOptions.goods_title}
              onChange={(e) => onFilterChange('goods_title', e.target.value)}
              placeholder="请输入"
            />
            <FormSelectField
              className="inline"
              name="state"
              label="订单状态："
              value={filterOptions.state}
              data={stateSelectData.map(({ value, text }) => {
                if (value === 'delivery_over_time') {
                  return {
                    value,
                    text: (
                      <div>
                        <span>{text}</span>
                        <Pop
                          trigger="hover"
                          position="right-center"
                          content="支付后超过30天还未发货的非预售订单"
                        >
                          <Icon className="help-wrapper" type="help-circle" />
                        </Pop>
                      </div>
                    ),
                  };
                }
                return {
                  value,
                  text,
                };
              })}
              onChange={(value) => onFilterChange('state', value)}
              placeholder="全部"
            />
            {/* 批发场景 筛选项隐藏订单类型 */}
            {!IS_WHOLESALE && (
              <FormSelectField
                popupClassName="order-type-select-popup"
                name="type"
                label="订单类型："
                value={filterOptions.type}
                data={generateSelectData(
                  !IS_WHOLESALE ? omit(NAME_MAP.type, ['wholesale']) : NAME_MAP.type
                ).map(({ value, text }) => {
                  if (value === 'self') {
                    return {
                      value,
                      text: (
                        <div>
                          <span>{text}</span>
                          <Pop
                            trigger="hover"
                            position="right-center"
                            content="除分销买家订单以外的所有订单"
                          >
                            <Icon className="help-wrapper" type="help-circle" />
                          </Pop>
                        </div>
                      ),
                    };
                  }
                  return {
                    value,
                    text,
                  };
                })}
                onChange={(value) => onFilterChange('type', value)}
                placeholder="全部"
                filter={(item, keyword) => {
                  if (React.isValidElement(item.text)) {
                    return renderToString(item.text).indexOf(keyword) > -1;
                  }
                  return item.text.indexOf(keyword) > -1;
                }}
              />
            )}
            {!isQttShop && (
              <FormSelectField
                className="inline"
                name="marketing_channel"
                label="推广方式："
                value={filterOptions.marketing_channel}
                data={generateSelectData(NAME_MAP.marketing_channel)}
                onChange={(value) => onFilterChange('marketing_channel', value)}
                placeholder="全部"
                filter={(item, keyword) => item.text.indexOf(keyword) > -1}
              />
            )}
            <FormSelectField
              name="feedback"
              label="售后状态："
              value={filterOptions.feedback}
              data={generateSelectData(NAME_MAP.state.feedback)}
              onChange={(value) => onFilterChange('feedback', value)}
              placeholder="全部"
            />
            <FormSelectField
              className="inline"
              name="express_type"
              label="配送方式："
              value={filterOptions.express_type}
              data={generateSelectData(NAME_MAP.express_type)}
              onChange={(value) => onFilterChange('express_type', value)}
              placeholder="全部"
            />
            {/* 批发场景 筛选项隐藏订单来源 */}
            {!IS_WHOLESALE && (
              <FormSelectField
                className="inline"
                name="order_source"
                label="订单来源："
                value={filterOptions.order_source}
                data={this.state.orderSourceList}
                optionText="desc"
                optionValue="code"
                onChange={(value) => onFilterChange('order_source', value)}
                placeholder="全部"
                filter={(item, keyword) => item.desc.indexOf(keyword) > -1}
              />
            )}
            {!isQttShop && (
              <FormSelectField
                className="inline"
                name="buy_way"
                label="付款方式："
                value={filterOptions.buy_way}
                data={generateSelectData(NAME_MAP.buy_way)}
                onChange={(value) => onFilterChange('buy_way', value)}
                placeholder="全部"
                filter={(item, keyword) => item.text.indexOf(keyword) > -1}
              />
            )}
            {(isEduHqStoreV4 || isWscHqStore) && (
              <FormSelectField
                name="sub_shop_kdt_id"
                label={_global.isYZEdu ? '所属校区：' : '网店：'}
                data={subShopList}
                onChange={(value) => {
                  onFilterChange('sub_shop_kdt_id', {
                    value: String(value),
                    text: getTextFromVal(allShops, Number(value)),
                  });
                }}
                value={filterOptions.sub_shop_kdt_id}
                onAsyncFilter={this.setStoreKeyword}
                placeholder="全部"
                onOpen={() => this.setStoreKeyword('')}
              />
            )}
            {filterOptions.type === 'period' && (
              <FormDatePickerField
                name="period_send_time"
                label="送达日期："
                onChange={(value) => onFilterChange('period_send_time', value)}
                value={filterOptions.period_send_time}
              />
            )}
          </div>
        </div>
        {showCityExpressDatePickerField && (
          <div className="filter-item">
            <div className="filter-item__field">
              {/*
              //@ts-ignore */}
              <DatePickerField
                name="book_time"
                label="送达时间："
                value={[filterOptions.delivery_start_time, filterOptions.delivery_end_time]}
                chooseDays={deliveryTimeChooseDays}
                handleChange={this.handleChangeDeliveryDatePicker}
                dateFormat="YYYY-MM-DD HH:mm:ss"
                preset={[
                  {
                    text: '60分钟内',
                    value: sixtyMinsChooseDay,
                  },
                  {
                    text: '今天',
                    value: 0,
                  },
                  {
                    text: '昨天',
                    value: 1,
                  },
                  {
                    text: '近30天',
                    value: 30,
                  },
                ]}
              />
            </div>
          </div>
        )}

        {_global.isYZEdu && !isEduBasicVersion() ? this.renderEduAdditionalFilters() : null}
      </div>
    );
  };

  beforeResetFilterOptions = () => {
    const { onBeforeFilterReset, handleClearFilterOptions } = this.props;
    onBeforeFilterReset().then(() => {
      handleClearFilterOptions();
    });
  };

  renderActions = () => {
    const { filterOptions } = this.props;
    const { showMoreFilters } = this.state;
    if (!showMoreFilters) {
      return;
    }

    return (
      <div className="form-actions">
        <Button type="primary" htmlType="submit" loading={this.props.loading}>
          筛选
        </Button>
        <Button type="default" onClick={this.beforeResetFilterOptions}>
          重置
        </Button>
        <Button type="default" disabled={!!filterOptions.tuanId} onClick={this.handleExportClick}>
          导出
        </Button>
        {!filterOptions.tuanId && (
          <BlankLink className="margin-left-16" href="/v4/trade/order/export-list">
            查看已导出列表
          </BlankLink>
        )}
      </div>
    );
  };

  handleAddOrderOftenSearchConfig = (filterOptions: IOrderSearchQuery): Promise<any> =>
    (this.orderOftenSearchConfigRef as MutableRefObject<any>)?.current?.addOrderOftenSearchConfig(
      filterOptions
    );

  render() {
    const {
      loading,
      handleSearchMethodChange,
      filterOptions,
      handleSubmit,
      onFilterConfirm,
      onFilterChange,
      getFilterParams,
    } = this.props;
    const { showMoreFilters } = this.state;
    return (
      <Form
        horizontal
        className="filter-container"
        disableEnterSubmit={false}
        onSubmit={handleSubmit(() => {
          handleSearchMethodChange('manual');
          onFilterConfirm(filterOptions);
        })}
      >
        <CloudSlot cloudKey="filter">
          <AggregateSearch
            showMoreFilters={showMoreFilters}
            loading={loading}
            filterOptions={filterOptions}
            onFilterChange={onFilterChange}
            onShowMoreFilters={(showMoreFilters: boolean) => {
              this.setState({ showMoreFilters });
              // localStorage以用户维度记录操作
              YZLocalStorage.setItem(`get_AggregateSearch_${userId}`, showMoreFilters);
              if (showMoreFilters) {
                window.Logger &&
                  window.Logger.log({
                    et: 'click', // 事件类型
                    ei: 'advanced_search', // 事件标识
                    en: '高级搜索点击', // 事件名称
                    pt: 'orderlist', // 页面类型
                    params: {
                      kdtid: window._global.kdtId,
                    }, // 事件参数
                  });
              }
            }}
          />
          <OrderOftenSearchConfig
            ref={this.orderOftenSearchConfigRef}
            storeList={this.props.storeList}
            subShopList={this.state.subShopList}
            orderSourceList={this.state.orderSourceList}
            filterOptions={filterOptions}
            getFilterParams={getFilterParams}
            onFilterChange={onFilterChange}
            onFilterConfirm={onFilterConfirm}
            handleSearchMethodChange={handleSearchMethodChange}
          />
          {this.renderFields()}
        </CloudSlot>
        {this.renderActions()}
        {this.state.showOrderExportCes && <SatisfactionDialog cesRule={ORDER_EXPORT_CES_NAME} />}
      </Form>
    );
  }
}

const WrappedFilter = withBranchShopSelector(createForm()(Filter));

export default WrappedFilter;
