import React, { useState } from 'react';
import { Button, Notify } from 'zent';
import { WaybillVersionEnum } from 'constants/express';
import {
  openBatchDeliveryDialog,
  closeBatchDeliveryDialog,
} from '@youzan/order-domain-pc-components/es/batch-express-delivery';
import '@youzan/order-domain-pc-components/css/index.css';

const BatchDeliveryPrintButton = props => {
  const [loading, setLoading] = useState(false);
  const handleBatchDelivery = () => {
    if (_global.electronWayBillVersion === WaybillVersionEnum.old) {
      return Notify.warn(
        '您当前使用的旧版电子面单系统暂不支持批量打单发货，请先到应用中心-电子面单页面升级新电子面单系统',
      );
    }
    const { selectedItem } = props;
    if (!selectedItem.length) {
      return Notify.warn('请先选择订单');
    }

    setLoading(true);

    const selectedList = selectedItem.map(item => {
      return {
        orderNo: item.orderNo,
        warehouseId: _global.kdtId,
        warehouseName: _global.shopName,
      };
    });
     openBatchDeliveryDialog({
          orders: selectedList,
          onSubmit() {
            closeBatchDeliveryDialog();
          },
          onClose: () => {
            closeBatchDeliveryDialog();
            setLoading(false);
          },
        });
  };
  return (
    <span className="batch-delivery-print-btn-wrap">
      <Button loading={loading} className="batch-delivery-print-btn" onClick={handleBatchDelivery}>
        批量打单发货
      </Button>
      <a target="_blank" href="/v4/trade/delivery/batch?from=orderList">
        查看批量打单发货记录
      </a>
    </span>
  );
};

export default BatchDeliveryPrintButton;
