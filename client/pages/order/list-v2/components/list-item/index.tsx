import React, { Component } from 'react';
import fullfillImage from '@youzan/utils/fullfillImage';
import formatMoney from '@youzan/utils/money/format';
import formatDate from '@youzan/utils/date/formatDate';
import {
  Sweetalert,
  Button,
  Icon,
  Pop,
  BlockLoading,
  Notify,
  ClampLines,
  ErrorBoundary,
} from 'zent';
import findIndex from 'lodash/findIndex';
import get from 'lodash/get';
import filter from 'lodash/filter';
import toString from 'lodash/toString';
import addDays from 'date-fns/add_days';
import { BlankLink } from '@youzan/react-components';
import { DynamicTable } from '@youzan/dynamic-table';
import { CloudSlot } from '@youzan/ranta-cloud-react';

import { Effect, Actions } from '../../effects';
import DeliveryBar from 'components/order/delivery-bar';
import AutomaticCallBar from 'components/order/automatic-call-bar';
import TicketButton from 'components/order/ticket-button';
import PopWrapper from 'components/pop-wrapper';
import openExpressDialog, {
  openWXSmallShopDialog,
  openQuickBackAdDialogProps,
} from 'components/order/open-express-dialog';
import { withRouter } from 'react-router';
import uuidV4 from 'uuid/v4';

import ListItemHeader from '../list-item-header';
import PrintOrder from '../../../components/print-order';
import PrintTangshiOrder from '../../../components/print-tangshi-order';
import ExpressPop from '../../../components/express-pop';
import RemindOrderButton from '../../../components/remind-order-button';
import { openCancelOrderDialog } from '../../../components/cancel-order';
import { openEditExpressDialog } from '../../../components/edit-express';
import DisableAction from '../../../components/disable-action';
import {
  openPickDialog,
  openRefusePickDialog,
  openCheckInDialog,
} from '../../../components/hotel-orde-opts';
import { openAddAddressDialog } from '../../../components/add-address-dialog';
import { openCancelTangshiOrderDialog } from '../../../components/cancel-tangshi-order';
import { openPriceDialog } from '../../../components/price-dialog';
import OpenCashier from '../../../components/open-cashier';
import { openStudentInfoDialog } from '../student-info-dialog';
import CityOrderState from '../../../components/city-order';
import openWholesaleVerifyDialog from '../../../components/wholesale-verify-dialog';
import {
  formatListItemData,
  getDefaultAddress,
  isImportOrder,
  notEduSignUp,
  isEduQuickOffline,
  isEduClassTransferOrder,
} from '../../utils';
import { DEFAULT_GOODS_IMAGE, IS_WHOLESALE } from '../../constants';
import {
  getWxSmallShopRefundStr,
  getIsFormSmallShop,
  isNotCurrentOutStoreOrder,
} from '../../../common/wx-small-shop';
import { isFinanceWithPriority } from 'constants/role';
import { videoShopMsgNotify } from 'constants/video-shop';
import { getRefundDetailUrl, formatJsonStrToArr } from 'fns/utils';
import { isFromShopInShop, isFromVideoShop } from 'fns/order-helper';
import buildUrl from '@youzan/utils/url/buildUrl';
import { IInfoData, ICustomInfoItem } from '../../types/education';
import { IFormattedOrderListItem, IGoodsItemWithPeriod, IOrderSearchQuery } from '../../types';
import { IDeliveryListItem, IGetTipsResponse } from 'definitions/order/list';
import { FEEDBACK_TYPE, IGoodsProperty } from 'definitions/order/detail/order';
import { isEduBasicVersion } from 'components/check-version';
import './index.scss';

import api from '../../api';
import classNames from 'classnames';
import { has } from 'lodash';
import { isOfflinePayOrderAndPendingAudit } from 'pages/order/common/wholesale';
import WholesaleAuditButton from 'pages/order/components/wholesale-audit-button';
import BlurredQueryButton from 'components/order/blurred-query-button';
import { ClickMode, ComponentVersion } from 'components/order/blurred-query-button/type';
import {
  orderAddressInfoKeyMap,
  buyerInfoKeyMap,
  mainOrderInfoKeyMap,
} from 'components/order/blurred-query-button/key';
import { isPureWscSingleStore } from '@youzan/utils-shop';
import { Button as ButtonText } from 'components/button';
import { yzLocation } from '@youzan/url-utils';
import { isQttShopOrder } from 'pages/order/common/helper';
import { InvoiceTypeEnum } from 'constants/order';
import { IColumnOptions, getStyleByColumn } from '../../columns';
import { isImageUrl, revertMessage } from '../../../detail-v2/components/goods-list/helper';
// import { throttle } from 'lodash';

interface IStateFromProps {
  isNewSafe: boolean;
  isPeriodBuy: boolean;
  isWaitShipped: boolean;
  isShipped: boolean;
  isLocalDelivery: boolean;
  isSelfTake: boolean;
  isGroup: boolean;
  isCodpay: boolean;
  isWaitProcessFeedback: boolean;
  isFeedbackSuccess: boolean;
  isHotel: boolean;
  isTangshi: boolean;
  isCrossBorder: boolean;
  isEnjoyBuy: boolean;
  isOfflinePayScene: boolean;
}

interface IProps extends ROUTER<{}> {
  selected: boolean;
  data: IFormattedOrderListItem;
  filterOptions: IOrderSearchQuery;
  tips: IGetTipsResponse;
  list: IFormattedOrderListItem[];
  onFilterConfirm: any;
  dynamicTable: DynamicTable<IColumnOptions>;
  mergedColumns: IColumnOptions[];
}

interface IState extends IStateFromProps {
  loading: boolean;
  posInit: boolean;
  isHideAutomaticCallBar: boolean;
}

// 判断是否需要重新发送请求，判断条件，渠道是蜂鸟或者达达，deliveryDetail.stateCode < 1
const filterReFetchPackages = (deliveryList: IDeliveryListItem[]) => {
  return filter(deliveryList, (item) => {
    const stateCode = get(item, 'deliveryDetail.stateCode', 1);
    if (item.sendType === 21 && stateCode < 1) {
      return true;
    }
    return false;
  });
};

class ListItem extends Component<IProps, IState> {
  static defaultProps = {
    data: {} as IFormattedOrderListItem,
    filterOptions: {} as IOrderSearchQuery,
    tips: {} as IGetTipsResponse,
  };
  op: HTMLDivElement | null;

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      posInit: false,
      isHideAutomaticCallBar: false,
      ...this.mapPropsToState(props),
    };
    this.op = null; // 操作栏容器ref
  }

  componentDidMount = () => {
    if (this.op) {
      this.formatInlineVerticalGap(this.op, 'mt10');
    }
    // window.onresize = throttle(() => this.formatInlineVerticalGap(this.op!, 'mt10'), 50);
  };

  componentDidUpdate(prevProps: Readonly<IProps>) {
    const oldSelectedItems = prevProps.dynamicTable.selectedItems;
    const newSelectedItems = this.props.dynamicTable.selectedItems;
    if (JSON.stringify(oldSelectedItems) !== JSON.stringify(newSelectedItems)) {
      this.op && this.formatInlineVerticalGap(this.op, 'mt10');
    }
  }

  componentWillReceiveProps(nextProps: IProps) {
    const newState = this.mapPropsToState(nextProps);
    this.setState(newState);
  }

  mapPropsToState = (props: IProps) => {
    return {
      isNewSafe: props.data.feedback >= 200, // 是否是新版维权，老维权15年7月以后的订单不会再有
      isPeriodBuy: +props.data.activityType === 13, // 是否是周期购
      isWaitShipped: +props.data.state === 5, // 待发货（已成团，待发货）
      isShipped: +props.data.state === 6, // 已发货
      isLocalDelivery: +props.data.expressType === 2, // 同城配送
      isSelfTake: +props.data.expressType === 1, // 到店自提
      isGroup: +props.data.orderType === 10, // 拼团订单
      isCodpay: +props.data.buyWay === 9, // 货到付款
      isWaitProcessFeedback: +props.data.feedback === 201, // 等待商家处理退款申请
      isFeedbackSuccess: +props.data.feedback === 250, // 退款成功
      isHotel: +props.data.orderType === 35, // 酒店订单
      isTangshi: !!props.data.isEatInOrder, // 是否是堂食订单
      isCrossBorder: !!get(props.data, 'tcTags.IS_CROSS_BORDER', false), // 是否是海淘订单
      isEnjoyBuy: has(props.data, 'enjoyBuyInfoList'), // 是否是随心订
      isOfflinePayScene: +props.data.buyWay === 5123, // 是否是线下支付订单
    };
  };

  formatInlineVerticalGap = (container: HTMLDivElement, addonClz, gap = 20) => {
    const childNode = Array.from(container.children);
    let lastY = -1;
    for (let idx = 0; idx < childNode.length; idx++) {
      const ele = childNode[idx];
      const { y } = ele.getBoundingClientRect();
      if (lastY !== -1 && y - lastY > gap) {
        ele.classList.add(addonClz);
      } else {
        ele.classList.remove(addonClz);
      }
      lastY = y;
    }
    setTimeout(() => {
      this.setState({ posInit: true });
      // 防止界面抖动 隐藏修改后再展示
    }, 80);
  };

  getRowSpan = (data: IFormattedOrderListItem) => {
    return this.state.isHotel ? 1 : data.items.length;
  };

  alertFeedbackNotice = () => {
    Sweetalert.alert({
      title: '退款维权提醒',
      content: (
        <div style={{ width: '420px' }}>
          订单中的部分商品，买家已提交了退款申请。你需要先跟买家协商，买家撤销退款申请后，才能进行发货操作。
        </div>
      ),
    });
  };

  getWeappCustomerName = (customer: string) => {
    const fdStart = customer.indexOf('wx_appservice');
    let newCustomer = '';
    if (fdStart === 0) {
      newCustomer = customer.replace(/wx_appservice/i, '小程序匿名用户');
    } else {
      newCustomer = customer;
    }
    return newCustomer;
  };

  // 通用的按钮点击前检查方法
  beforeCheck = (actionName: string, data: IFormattedOrderListItem): boolean => {
    // 检查是否有维权申请
    if (this.state.isWaitProcessFeedback) {
      this.alertFeedbackNotice();
      return false;
    }

    // 可以在这里添加更多的通用检查逻辑
    // 例如：检查订单状态、权限等

    console.log(`执行操作前检查: ${actionName}`, data);
    return true;
  };

  // 订单操作后，刷新该条订单
  refreshOrder = (useWholeReload = false) => {
    this.setState({ loading: true });
    const { query } = get(this.props, 'location');

    // 检查是否有同城订单的语音提醒
    Effect.app.getNoticeAndPlayAudio({ storeId: this.props.filterOptions.store_id });
    const type = IS_WHOLESALE ? 'wholesale_order' : query.type;
    api
      .getOrderData({
        orderNo: this.props.data.orderNo,
        orderId: this.props.data.orderId, // 堂食点餐订单，待处理的状态下需要 order_id 查订单信息
        expressType: query.express_type, // 查出同城送信息需要加上这个参数
        extType: query.ext_type, // 查出网点信息需要带上这个参数
        type, // 根据订单类型带出不同的信息
      })
      .then((data) => {
        const listItem = formatListItemData(data);
        this.updateOrderListItem(listItem);
      })
      .finally(() => {
        this.setState({ loading: false });
        // 发货弹窗 优化后的隐私信息组件会传入true
        // 刷新整个列表数据
        if (useWholeReload) {
          this.props?.onFilterConfirm();
        }
      });
  };

  // 同城送发货成功后的回调
  afterDeliveryCallback = () => {
    this.reFetchOrderInfo();
  };

  reFetchOrderInfo = () => {
    const { query } = get(this.props, 'location');
    api
      .getOrderData({
        orderNo: this.props.data.orderNo,
        expressType: query.express_type, // 查出同城送信息需要加上这个参数
        extType: query.ext_type, // 查出网点信息需要带上这个参数
        type: query.type, // 根据订单类型带出不同的信息
      })
      .then((data) => {
        const deliveryList = get(data, 'deliveryList', [] as IDeliveryListItem[]);
        const listItem = formatListItemData(data);
        const reFetchItems = filterReFetchPackages(deliveryList);
        this.updateOrderListItem(listItem);
        if (reFetchItems.length > 0) {
          reFetchItems.forEach((item) => {
            this.getPackageDetailWithCount(listItem, item.packId, 0);
          });
        }
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  getPackageDetailWithCount = (
    listItem: IFormattedOrderListItem,
    packId: string,
    count: number
  ) => {
    if (count > 10) {
      return;
    }
    setTimeout(() => {
      api
        .getPackageDetail(packId)
        .then((data) => {
          if (get(data, 'deliveryDetail.stateCode', 0) < 1) {
            this.getPackageDetailWithCount(listItem, packId, count + 1);
          } else {
            const deliveryList = listItem.deliveryList;
            const index = findIndex(deliveryList, (item) => item.packId === packId);
            if (index >= 0) {
              deliveryList[index] = data;
              listItem.deliveryList = [...deliveryList];
              this.updateOrderListItem(listItem);
            }
          }
        })
        .catch(() => {
          this.getPackageDetailWithCount(listItem, packId, count + 1);
        });
    }, 3000);
  };

  /**
   * 刷新订单后更新列表页
   */
  updateOrderListItem = (data: Partial<IFormattedOrderListItem>) => {
    const orderList = get(this.props, 'list', []);
    let index = findIndex(orderList, (item) => item.orderNo === this.props.data.orderNo);
    // 堂食待处理订单没有订单号（只有order_id）设计缺陷，导致代码逻辑复杂
    if (!this.props.data.orderNo && this.props.data.orderId) {
      index = findIndex(orderList, (item) => item.orderId === this.props.data.orderId);
    }

    if (index > -1) {
      const newOrderList = [...orderList];
      newOrderList[index] = {
        ...newOrderList[index],
        ...data,
      };

      Actions.AppState.updateOrderList(newOrderList);
    }
  };

  // 售后
  renderAftermarket = (
    data: IFormattedOrderListItem,
    goods: IGoodsItemWithPeriod,
    goodsIndex: number
  ) => {
    const orderDetailLink = getRefundDetailUrl(data.orderNo, goods.id);
    // 订单是否来自小商店
    const isFormSmallShop = getIsFormSmallShop(data as any);

    // 超卖（通用逻辑）
    const renderOversold = () => {
      const content = (
        <div style={{ width: '300px' }}>
          由于多人同时付款时，第三方支付接口存在时间差，因此该笔订单属于超卖订单，需要你自行联系买家退款，退款完成后再在订单详情做“标记退款”操作。
        </div>
      );
      return (
        <span>
          已超卖，需退款给买家
          <Pop trigger="hover" position="left-center" content={content}>
            <Icon className="oversold-icon" type="help-circle" />
          </Pop>
        </span>
      );
    };

    if (isFormSmallShop) {
      const text = getWxSmallShopRefundStr(goods.outItemRefundInfo);

      // 小商店订单
      return <td className="aftermarket-cell">{text}</td>;
    }
    // 新版维权都是商品维度
    if (this.state.isNewSafe) {
      const renderNewSafe = () => {
        const { feedbackStr, feedback, feedbackType } = goods;
        const renderHasFeedbackStr = () => {
          const isShowButton = +feedback === 250 && +feedbackType !== FEEDBACK_TYPE.EXCHANGE;
          // 订单详情链接
          return (
            <div>
              <div>
                <BlankLink href={orderDetailLink}>{feedbackStr}</BlankLink>
              </div>
              {/* 退款成功渲染"钱款去向按钮" */}
              {isShowButton && (
                <ButtonText
                  type="text"
                  href={`/v4/trade/order/refund?order_no=${data.orderNo}`}
                  target="_blank"
                  className="the-money-to"
                >
                  查看钱款去向
                </ButtonText>
              )}
            </div>
          );
        };
        if (feedbackStr) {
          return renderHasFeedbackStr();
        } else if (goodsIndex === 0 && this.state.isWaitShipped && data.isOversold) {
          return renderOversold();
        }
      };
      return (
        <td className="aftermarket-cell">
          <div className="aftermarket">{renderNewSafe()}</div>
        </td>
      );
    } else if (goodsIndex === 0) {
      // feedback
      /**
       * 老维权，订单维度
       * 15年7月以后的订单不会再有
       */
      return (
        <td className="aftermarket-cell" rowSpan={this.getRowSpan(data)}>
          <div className="aftermarket">
            {data.feedbackStr && <div>{data.feedbackStr}</div>}
            {!data.feedbackStr && this.state.isWaitShipped && data.isOversold && renderOversold()}
            {+data.feedback >= 100 && <p>收到维权</p>}
          </div>
        </td>
      );
    }
  };

  // 订单状态
  renderStateCell = (
    data: IFormattedOrderListItem,
    item: IGoodsItemWithPeriod,
    goodsIndex: number
  ) => {
    const deliveryStateMap = {
      0: '待付款',
      1: '待发货',
      2: '已发货',
      3: '全额退款',
      4: '部分退款',
    };

    // 周期购待发货
    const isPeriodOrderPendingDelivery =
      this.state.isPeriodBuy && this.state.isWaitShipped && item.periodOrderDetail;

    // 是否有 送礼礼单可发货
    const waitShippedNum = get(data, 'gift_order.wait_shipped_num', 0);
    const hasGiftOrder = waitShippedNum > 0;

    // 订单状态显示
    const renderState = () => {
      // 如果是随心订
      if (this.state.isEnjoyBuy) {
        const renderPeriodBuyState = () => {
          // 待配送
          if (+item.enjoyBuyInfo!.deliveryState === 1) {
            return (
              <div>
                {item.enjoyBuyInfo!.issue ? (
                  <div>
                    第{item.enjoyBuyInfo!.issue}
                    期待发货
                  </div>
                ) : (
                  <div>等待商家发货</div>
                )}
                {/* 周期购订单显示送达时间 */}
                {item.planExpressTime && (
                  <div>
                    <span className="orange">{item.planExpressTime}</span>
                    送达
                  </div>
                )}
              </div>
            );
          } else if (
            // 已配送
            (+item.enjoyBuyInfo!.deliveryState === 2 &&
              +item.enjoyBuyInfo!.issue === +item.enjoyBuyInfo!.totalIssue) ||
            (data.isPeriodBuySingle && !this.state.isWaitShipped)
          ) {
            return <div>商家已发货</div>;
          } else if (+item.enjoyBuyInfo!.deliveryState === 0) {
            return <div>待付款</div>;
          }
          return <div>{deliveryStateMap[+item.enjoyBuyInfo!.deliveryState]}</div>;
        };
        return renderPeriodBuyState();
      }

      if (isPeriodOrderPendingDelivery) {
        // 周期购订单状态
        const renderPeriodBuyState = () => {
          // 待配送
          if (+item.periodOrderDetail!.deliveryState === 1) {
            return (
              <div>
                {item.periodOrderDetail!.issue ? (
                  <div>
                    第{item.periodOrderDetail!.issue}
                    期待发货
                  </div>
                ) : (
                  <div>等待商家发货</div>
                )}
                {/* 周期购订单显示送达时间 */}
                {item.planExpressTime && (
                  <div>
                    <span className="orange">{item.planExpressTime}</span>
                    送达
                  </div>
                )}
              </div>
            );
          } else if (
            // 已配送
            (+item.periodOrderDetail!.deliveryState === 2 &&
              +item.periodOrderDetail!.issue === +item.periodOrderDetail!.totalIssue) ||
            (data.isPeriodBuySingle && !this.state.isWaitShipped)
          ) {
            return <div>商家已发货</div>;
          } else if (+item.periodOrderDetail!.deliveryState === 0) {
            return <div>待付款</div>;
          }
          return <div>{deliveryStateMap[+item.periodOrderDetail!.deliveryState]}</div>;
        };
        return renderPeriodBuyState();
      } else if (data.isPeriodBuySingle && this.state.isWaitShipped) {
        return <div>周期购待发货</div>;
      }

      return (
        <div>
          <p>{data.stateStr}</p>
          {hasGiftOrder && (
            <p className="gift-plugin-order__remain">
              {waitShippedNum}
              个礼单可发货
            </p>
          )}
        </div>
      );
    };

    // 同城送状态提示
    const renderCityState = () => {
      if (this.state.isLocalDelivery && this.state.isWaitShipped && data.deliveryTimeDisplay) {
        const content = (
          <div>
            已开启定时达
            <br />
            预约送达时间：
            {data.deliveryTimeDisplay}
          </div>
        );
        return (
          <Pop trigger="hover" position="bottom-center" content={content}>
            <div>
              <Icon className="city-state-icon" type="info-circle-o" />
            </div>
          </Pop>
        );
      }
    };

    if (goodsIndex === 0 || isPeriodOrderPendingDelivery) {
      return (
        <td
          rowSpan={isPeriodOrderPendingDelivery ? 1 : this.getRowSpan(data)}
          className="state-cell"
        >
          <div className="order-state">
            {renderState()}
            {/* 同城送订单 */}
            {renderCityState()}
          </div>
        </td>
      );
    }
  };

  // 商品编码
  private getGoodsNoHtml(goods: IGoodsItemWithPeriod) {
    const { sku, skuCode } = goods;
    const hasSku = sku && sku.length > 0;

    if (hasSku && skuCode) {
      return <div className="sku-item">规格编码: {skuCode}</div>;
    }

    return null;
  }

  // 操作项
  renderOperations = (
    data: IFormattedOrderListItem,
    item: IGoodsItemWithPeriod,
    goodsIndex: number
  ) => {
    // 是否需要补充信息
    const needEduSignUp =
      notEduSignUp(data) && data.orderState === 'success' && data.refundState === 0;
    // 订单是否来自小商店
    const isFormSmallShop = getIsFormSmallShop(data as any);

    // 周期购待发货
    const isPeriodOrderPendingDelivery =
      this.state.isPeriodBuy && this.state.isWaitShipped && item.periodOrderDetail;

    // 是否可以快速收款
    const isEduQuickOffline =
      data.orderMark === 'edt_offline_sign' &&
      data.orderState === 'topay' &&
      +get(data, 'tcExtra.OUTSIDE_IMPORT') !== 1;

    // 学员信息
    const infoData = get(data, 'tcExtra.EDU_STUDENT_INFO');
    // 自定义学员资料项
    const customInfo = get(data, 'customInfoMap.edu_student_info');

    // 是否显示报名信息，课程商品新增
    let showStudentInfo;
    let customStudentInfo;

    try {
      showStudentInfo = JSON.parse(infoData);
      customStudentInfo = JSON.parse(customInfo);
    } catch (err) {
      // do nothing
    }
    const showRegisterInfo =
      !!(
        data.permission.allowKnowledgePayViewSingUpInfo &&
        (showStudentInfo || customStudentInfo)
      ) && !notEduSignUp(data);

    // 发货
    const renderSend = () => {
      const sendBtnProps: { size: any; type: any; className: string } = {
        size: 'small',
        type: 'primary',
        className: 'opt-item',
      };
      // 小商店订单来源为非当前绑定的小商店 无法操作
      if (
        this.state.isWaitShipped &&
        data.permission.allowSend &&
        isNotCurrentOutStoreOrder(data as any)
      ) {
        return (
          <Button {...sendBtnProps} disabled>
            发货
          </Button>
        );
      }
      return (
        <>
          {this.state.isWaitShipped &&
            data.permission.allowSend &&
            (data.permission.allowUnpack ? (
              // 拆包发货 dialog
              <>
                <Button {...sendBtnProps} onClick={() => this.handleExpressGoods(data, item)}>
                  发货
                </Button>
              </>
            ) : (
              // 普通发货 pop
              <>
                {/* @ts-ignore */}
                <ExpressPop callback={this.refreshOrder} orderNo={data.orderNo}>
                  <Button {...sendBtnProps}>发货</Button>
                </ExpressPop>
              </>
            ))}
          {isEduQuickOffline && (
            <div>
              <OpenCashier orderNo={data.orderNo} realPay={data.realPay} />
            </div>
          )}
          {showRegisterInfo && (
            <div>
              <a onClick={() => this.handleRegisterInfo(data)}>报名信息</a>
            </div>
          )}
          {needEduSignUp && (
            <div>
              {!isEduBasicVersion() && (
                <a
                  href={'/v4/vis/edu/page/enrollment?orderNo=' + data.orderNo}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  补充报名信息
                </a>
              )}
            </div>
          )}
        </>
      );
    };

    // 电子卡券：出票
    const renderTicket = () => {
      if (!data.permission.allowTicket) {
        return null;
      }
      const isFenxiaoOrder = get(data, 'tcTags.IS_FENXIAO_ORDER', false);
      return (
        <div>
          <PopWrapper
            className="opt-item"
            trigger="hover"
            position="top-center"
            content={isFenxiaoOrder ? '分销商品由供货商出票' : ''}
          >
            <TicketButton
              orderNo={data.orderNo}
              callback={this.refreshOrder}
              disabled={isFenxiaoOrder}
            />
          </PopWrapper>
        </div>
      );
    };

    // 允许接单
    const renderAllowPickOrder = () => {
      if (data.permission.allowPickOrder) {
        return (
          <>
            <ButtonText
              type="text"
              onClick={() => this.handlePickOrder(data)}
              className="pick-order opt-item"
            >
              接单
            </ButtonText>
            {data.permission.allowRefusePickOrder && +item.feedback !== 201 && (
              <ButtonText
                type="text"
                onClick={() => this.handleRefusePick(data)}
                className="refuse-order opt-item"
              >
                拒绝接单
              </ButtonText>
            )}
          </>
        );
      }
    };

    const renderAllowCheckIn = () => {
      if (data.permission.allowCheckIn) {
        return (
          <ButtonText
            type="text"
            className="pick-order opt-item"
            onClick={() => this.handleCheckIn(data)}
          >
            确认入住
          </ButtonText>
        );
      }
    };

    // 修改物流
    const renderUpExpressSend = () => {
      if (data.permission.allowUpExpressSend && !this.state.isPeriodBuy) {
        if (data.permission.allowUnpack) {
          // 视频号小店 -- 修改物流
          const _isFromVideoShop = isFromVideoShop(data);
          if (_isFromVideoShop) {
            return (
              <ButtonText
                type="text"
                className="opt-item"
                onClick={() => Notify.error(videoShopMsgNotify)}
              >
                修改物流
              </ButtonText>
            );
          }

          return (
            <ButtonText
              type="text"
              className="opt-item"
              onClick={() => this.handleEditExpress(data, item)}
            >
              修改物流
            </ButtonText>
          );
        }
        return (
          <>
            <ExpressPop isEdit callback={this.refreshOrder} orderNo={data.orderNo}>
              <ButtonText type="text" className="opt-item">
                修改物流
              </ButtonText>
            </ExpressPop>
          </>
        );
      }
    };

    const renderAllowRemind = () => {
      if (data.permission.allowRemindOrderButton) {
        // 视频号小店 -- 订单催付
        const _isFromVideoShop = isFromVideoShop(data);
        if (_isFromVideoShop) {
          return (
            <ButtonText
              type="text"
              className="opt-item"
              onClick={() => Notify.error(videoShopMsgNotify)}
            >
              催付
            </ButtonText>
          );
        }
        return (
          <RemindOrderButton
            sourcePage="orderlist"
            position="left-center"
            orderInfo={data}
            type="v2"
          />
        );
      }
    };

    // 取消订单
    const renderAllowClose = () => {
      if (data.permission.allowClose && goodsIndex === 0) {
        const dianzhongdianInfo = isFromShopInShop(data);
        const _isFromVideoShop = isFromVideoShop(data);
        // 视频号小店 -- 取消订单
        if (_isFromVideoShop) {
          return (
            <ButtonText
              type="text"
              className="opt-item"
              onClick={() => Notify.error(videoShopMsgNotify)}
            >
              取消订单
            </ButtonText>
          );
        }
        if (dianzhongdianInfo) {
          return (
            <ButtonText
              type="text"
              className="opt-item"
              onClick={() => Notify.error(dianzhongdianInfo)}
            >
              取消订单
            </ButtonText>
          );
        }
        // 堂食 -- 取消订单
        if (data.isEatInOrder) {
          return (
            <ButtonText
              type="text"
              className="opt-item"
              onClick={() =>
                openCancelTangshiOrderDialog({
                  orderId: data.orderId, // 堂食订单-待处理：会有这个 order_id
                  storeId: data.storeId, // 多网点订单后端会校验 storeId
                  callback: this.refreshOrder,
                })
              }
            >
              取消订单
            </ButtonText>
          );
        }
        // 货到付款
        if (this.state.isCodpay) {
          return (
            <ButtonText
              type="text"
              className="opt-item"
              onClick={() =>
                openCancelOrderDialog({
                  orderNo: data.orderNo,
                  callback: this.refreshOrder,
                })
              }
            >
              取消订单
            </ButtonText>
          );
        }
        return (
          <ButtonText
            type="text"
            className="opt-item"
            onClick={() =>
              openCancelOrderDialog({
                orderNo: data.orderNo,
                callback: this.refreshOrder,
              })
            }
          >
            取消订单
          </ButtonText>
        );
      }
    };

    // 打印堂食小票
    const renderPrintTangshi = () => {
      if (!get(data, 'permission.allowPrintEatInOrder')) {
        return;
      }
      return (
        <div>
          <PrintTangshiOrder data={data}>
            <ButtonText type="text" className="opt-item">
              打印小票
            </ButtonText>
          </PrintTangshiOrder>
        </div>
      );
    };

    // 打印小票按钮
    const renderPrintTicketButton = () => {
      if (data.permission.allowPrint) {
        return (
          <DisableAction isDisabled={isFormSmallShop}>
            <PrintOrder data={data} type="v2">
              <ButtonText type="text">打印小票</ButtonText>
            </PrintOrder>
          </DisableAction>
        );
      }
    };

    // 打印凭证按钮
    const renderPrintPaymentCertificate = (orderNo) => {
      const isOfflineSign = get(data, 'tc_order_source_d_t_o.orderSourceCode') === 'offline_sign';
      const isPaid = get(data, 'state') === 100;
      const orderKdtId = get(data, 'kdtId', '');
      const isImport = isImportOrder(data);
      const shouldSignUp = notEduSignUp(data);
      if (orderNo && isEduClassTransferOrder(data) && orderKdtId) {
        return (
          <BlankLink href={`/v4/vis/edu/page/adjustcourse#/certificate/${orderNo}/${orderKdtId}`}>
            打印转课凭证
          </BlankLink>
        );
      }
      if (isOfflineSign && isPaid && !isImport && !shouldSignUp) {
        return (
          <div>
            <BlankLink href={`/v4/vis/edu/page/signUpCerti?orderNo=${orderNo}`}>
              打印缴费凭证
            </BlankLink>
          </div>
        );
      }
    };

    // 批发线下支付审核
    const renderWholesaleVerify = () => {
      // 是否展示审核按钮
      if (
        isOfflinePayOrderAndPendingAudit({
          buyWay: data.buyWay,
          allowAuditWholesaleOrder: data?.permission?.allowAuditWholesaleOrder,
        })
      ) {
        return (
          <WholesaleAuditButton
            _type="v2"
            className="wholesaleVerifyBtn"
            handleClick={() =>
              openWholesaleVerifyDialog({ orderNo: data.orderNo, callback: this.refreshOrder })
            }
          />
        );
      }
    };
    if (goodsIndex === 0 || isPeriodOrderPendingDelivery) {
      const getTuanUrl = async (data) => {
        if (!data.tuanId) {
          const { activityType, orderNo } = data;
          await api
            .getActivityGroup({
              umpType: activityType,
              orderNo,
            })
            .then((res) => {
              if (res) {
                const { groupNo } = res[orderNo];
                data.tuanId = groupNo;
              }
            })
            .catch((msg) => {
              Notify.error(msg);
            });
        }
        const url = `#list?type=tuan&state=all&tuanId=${data.tuanId}&sub_shop_kdt_id=${data.kdtId}`;
        yzLocation.href = url;
      };
      return (
        <td
          rowSpan={isPeriodOrderPendingDelivery ? 1 : this.getRowSpan(data)}
          className="operation-cell cell-fixed"
        >
          <div
            className="operation-cell__wrap"
            ref={(ref) => (this.op = ref)}
            style={{ visibility: this.state.posInit ? 'visible' : 'hidden' }}
          >
            {/* 发货 */}
            {renderSend()}
            {/* 电子卡券：出票 */}
            {renderTicket()}
            {/* 同城订单状态：接单、拒单 */}
            <CityOrderState
              allowConfirmCityOrder={get(data, 'permission.allowConfirmCityOrder', false)}
              allowRejectCityOrder={get(data, 'permission.allowRejectCityOrder', false)}
              orderNo={data.orderNo}
              callback={this.refreshOrder}
              type="v2"
            />
            {/* 允许接单 酒店/餐饮 */}
            {renderAllowPickOrder()}
            {/* 允许酒店入住 */}
            {renderAllowCheckIn()}
            {/* 修改物流 */}
            {renderUpExpressSend()}
            {/* 订单催付 */}
            {renderAllowRemind()}
            {/* 允许关闭订单 */}
            {renderAllowClose()}
            {/* 打印小票 */}
            {renderPrintTicketButton()}
            {/* 打印堂食小票 */}
            {renderPrintTangshi()}
            {/* 打印缴费凭证 */}
            {renderPrintPaymentCertificate(get(data, 'orderNo'))}
            {/* 查看同团订单 */}
            {+data.orderType === 10 && data.orderState !== 'topay' && (
              <ButtonText type="text" className="opt-item" onClick={() => getTuanUrl(data)}>
                查看同团订单
              </ButtonText>
            )}
            {
              // 抽奖拼团订单
              +data.activityType === 23 && data.tuanId && (
                <ButtonText
                  className="opt-item"
                  type="text"
                  target="_blank"
                  href={`#list?type=lottery&state=all&tuanId=${data.tuanId}`}
                >
                  查看同团订单
                </ButtonText>
              )
            }
            {/* 展示批发场景 - 审核按钮 */}
            {renderWholesaleVerify()}
          </div>
        </td>
      );
    }
  };

  // 付款金额
  renderPayPriceCell = (data: IFormattedOrderListItem, goodsIndex: number) => {
    if (goodsIndex !== 0) {
      return;
    }
    const renderPrice = () => {
      if (data.isPoints) {
        return (
          <div>
            {data.realPointPay}
            积分
            {parseFloat(data.settlementMoney) > 0 && <span>+ ￥{data.settlementMoney}</span>}
          </div>
        );
      }
      return <div>{data.settlementMoney}</div>;
    };
    const dianZhongDianInfo = isFromShopInShop(data);
    return (
      <td className="pay-price-cell" rowSpan={this.getRowSpan(data)}>
        <div>
          {renderPrice()}
          {+data.postage !== 0 && (
            <div>
              <span className="c-gray">(含运费: {data.postage})</span>
            </div>
          )}
          {data.permission.allowChangePrice && !dianZhongDianInfo && (
            <ButtonText type="text" onClick={() => this.handleEditPrice(data)}>
              修改价格
            </ButtonText>
          )}
          {data.permission.allowChangePrice && dianZhongDianInfo && (
            <ButtonText type="text" onClick={() => Notify.error(dianZhongDianInfo)}>
              修改价格
            </ButtonText>
          )}
        </div>
      </td>
    );
  };

  renderSkuAndProperty = (goods: IGoodsItemWithPeriod) => {
    const { sku = [], extraMap } = goods;
    const properties = formatJsonStrToArr<IGoodsProperty>(extraMap?.GOODS_PROPERTY);
    const skuAndProperty = [
      ...sku.map((item) => item.v),
      ...properties.map((property) => property.valName),
    ];

    const skuInfo = skuAndProperty.join(',');

    return (
      <div className="sku-item" key={uuidV4()}>
        <ClampLines key={uuidV4()} lines={2} showPop={false} text={skuInfo} />
      </div>
    );
  };

  renderGoodsTitle(canNotJump, goods) {
    const goodsTitle = canNotJump ? (
      goods.title
    ) : (
      <a
        href={buildUrl(goods.url, '', goods.kdtId)}
        rel="noopener noreferrer"
        target="_blank"
        title={goods.title}
      >
        {goods.title}
      </a>
    );
    return goodsTitle;
  }

  renderGoodsItem = (data: IFormattedOrderListItem, goods: IGoodsItemWithPeriod) => {
    /**
     * 商品信息
     */
    const _renderGoodsInfo = () => {
      const tags = [] as string[];
      if (this.state.isEnjoyBuy) {
        tags.push('随心订');
      }
      if (+goods.isPresent) {
        tags.push('赠品');
      }
      // if (goods.isShipped) {
      //   tags.push('已发货');
      // }
      const getDateFromGoods = (goods: IGoodsItemWithPeriod, isEndDay = false) => {
        if (get(goods, 'goodsInfo.goods_date')) {
          let date = new Date(get(goods, 'goodsInfo.goods_date'));
          if (isEndDay) {
            date = addDays(date, 1);
          }
          const dateStr = `${+formatDate(date, 'MM')}月${+formatDate(date, 'DD')}日`;
          return dateStr;
        }
      };
      // 酒店商品
      const _renderHotelSku = () => {
        const { items } = data;
        const len = items.length;
        let dateFirst = getDateFromGoods(items[0]);
        let dateLast: string | undefined;
        if (len === 1) {
          dateLast = getDateFromGoods(items[0], true);
        } else {
          const first = items[0];
          const last = items[len - 1];
          dateFirst = getDateFromGoods(first);
          dateLast = getDateFromGoods(last, true);
        }
        if (dateFirst && dateLast) {
          return <div className="sku-item">{`${dateFirst} - ${dateLast}`}</div>;
        }
      };
      // 周期购 sku
      // 个人推测改造后和商品sku合并 故暂时注释
      // const _renderPeriodSku = () => {
      //   if (
      //     get(goods, 'periodOrderDetail.periodSku') &&
      //     get(goods, 'periodOrderDetail.periodDeliverTime')
      //   ) {
      //     return (
      //       <>
      //         <div className="sku-item">
      //           周期购规格：
      //           {get(goods, 'periodOrderDetail.periodDesc')}
      //         </div>
      //         <div className="sku-item">
      //           送达时间：
      //           {get(goods, 'periodOrderDetail.periodDeliverTime')}
      //         </div>
      //       </>
      //     );
      //   }
      // };

      // const goodsTitle = <ClampLines lines={2} showPop={false} text={goods.title} />;
      // 符合规则的商品标题不可点击 无法跳转
      const canNotJump =
        // 6 二维码订单 15 返利订单 75 知识付费订单
        findIndex([6, 15, 75], (item) => item === +data.orderType) > -1 ||
        isImportOrder(data) ||
        isEduClassTransferOrder(data) ||
        isEduQuickOffline(data) ||
        // 小商店订单，并且没有alias。不可跳转
        (getIsFormSmallShop(data as any) && !goods.alias) ||
        isQttShopOrder(data as any);

      return (
        <div className="goods-info__info">
          {/* 商品名称 */}
          <div
            className={classNames('goods-title', {
              disabled: isNotCurrentOutStoreOrder(data as any),
            })}
          >
            {this.renderGoodsTitle(canNotJump, goods)}
            {/* {canNotJump ? (
              goodsTitle
            ) : (
              <a
                href={buildUrl(goods.url, '', goods.kdtId)}
                rel="noopener noreferrer"
                target="_blank"
                title={goods.title}
              >
                {goodsTitle}
              </a>
            )} */}
          </div>
          {/* sku */}
          <div className="goods-skus">
            {this.renderSkuAndProperty(goods)}
            {this.getGoodsNoHtml(goods)}
            {/* 酒店订单 */}
            {this.state.isHotel && _renderHotelSku()}
            {/* 周期购订单规格 */}
            {/* {this.state.isPeriodBuy && _renderPeriodSku()} */}
          </div>
          {/* 赠品标签 */}
          <div className="goods-tags">
            {tags.map((tag, idx) => {
              return (
                <span key={idx} className="goods-tag">
                  {tag}
                </span>
              );
            })}
          </div>
        </div>
      );
    };

    /**
     * 商品价格
     */
    const _renderGoodsPrice = () => {
      // 酒店商品计算平均价格
      let point = get(goods, 'goodsInfo.points_price');
      let price: number | string = goods.price;
      let payPrice: number | string = goods.payPrice;
      if (this.state.isHotel) {
        // 酒店商品特殊逻辑
        const items = data.items;
        point = 0;
        price = 0;
        payPrice = 0;
        items.forEach((one) => {
          point += get(one, 'goodsInfo.points_price');
          price = (price as number) + one.priceLong;
          payPrice = (payPrice as number) + one.payPriceLong;
        });
        price = (price / 100).toFixed(2);
        payPrice = (payPrice / 100).toFixed(2);
      }
      if (!data.isPoints || (data.isPoints && !point)) {
        return <div className="goods-info__pay">￥{price}</div>;
      }
      return (
        <div className="goods-info__pay">
          {point}
          积分
          {parseFloat(payPrice as string) > 0 && ` + ￥${payPrice}`}
        </div>
      );
    };

    const imageUrl =
      isImportOrder(data) || isEduClassTransferOrder(data)
        ? DEFAULT_GOODS_IMAGE
        : fullfillImage(goods.imageUrl, '!100x100.jpg');

    return (
      <td className="goods-cell cell-fixed">
        <div className="goods-item-cell">
          <div className="goods-info__img_block">
            <img className="goods-info__img" src={imageUrl} alt="" />
            {/* 已发货展示标签 */}
            {goods.isShipped && <div className="goods-order-state">已发货</div>}
          </div>
          {_renderGoodsInfo()}
          <div className="goods-info__price">
            {_renderGoodsPrice()}
            <div className="goods-info__unit">
              {goods.num}
              {this.state.isHotel ? '间房' : '件'}
            </div>
          </div>
        </div>
      </td>
    );
  };

  formatCustomer(customer = '') {
    // ASCII码中，对应符号为软连字符，视觉上为空字符串，因此过滤掉
    // 参考：http://ascii.wjccx.com/
    const noCharAscCodes = [32, 127, 129, 141, 143, 144, 157, 160, 173];
    const chars = customer.split('').filter((char) => {
      return !noCharAscCodes.includes(char.charCodeAt(0));
    });
    // 如果为空默认展示 -
    return chars.join('') || '-';
  }

  // 买家
  renderCustomerCell = (data: IFormattedOrderListItem, goodsIndex: number) => {
    const { isTangshi } = this.state;
    const {
      isModifyLogistics,
      orderState,
      userName,
      tel,
      buyerName,
      buyerPhone,
      orderNo,
      customer,
    } = data;
    const isToSend = orderState === 'tosend'; // 待发货
    const isToPay = orderState === 'topay'; // 待付款
    const renderFans = () => {
      // 订单是否来自小商店
      const isFormSmallShop = getIsFormSmallShop(data as any);
      const dianZhongDianInfo = isFromShopInShop(data);

      const showMoreIcon =
        isPureWscSingleStore &&
        (userName.includes('*') || customer.includes('*') || tel.includes('*')) ? (
          <BlurredQueryButton
            style={{ marginLeft: 2 }}
            version={ComponentVersion.OptimizedPrivacy}
            clickMode={ClickMode.Fetch}
            searchKey={[
              buyerInfoKeyMap.customer,
              orderAddressInfoKeyMap.tel,
              orderAddressInfoKeyMap.userName,
            ]}
            orderNo={orderNo}
            onShowMore={(value) => {
              if (value?.length > 0) {
                value.forEach((item) => {
                  data[item.key] = item.value;
                });
                this.updateOrderListItem(data);
              }
            }}
          />
        ) : null;

      if (isFormSmallShop) {
        return (
          <>
            小商店用户
            {showMoreIcon}
          </>
        );
      }

      if (!data.customer) {
        return (
          <>
            非粉丝
            {showMoreIcon}
          </>
        );
      }
      /* 粉丝昵称有可能是 span 标签，需要显示 emoji 表情 */
      if (data.buyerId) {
        // 角色是财务
        if (isFinanceWithPriority || +data.orderType === 3) {
          return (
            <>
              <span
                dangerouslySetInnerHTML={{ __html: this.getWeappCustomerName(data.customer) }}
              />
              {showMoreIcon}
            </>
          );
        }

        // 店中店订单
        if (dianZhongDianInfo) {
          return (
            <>
              <a
                dangerouslySetInnerHTML={{ __html: data.customer }}
                onClick={() => Notify.error(dianZhongDianInfo)}
              />
              {showMoreIcon}
            </>
          );
        }
        const customer = this.formatCustomer(data.customer);

        return (
          <>
            <a
              href={`/v4/scrm/customer/manage#/detail?yzUid=${data.buyerId}`}
              rel="noopener noreferrer"
              dangerouslySetInnerHTML={{ __html: customer }}
              target="_blank"
              className={!showMoreIcon ? 'customer-view-block' : ''}
            />
            {showMoreIcon}
          </>
        );
      } else if (+data.customer !== +data.tel || +data.orderType === 35) {
        return (
          <>
            <span dangerouslySetInnerHTML={{ __html: this.getWeappCustomerName(data.customer) }} />
            {showMoreIcon}
          </>
        );
      }
    };
    const wholesalerName = data.tcExtra?.bizOrderAttribute?.wholesaleSnapshot?.name || '';
    const showBuyerLine = isTangshi ? !!(buyerName || buyerPhone) : !!(tel || userName); // 有收货人信息则展示该行
    if (goodsIndex === 0) {
      return (
        <td className="customer-cell" rowSpan={this.getRowSpan(data)}>
          <div className="fans-cell">
            <span className="label">买家：</span>
            {renderFans()}
          </div>
          {showBuyerLine && (
            <div className="buyer-cell">
              <span className="label">收货人：</span>
              {!this.state.isTangshi && (
                <>
                  <span
                    className={classNames('user-name', {
                      disabled: isNotCurrentOutStoreOrder(data as any),
                    })}
                  >
                    {IS_WHOLESALE ? wholesalerName : userName}
                  </span>
                  <span>{tel}</span>
                  {isModifyLogistics && (isToSend || isToPay) && (
                    <span className="modify-address">(信息变更)</span>
                  )}
                </>
              )}
              {this.state.isTangshi && (
                <>
                  <span
                    className={classNames('user-name', {
                      disabled: isNotCurrentOutStoreOrder(data as any),
                    })}
                  >
                    {buyerName}
                  </span>
                  <span>{buyerPhone}</span>
                </>
              )}
            </div>
          )}
        </td>
      );
    }
  };

  // 配送方式
  renderExpressWay = (data: IFormattedOrderListItem, goodsIndex: number) => {
    if (goodsIndex === 0) {
      return (
        <td className="express-cell" rowSpan={this.getRowSpan(data)}>
          {get(data, 'expressTypeDesc[0]', '')}
        </td>
      );
    }
  };

  // 订单发货时间
  renderExpressTime = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      return (
        <td
          className={`cell-${column.size} cell-left`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {get(data, 'expressTime', '')}
        </td>
      );
    }
  };

  // 收货地址
  renderAddressDetail = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    // 自提订单不展示收货地址
    if (goodsIndex === 0) {
      return (
        <td
          className={`cell-${column.size} cell-left`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {!this.state.isSelfTake &&
            [
              get(data, 'province', ''),
              get(data, 'city', ''),
              get(data, 'county', ''),
              get(data, 'addressDetail', ''),
            ].join('')}
        </td>
      );
    }
  };

  // 提货时间
  renderSelfFetchTime = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      return (
        <td
          className={`cell-${column.size} cell-left`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {get(data, 'selfFetch.fetchTime', '')}
        </td>
      );
    }
  };

  // 商品分组
  renderGoodsGroupTitles = (
    _data: IFormattedOrderListItem,
    goods: IGoodsItemWithPeriod,
    column: IColumnOptions
  ) => {
    let groupTitles = [];
    try {
      groupTitles = JSON.parse(get(goods, 'goodsInfo.groupTitles', ''));
    } catch {}
    return (
      <td className={`cell-${column.size} cell-left`} style={getStyleByColumn(column)}>
        {groupTitles.join('；')}
      </td>
    );
  };
  // 商品金额合计
  renderGoodsTotalPrice = (
    _data: IFormattedOrderListItem,
    goods: IGoodsItemWithPeriod,
    column: IColumnOptions
  ) => {
    const unitPrice = get(goods, 'unitPrice', 0);
    const num = get(goods, 'num', 0);
    return (
      <td className={`cell-${column.size} cell-right`} style={getStyleByColumn(column)}>
        {formatMoney((unitPrice * num) / 100, false, false)}
      </td>
    );
  };
  // 商品抵用积分数
  renderGoodsPointsPrice = (
    _data: IFormattedOrderListItem,
    goods: IGoodsItemWithPeriod,
    column: IColumnOptions
  ) => {
    const pointsPrice = get(goods, 'goodsInfo.points_price', 0);
    const num = get(goods, 'num', 0);
    return (
      <td className={`cell-${column.size} cell-right`} style={getStyleByColumn(column)}>
        {pointsPrice > 0 && pointsPrice * num}
      </td>
    );
  };
  // 商品实际成交金额
  renderGoodsRealPay = (
    _data: IFormattedOrderListItem,
    goods: IGoodsItemWithPeriod,
    column: IColumnOptions
  ) => {
    return (
      <td className={`cell-${column.size} cell-right`} style={getStyleByColumn(column)}>
        {formatMoney(get(goods, 'realPay', 0) / 100, false, false)}
      </td>
    );
  };
  // 商品留言
  renderGoodsMessage = (
    _data: IFormattedOrderListItem,
    goods: IGoodsItemWithPeriod,
    _column: IColumnOptions
  ) => {
    const messageList = revertMessage(get(goods, 'message', {}));
    return (
      <td className="goods-message-cell cell-left">
        {messageList.map((item, idx) => {
          const { title, text } = item;
          if (text === '') {
            return null;
          }
          if (isImageUrl(text)) {
            return (
              <dl key={idx}>
                <dt>{title}：</dt>
                <dd>
                  <BlankLink href={text} target="_blank">
                    点此查看图片
                  </BlankLink>
                </dd>
              </dl>
            );
          }
          return (
            <dl key={idx}>
              <dt>{title}：</dt>
              <dd>{text}</dd>
            </dl>
          );
        })}
      </td>
    );
  };

  // 储值/礼品卡支付金额
  renderDeductionPay = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      const deductionPay = get(data, 'deductionPay', 0);
      return (
        <td
          className={`cell-${column.size} cell-right`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {Number(deductionPay) > 0 ? deductionPay : ''}
        </td>
      );
    }
  };

  // 外部支付方式
  renderOutsidePayWayName = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      return (
        <td
          className={`cell-${column.size} cell-left`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {get(data, 'tcExtra.OUTSIDE_PAYWAY_NAME', '')}
        </td>
      );
    }
  };

  // 外部收银单号
  renderOutsidePayAcquireNo = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      return (
        <td
          className={`cell-${column.size} cell-left`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {get(data, 'tcExtra.OUTSIDE_PAY_ACQUIRENO', '')}
        </td>
      );
    }
  };

  // 群团团帮卖+专属佣金
  renderQttPromotionFee = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      let qttOrder = {};
      try {
        qttOrder = JSON.parse(get(data, 'tcExtra.ATTR_QTT_ORDER_EXPORT', '{}'));
      } catch {}
      const promotionComm = get(qttOrder, 'promotionComm');
      const exclusiveComm = get(qttOrder, 'exclusiveComm');

      return (
        <td
          className={`cell-${column.size} cell-right`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {(promotionComm != null || exclusiveComm != null) &&
            formatMoney((promotionComm || 0) + (exclusiveComm || 0), false, false)}
        </td>
      );
    }
  };

  // 群团团佣金结算状态
  renderQttSettleStatus = (
    data: IFormattedOrderListItem,
    goodsIndex: number,
    column: IColumnOptions
  ) => {
    if (goodsIndex === 0) {
      let qttOrder = {};
      try {
        qttOrder = JSON.parse(get(data, 'tcExtra.ATTR_QTT_ORDER_EXPORT', '{}'));
      } catch {}
      const settleStatus = get(qttOrder, 'settleStatus');
      let settleStatusText = '';
      if (settleStatus == 101 || settleStatus == 103) {
        settleStatusText = '未结算';
      } else if (settleStatus == 102) {
        settleStatusText = '已结算';
      }
      return (
        <td
          className={`cell-${column.size} cell-left`}
          rowSpan={this.getRowSpan(data)}
          style={getStyleByColumn(column)}
        >
          {settleStatusText}
        </td>
      );
    }
  };

  // 发货dialog
  handleExpressGoods = (data: IFormattedOrderListItem, item: IGoodsItemWithPeriod) => {
    if (!this.beforeCheck('发货', data)) {
      return;
    }

    getDefaultAddress().then((address) => {
      if (address?.address) {
        const isSmallStoreOrder =
          get(data, 'tc_order_source_d_t_o.orderSourceCode') === 'wx_small_shop';
        if (isSmallStoreOrder) {
          const closeDialog = openWXSmallShopDialog({
            orderNo: data.orderNo,
            outOrderNo: data.outBizNo,
            callback: (shouldRefresh) => {
              closeDialog();
              if (!shouldRefresh) {
                openExpressDialog({
                  kdtId: item.kdtId,
                  orderNo: data.orderNo,
                  outOrderNo: data.outBizNo,
                  orderInfo: data,
                  itemId: toString(item.itemId),
                  isSmallStoreOrder,
                  callback: this.refreshOrder,
                });
              } else {
                this.refreshOrder();
              }
            },
          });
          return;
        }
        openExpressDialog({
          kdtId: item.kdtId,
          orderNo: data.orderNo,
          orderInfo: data,
          itemId: toString(item.itemId),
          isSmallStoreOrder,
          callback: ({ showQuickBackAdDialog }) => {
            this.refreshOrder();
            if (showQuickBackAdDialog) {
              openQuickBackAdDialogProps();
            }
          },
        });
      } else {
        openAddAddressDialog();
      }
    });
  };

  handleCancelOrder = (data: IFormattedOrderListItem) => {
    let tips: React.ReactNode = null;
    if (data.orderTag && data.orderTag.isMergePay) {
      tips = (
        <div style={{ marginTop: '15px' }} className="red">
          与该订单关联的其他订单将同时取消
        </div>
      );
    }
    openCancelOrderDialog({
      orderNo: data.orderNo,
      callback: this.refreshOrder,
      tips,
    });
  };

  // 接单
  handlePickOrder = (data: IFormattedOrderListItem) => {
    if (!this.beforeCheck('接单', data)) {
      return;
    }
    openPickDialog({
      orderNo: data.orderNo,
      callback: this.refreshOrder,
    });
  };

  // 拒绝接单
  handleRefusePick = (data: IFormattedOrderListItem) => {
    if (!this.beforeCheck('拒绝接单', data)) {
      return;
    }
    openRefusePickDialog({
      orderNo: data.orderNo,
      callback: this.refreshOrder,
    });
  };

  handleCheckIn = (data: IFormattedOrderListItem) => {
    if (!this.beforeCheck('确认入住', data)) {
      return;
    }
    openCheckInDialog({
      orderNo: data.orderNo,
      callback: this.refreshOrder,
    });
  };

  // 修改物流
  handleEditExpress = (data: IFormattedOrderListItem, _) => {
    if (!this.beforeCheck('修改物流', data)) {
      return;
    }
    openEditExpressDialog({
      orderNo: data.orderNo,
      upExpressVersion: (data.permission || {}).upExpressVersion || 1,
      callback: this.refreshOrder,
    });
  };

  // 改价
  handleEditPrice = (data: IFormattedOrderListItem) => {
    if (!this.beforeCheck('修改价格', data)) {
      return;
    }
    const isEduOrder = data.items[0].goodsType === 31;
    openPriceDialog({
      orderNo: data.orderNo,
      callback: this.refreshOrder,
      isCrossBorder: this.state.isCrossBorder,
      isEduOrder,
    });
  };

  // 报名信息
  handleRegisterInfo(data: IFormattedOrderListItem) {
    let infoData = {} as IInfoData;
    let customInfo = [] as ICustomInfoItem[];
    const studentInfo = get(data, 'tcExtra.EDU_STUDENT_INFO', '{}');
    const customStudentInfo = get(data, 'customInfoMap.edu_student_info', '[]');

    try {
      infoData = JSON.parse(studentInfo) || {};
      customInfo = JSON.parse(customStudentInfo) || [];
    } catch (err) {
      Notify.error('解析报名信息失败');
      return false;
    }

    openStudentInfoDialog(infoData, customInfo);
  }

  handleCancelAutoCall = () => {
    const { data } = this.props;
    const orderNo = data ? data.orderNo : '';
    api
      .cancelAutoCall({
        kdtId: window._global.kdtId,
        orderNo,
      })
      .then((res) => {
        if (res) {
          this.refreshOrder();
        }
      })
      .catch((msg) => {
        Notify.error(msg);
      });
  };

  renderInvoiceInfo() {
    const { data } = this.props;
    if (!data.tcExtra || !data.tcExtra.invoice) {
      return null;
    }
    const { invoice } = data.tcExtra;
    const {
      emailList,
      taxpayerId,
      userName,
      raiseType,
      invoiceType,
      openingBankName,
      bankAccount,
      address,
      phone,
    } = invoice;
    const invoiceTypeText =
      invoiceType === InvoiceTypeEnum.Dedicated ? '增值税专用发票' : '普通发票';
    return (
      <div className="info-cell">
        <div className="info-label">申请开票：</div>
        <div className="info-detail">
          发票类型：
          {invoiceTypeText + '，'}
          发票抬头：
          {userName}
          {'，'}
          {raiseType === 'enterprise' && `企业税号：${taxpayerId}，`}
          {!!openingBankName && `开户银行：${openingBankName}，`}
          {!!bankAccount && `银行账号：${bankAccount}，`}
          {!!address && `企业地址：${address}，`}
          {!!phone && `企业电话：${phone}，`}
          {emailList && emailList.length >= 0 && `收票邮箱：${emailList[0]}`}
          {isPureWscSingleStore &&
            (userName.includes('*') ||
              (emailList && emailList.length >= 0 && emailList[0].includes('*'))) && (
              <BlurredQueryButton
                style={{ marginLeft: 6 }}
                version={ComponentVersion.OptimizedPrivacy}
                clickMode={ClickMode.Fetch}
                searchKey={[
                  mainOrderInfoKeyMap.inVoiceUserName,
                  mainOrderInfoKeyMap.inVoiceEmailList,
                  mainOrderInfoKeyMap.inVoiceOpeningBankName,
                  mainOrderInfoKeyMap.inVoiceBankAccount,
                  mainOrderInfoKeyMap.inVoiceAddress,
                  mainOrderInfoKeyMap.inVoicePhone,
                ]}
                orderNo={data.orderNo}
                onShowMore={(value) => {
                  if (value?.length > 0) {
                    const res = { ...data };
                    const inVoiceUserName = value.find(
                      (item) => item.key === mainOrderInfoKeyMap.inVoiceUserName.key
                    );
                    const inVoiceEmailList = value.find(
                      (item) => item.key === mainOrderInfoKeyMap.inVoiceEmailList.key
                    );
                    const inVoiceOpeningBankName = value.find(
                      (item) => item.key === mainOrderInfoKeyMap.inVoiceOpeningBankName.key
                    );
                    const inVoiceBankAccount = value.find(
                      (item) => item.key === mainOrderInfoKeyMap.inVoiceBankAccount.key
                    );
                    const inVoiceAddress = value.find(
                      (item) => item.key === mainOrderInfoKeyMap.inVoiceAddress.key
                    );
                    const inVoicePhone = value.find(
                      (item) => item.key === mainOrderInfoKeyMap.inVoicePhone.key
                    );

                    res.tcExtra = {
                      ...res.tcExtra,
                      // @ts-ignore
                      invoice: {
                        ...res.tcExtra.invoice,
                        userName: inVoiceUserName?.value || '',
                        emailList: [inVoiceEmailList?.value || ''],
                        openingBankName: inVoiceOpeningBankName?.value || '',
                        bankAccount: inVoiceBankAccount?.value || '',
                        address: inVoiceAddress?.value || '',
                        phone: inVoicePhone?.value || '',
                      },
                    };
                    this.updateOrderListItem(res);
                  }
                }}
              />
            )}
        </div>
      </div>
    );
  }

  renderCloudColumn({ data, key, index }) {
    const { orderNo, tcExtra } = data;
    const { custom_tags: customTags } = tcExtra || {};
    return (
      <td key={`cloud-column-${key}-${orderNo}-${index}`} className="cloud-column-cell">
        <ErrorBoundary onError={(err) => console.log(err)}>
          <CloudSlot
            cloudKey="table-column"
            index={index}
            name={key}
            data={{ orderNo, customTags }}
          />
        </ErrorBoundary>
      </td>
    );
  }

  render() {
    const { data, selected, dynamicTable, mergedColumns } = this.props;
    const { loading } = this.state;
    const isShowAutoBar = get(data, 'deliveryAutomaticThirdCall.isShowAutoBar', false);
    const invoice = get(data, 'tcExtra.INVOICE');

    const attrQttOrderExport = get(data, 'tcExtra.ATTR_QTT_ORDER_EXPORT');
    let founderToMemberMark = '';
    try {
      founderToMemberMark = attrQttOrderExport
        ? JSON.parse(attrQttOrderExport).founderToMemberMark
        : '';
    } catch (error) {}

    return (
      <div className="list-item">
        <BlockLoading loading={loading}>
          <ListItemHeader
            dynamicTable={dynamicTable}
            data={data}
            selected={selected}
            refresh={this.refreshOrder}
          />
          <div className="list-item-body">
            <table className="list-item-table">
              <tbody className="list-item-table-body">
                {(data.items || []).map((goods, goodsIndex) => {
                  // 酒店商品入住多个晚，只显示一个商品
                  if (this.state.isHotel && goodsIndex !== 0) {
                    return null;
                  }
                  return (
                    <tr className="list-item-row" key={goodsIndex}>
                      {/* 商品 */}
                      {this.renderGoodsItem(data, goods)}

                      {mergedColumns.map((column, index) => {
                        if (column.key?.includes('cloud')) {
                          return this.renderCloudColumn({ data, key: column.key, index });
                        }

                        switch (column.key) {
                          /* 售后 */
                          case 'feedback':
                            return this.renderAftermarket(data, goods, goodsIndex);
                          /* 实付金额 */
                          case 'receivePay':
                            return this.renderPayPriceCell(data, goodsIndex);
                          /* 买家/收货人 */
                          case 'customer':
                            return this.renderCustomerCell(data, goodsIndex);
                          /* 配送方式 */
                          case 'expressType':
                            return this.renderExpressWay(data, goodsIndex);
                          /* 订单状态 */
                          case 'orderState':
                            return this.renderStateCell(data, goods, goodsIndex);
                          /** 订单发货时间 */
                          case 'expressTime':
                            return this.renderExpressTime(data, goodsIndex, column);
                          /** 收货地址 */
                          case 'addressDetail':
                            return this.renderAddressDetail(data, goodsIndex, column);
                          /** 自提时间 */
                          case 'selfFetchTime':
                            return this.renderSelfFetchTime(data, goodsIndex, column);
                          /** 商品分组 */
                          case 'goodsGroupTitles':
                            return this.renderGoodsGroupTitles(data, goods, column);
                          /** 商品金额合计 */
                          case 'goodsTotalPrice':
                            return this.renderGoodsTotalPrice(data, goods, column);
                          /** 商品抵用积分数 */
                          case 'goodsPointsPrice':
                            return this.renderGoodsPointsPrice(data, goods, column);
                          /** 商品实际成交金额 */
                          case 'goodsRealPay':
                            return this.renderGoodsRealPay(data, goods, column);
                          /** 商品留言 */
                          case 'goodsMessage':
                            return this.renderGoodsMessage(data, goods, column);
                          /** 储值/礼品卡支付金额 */
                          case 'deductionPay':
                            return this.renderDeductionPay(data, goodsIndex, column);
                          /** 外部支付方式 */
                          case 'outsidePayWayName':
                            return this.renderOutsidePayWayName(data, goodsIndex, column);
                          /** 外部收银单号 */
                          case 'outsidePayAcquireNo':
                            return this.renderOutsidePayAcquireNo(data, goodsIndex, column);
                          /** 群团团帮卖+专属佣金 */
                          case 'qttPromotionFee':
                            return this.renderQttPromotionFee(data, goodsIndex, column);
                          /** 群团团佣金结算状态 */
                          case 'qttSettleStatus':
                            return this.renderQttSettleStatus(data, goodsIndex, column);
                        }
                      })}

                      {/* 操作 */}
                      {this.renderOperations(data, goods, goodsIndex)}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          <table className="list-item-footer">
            {/* 买家备注/发票信息 */}
            {((dynamicTable.has('buyerMsg') && data.buyerMsg) ||
              (dynamicTable.has('invoice') && invoice)) && (
              <tr className="order-basic-info">
                <td>
                  {dynamicTable.has('buyerMsg') && data.buyerMsg && (
                    <div className="info-cell">
                      <div className="info-label">买家备注：</div>
                      <div className="info-detail">{data.buyerMsg}</div>
                    </div>
                  )}
                  {dynamicTable.has('invoice') && invoice && this.renderInvoiceInfo()}
                </td>
              </tr>
            )}
            {/* 卖家备注 */}
            {dynamicTable.has('remark') && data.remark && (
              <tr className="order-basic-info warning-bg">
                <td>
                  {data.remark && (
                    <div className="info-cell">
                      <div className="info-label">卖家备注：</div>
                      {/* 卖家备注内容需要支持换行 */}
                      <div className="info-detail">{data.remark}</div>
                    </div>
                  )}
                </td>
              </tr>
            )}
            {/* 团长备注 */}
            {founderToMemberMark && (
              <tr className="order-basic-info warning-bg2">
                <td>
                  {founderToMemberMark && (
                    <div className="info-cell">
                      <div className="info-label">团长备注：</div>
                      {/* 团长备注内容需要支持换行 */}
                      <div className="info-detail">{founderToMemberMark}</div>
                    </div>
                  )}
                </td>
              </tr>
            )}
            {/* 同城配送订单展示 */}
            {+data.expressType === 2 && data.deliveryList && data.deliveryList.length > 0 && (
              <tr className="delivery-row">
                <td>
                  {data.deliveryList.map((deliveryListItem, idx) => {
                    return (
                      <DeliveryBar
                        key={idx}
                        deliveryInfo={deliveryListItem}
                        orderNo={data.orderNo}
                        callback={this.afterDeliveryCallback}
                        tips={this.props.tips}
                        type="v2"
                      />
                    );
                  })}
                </td>
              </tr>
            )}
            {isShowAutoBar && (
              <tr className="delivery-row">
                <td>
                  <AutomaticCallBar
                    autoCallInfo={data.deliveryAutomaticThirdCall}
                    onCancel={this.handleCancelAutoCall}
                    type="v2"
                  />
                </td>
              </tr>
            )}
            {+data.refundType === 11 ? (
              <tr className="remark-row">
                <td>{`该订单已改价至 ${data.realPay} 元，为避免商家资损，请联系买家重新下单。`}</td>
              </tr>
            ) : null}
          </table>
        </BlockLoading>
      </div>
    );
  }
}

export default withRouter(ListItem);
