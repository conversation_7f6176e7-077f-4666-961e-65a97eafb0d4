import * as React from 'react';
import { Notify, BlockLoading, ErrorBoundary } from 'zent';
import { cloud, CloudCtx } from '@youzan/ranta-cloud-react';
import { Loader } from '@youzan/content-loader-react';
import args from '@youzan/utils/url/args';
import { isOfflineAdmin, storeId } from 'constants/role';
import { IOrderDetailParam } from 'definitions/order/detail';
import get from 'lodash/get';

import { OrderContextProvider, IOrderContext } from './order-context';

import OrderBasicInfo from './components/order-basic';
import OrderState from './components/order-state';
import OrderInfo from './components/order-info';
import ChildOrderList from './components/child-order-list';
import GiftChildOrderList from './components/gift-child-order-list';
import GoodsList from './components/goods-list';
import SaleInfo from './components/sale-info';
import OrderExpress from './components/order-express';
import VirtualTicket from './components/virtual-ticket';
import QttGroupInfo from './components/qtt-group-info';
import OrderPeerpayList from './components/order-peerpay-list';
import { IOrder, IEduOrderDetail, IQttOrderDetail } from './models';
import {
  getOrderDetail,
  getEduDetailByOrderNo,
  getGiveawayByOrderNo,
  getOrderDetailForPc,
} from './api';
import {
  isGiftGroupOrder,
  isVirtualTicket,
  isKnowledgeGiftOrder,
  isEduOrder,
} from './common/helper';

import './style.scss';
import SalesmanInfo from './components/salesman-info';
import { isQttOrder } from '../common/helper';
import { isFenxiao } from './common/helper';

interface IState {
  orderInfo: IOrder;
  eduOrderInfo: IEduOrderDetail;
  qttOrderInfo: IQttOrderDetail;
  giveaway: IOrderContext['giveaway'];
  refundInfo: any[];
  loading: boolean;
  isFetching: boolean;
  isCheckWxRefund: boolean;
}

/**
 * 订单状态: 1 已下单 10 待支付 20 已支付 30 待接单 40 已接单 50 待发货 60 已发货 70 待收货 80 已收货 99 已关闭 100 已完成
 */
enum OrderStateEnum {
  CREATED = 1, // 已下单
  WAIT_PAY = 10, // 待支付
  PAID = 20, // 已支付
  WAIT_CONFIRM = 30, // 待接单
  CONFIRMED = 40, // 已接单
  WAIT_SHIPPED = 50, // 待发货
  SHIPPED = 60, // 已发货
  WAIT_RECEIVED = 70, // 待收货
  RECEIVED = 80, // 已收货
  CLOSED = 99, // 已关闭
  SUCCESS = 100, // 已完成
}

interface OrderDetail {
  /** 订单编号 */
  orderNo: string;
  /** 订单状态 */
  orderState: OrderStateEnum;
  /** 订单状态文案 */
  orderStateTitle: string;
}

@cloud()
class App extends React.Component<{}, IState> {
  ctx!: CloudCtx<App>;

  public state: IState = {
    orderInfo: {} as IOrder,
    eduOrderInfo: {} as IEduOrderDetail,
    qttOrderInfo: {} as IQttOrderDetail,
    refundInfo: [],
    loading: true,
    giveaway: [],
    isFetching: true,
    isCheckWxRefund: true,
  };

  @cloud('orderDetail', 'data')
  orderDetail!: OrderDetail;

  initCloudData(orderInfo: IOrder) {
    this.orderDetail = {
      orderNo: orderInfo.orderNo,
      orderState: orderInfo.newOrderState as OrderStateEnum,
      orderStateTitle: orderInfo.stateStr,
    };
  }

  componentDidMount() {
    this._fetch();
    // 性能埋点
    window.mark?.log();
  }

  // 获取订单详情数据
  _fetch = (needLoading = true) => {
    const orderNo = args.get('orderNo', window.location.href) || '';
    const params: IOrderDetailParam = { orderNo };

    /**
     * 网店管理员的 storeId
     * 网点管理员只能查询自己网点的订单
     */
    if (isOfflineAdmin) {
      params.storeId = storeId;
    }

    if (_global.isYZEdu) {
      // 如果是有赞教育，查询订单详情需要额外查询自定义资料项来渲染学员信息
      params.withCustomInfo = true;
    }

    if (needLoading) {
      this.setState({ isFetching: true });
    }

    // 子组件使用了reload.then()
    return Promise.all([getOrderDetail(params)])
      .then(([orderInfo]) => {
        this.setState({
          orderInfo,
        });

        this.initCloudData(orderInfo);

        this._eduDetailfetchCheck(orderInfo, orderNo);
        isQttOrder(orderInfo) && this._qttDetailFetch(orderInfo, orderNo);
      })
      .catch(msg => {
        Notify.error(msg);
      })
      .finally(() => this.setState({ loading: false, isFetching: false }));
  };

  // 主商品是课程商品或者赠品，额外调用接口查询更多数据
  _eduDetailfetchCheck = (orderInfo: IOrder, orderNo: any) => {
    if (isEduOrder(orderInfo)) {
      const emptyPromise = Promise.resolve({});
      return Promise.all([
        getEduDetailByOrderNo({ orderNo }),
        _global.isYZEdu
          ? getGiveawayByOrderNo({ orderNo, chainStoreKdtId: orderInfo.kdtId }).catch(msg =>
              Notify.error(msg),
            )
          : emptyPromise,
      ]).then(([eduOrderInfo, giveaway]) => {
        this.setState({ eduOrderInfo, giveaway });
        // 如果是默认值7天，不做处理，依然使用原数据 @村长
        if (get(eduOrderInfo, 'autoSuccessDays') === 7) {
          return;
        } else {
          orderInfo.orderSuccessTime = get(eduOrderInfo, 'autoSuccessDays', -1);
          this.setState({
            orderInfo,
          });
        }
      });
    }
  };

  // 群团团订单信息
  _qttDetailFetch = (_, orderNo: any) => {
    getOrderDetailForPc({ orderNo }).then(res => {
      this.setState({
        qttOrderInfo: res,
      });
    });
  };

  render() {
    const {
      orderInfo,
      eduOrderInfo,
      qttOrderInfo,
      giveaway,
      loading,
      isFetching,
      isCheckWxRefund,
    } = this.state;
    if (loading) {
      return <BlockLoading loading />;
    }

    return (
      <OrderContextProvider
        value={{
          orderInfo,
          giveaway,
          eduOrderInfo,
          qttOrderInfo,
          reload: this._fetch,
          isCheckWxRefund,
          updateIsCheckWxRefund: isCheckWxRefund => this.setState({ isCheckWxRefund }),
        }}
      >
        <BlockLoading loading={isFetching}>
          <div className="basic-state-area">
            <OrderBasicInfo />
            <OrderState />
          </div>
          {/* 物流包裹信息 */}
          {/* 非固定展示组件 布局信息存放在组件内 */}
          {!isVirtualTicket(orderInfo) && <OrderExpress />}
          <div className="order-info-area">
            <div className="order-info-area_title">订单明细</div>
            <OrderInfo />
          </div>
          {/* 群团团订单团购信息 */}
          {!isFenxiao(orderInfo) && isQttOrder(orderInfo) && qttOrderInfo.note && <QttGroupInfo />}
          <OrderPeerpayList />
          {/* 知识付费送礼订单不展示子订单列表 */}
          {!isKnowledgeGiftOrder(orderInfo) && <ChildOrderList />}
          {/* 我要送礼社群版显示独立的子订单列表 */}
          {isGiftGroupOrder(orderInfo) && <GiftChildOrderList />}
          <SalesmanInfo orderInfo={orderInfo} />
          <div className="goods-info-area">
            <div className="goods-info-area_title">商品信息</div>
            <GoodsList />
            <SaleInfo />
          </div>
          {isVirtualTicket(orderInfo) && (
            <ErrorBoundary>
              <div className="virtual-ticket-area">
                <VirtualTicket />
              </div>
            </ErrorBoundary>
          )}
          {/* 开店增值礼包-转付费弹窗 */}
          <Loader url="/v4/assets/output" content="security/shop-open-gift" props={{ pageSource: 'order-detail' }} />
          {/* 开店礼包二期弹窗 */}
          <Loader
            url="/v4/assets/output"
            content="security/guarantee-subsidy-ad-dialog"
            props={{
              sceneNo: 'ORDER_DETAIL_POPUP',
            }}
          />
        </BlockLoading>
      </OrderContextProvider>
    );
  }
}

export default App;
