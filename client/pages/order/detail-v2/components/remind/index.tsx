import * as React from 'react';
import map from 'lodash/map';
import get from 'lodash/get';
import { isXhsLocalLifeOrder } from 'fns/order-helper';
import { BlankLink } from '@youzan/react-components';
import EduOrderTips from '../edu-order-alert/index';

import { IOrder } from '../../models';
import { toTuan, toConfirm, toSend, isSend, isCancel, isSuccess } from '../../common/constants';
import {
  isHotelOrder,
  isTCSOrder,
  getEduGoodsType,
  isEduOrder,
  isEduOfflineOrder,
  isKuaishouOrder,
  isHuijuOrder,
  isGuangWxChannelOrder,
} from '../../common/helper';

import s from './style.m.scss';

export interface IProps {
  orderInfo: IOrder;
  eduOrderInfo: any;
}

class Remind extends React.Component<IProps> {
  // 教育附加提醒信息
  renderEduTips() {
    const { orderInfo, eduOrderInfo } = this.props;
    // 如果不是教育订单
    if (!isEduOrder(orderInfo)) {
      return null;
    }

    // 转课提醒
    const orderAttr = get(orderInfo, 'tcExtra.BIZ_ORDER_ATTRIBUTE');
    const transferOrderNo = JSON.parse(get(eduOrderInfo, 'extInfo.transferOutOrder', '[]'));
    if (!orderAttr) {
      return null;
    }
    if (transferOrderNo[0]) {
      return (
        <div className={s.tips}>
          <span className={s.orange}>转课提醒：</span>
          <ul className={s.gray}>
            <li>当前转课订单中的课时或是有效期是从以下订单中转出：</li>
            <li>
              订单号：
              <ul style={{ marginLeft: '60px', marginTop: '-20px' }}>
                {transferOrderNo.map(item => (
                  <li key={item}>
                    <a
                      href={'/v4/trade/order/detail?orderNo=' + item}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {item}
                    </a>
                  </li>
                ))}
              </ul>
            </li>
          </ul>
        </div>
      );
    }

    // 教育赠品订单
    let presentOrderNoList = [];
    try {
      const buyGivePresentOrder = get(eduOrderInfo, 'extInfo.buyGivePresentOrder');
      if (buyGivePresentOrder) {
        const tempOrder = JSON.parse(buyGivePresentOrder);
        presentOrderNoList = tempOrder.presentOrderNoList;
      }
    } catch (error) {
      // do nothing
    }

    // 全部实物赠品
    const presents = orderInfo.items.filter(item => item.isPresent && item.goodsType !== 31);

    if (presents && presents.length > 0) {
      // 实物赠品全部被领取
      const allFinished = presents.length > 0 && presents.length === presentOrderNoList.length;
      // 已领取的实物赠品数量
      const finishedLength = presentOrderNoList.length;
      // 未领取的实物赠品数量
      const unfinishedLength = presents.length - presentOrderNoList.length;

      return (
        <div className={s.eduTips}>
          <span className={s.orange}>赠品提醒：</span>
          <ul className={s.gray}>
            <li>
              {allFinished
                ? `当前订单的实物赠品已生成${finishedLength}笔订单：`
                : `该笔订单还有${unfinishedLength}个实物赠品未被买家领取，领取成功后会生成相应的赠品订单。`}
            </li>
            {presentOrderNoList.map((txt, id) => (
              <li key={id}>
                订单号：
                <a
                  href={'/v4/trade/order/detail?orderNo=' + txt}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {txt}
                </a>
              </li>
            ))}
          </ul>
        </div>
      );
    }

    return null;
  }

  // 有赞提醒 copy 老代码
  renderYzTips() {
    const { orderInfo } = this.props;
    const { buyWay, expressType, orderState, state } = orderInfo;

    let remindText;
    const isHotel = isHotelOrder(orderInfo);
    const isToConfirm = orderState === toConfirm; // 待确认/待接单
    const isToSend = orderState === toSend; // 待发货
    const isSendState = orderState === isSend; // 已发货
    const isCancelState = orderState === isCancel; // 已关闭
    const isSuccessState = orderState === isSuccess; // 交易成功
    const isTotuan = orderState === toTuan; // 待成团
    const isTCS = isTCSOrder(orderInfo); // 同城送
    const isEduGoods = isEduOrder(orderInfo);
    const eduGoodsType = getEduGoodsType(get(orderInfo, 'items[0]'), orderInfo);
    const isSingleGoods = orderInfo.items.length === 1;

    // 交易关闭和待成团不展示提醒
    if (isCancelState || isTotuan) {
      return null;
    }

    /**
     * 1: 订单创建
     * 2：待填收货地址
     * 3：待付款
     * 4：待系统确认
     */
    if ([1, 2, 3, 4].indexOf(state) !== -1) {
      remindText = ['请务必等待订单状态变更为“买家已付款，等待卖家发货”后再进行发货。'];
      if (isHotel) {
        remindText = ['买家付款后，需商家确认接单才算预订成功。'];
      }
    }

    // 同城待接单
    const cityWaitConfirm = isToConfirm && isTCS;

    // 待发货/带接单
    if (isToSend || cityWaitConfirm) {
      let waitDeliverText;
      let expressWayText;
      let afterPayText;

      switch (buyWay) {
        case 9:
          if (expressType > 0) {
            waitDeliverText = '到店付款订单，均需商家自行线下向买家收款，有赞不参与结算；';
          } else {
            waitDeliverText = '货到付款订单，均需商家自行线下向买家收款，有赞不参与结算；';
          }
          break;
        default:
          if (expressType > 0) {
            waitDeliverText = '如果买家无法到店提货，请及时与买家联系并说明情况并及时退款；';
          } else {
            waitDeliverText = '如果无法发货，请及时与买家联系并说明情况后进行退款；';
          }
      }

      if (expressType > 0) {
        expressWayText = '你也可以跟买家协商，征得买家同意后，手动发货；';
      } else {
        if (isHotel) {
          expressWayText = '买家申请退款后，须征得买家同意后再发货，否则买家有权拒收；';
        } else {
          expressWayText = '买家申请退款后，须征得买家同意后再发货，否则买家有权拒收货物；';
        }
      }

      // 微信自有支付
      if (buyWay === 1) {
        afterPayText = (
          <span>
            微信支付－自有订单，请到“
            <BlankLink href="//pay.weixin.qq.com">微信商家平台</BlankLink>
            ”进行退款后，再进行标记退款；
          </span>
        );
      } else {
        afterPayText = '买家付款后超过7天仍未发货，将有权申请有赞客服介入发起退款维权；';
      }

      // 对酒店商品订单特殊处理
      if (isHotel) {
        // waitDeliverText = '如果无法接单，请及时与买家联系并说明情况后进行退款；';
        waitDeliverText = '买家付款后，需商家确认接单才算预订成功。';
        expressWayText = expressWayText.replace('发货', '接单');
        if (buyWay !== 1) {
          afterPayText = afterPayText.replace('发货', '接单');
        }
      }

      remindText = [waitDeliverText, expressWayText, afterPayText];
    }

    // 已发货
    if (isSendState) {
      let alreadyDeliverText;
      switch (buyWay) {
        case 9:
          if (expressType > 0) {
            alreadyDeliverText = '到店付款订单，均需商家自行线下向买家收款，有赞不参与结算；';
          } else {
            alreadyDeliverText = '货到付款订单，均需商家自行线下向买家收款，有赞不参与结算；';
          }
          break;
        default:
          alreadyDeliverText = '交易成功后，有赞将把货款结算至你的店铺账户余额，你可申请提现；';
          if (isKuaishouOrder(orderInfo)) {
            alreadyDeliverText =
              '交易成功且售后处理期结束后，有赞将把货款结算至你的店铺账户余额，你可申请提现；';
          }
          if (isXhsLocalLifeOrder(orderInfo)) {
            alreadyDeliverText =
              '交易成功后，货款将直接结算至小红书本地生活后台帐户，你可前往申请提现；';
          }
      }
      if (isHotel) {
        remindText = [alreadyDeliverText];
      } else if (buyWay === 1) {
        // 已发货且为微信-自有支付的情况下
        remindText = [
          '交易成功后，请及时关注你发出的包裹状态，确保能配送至买家手中；',
          '如果买家表示未收到货或者货物有问题，请及时联系买家积极处理，友好协商；',
        ];
      } else if (isGuangWxChannelOrder(orderInfo)) {
        // 爱逛视频号订单
        remindText = [
          '应视频号侧规则，爱逛-视频号订单将在售后期结束后结算货款（一般是确认收货后8天）；',
          '请及时关注你发出的包裹状态，确保能配送至买家手中；',
          '如果买家表示未收到货或者货物有问题，请及时联系买家积极处理，友好协商；',
        ];
      } else {
        remindText = [
          alreadyDeliverText,
          '请及时关注你发出的包裹状态，确保能配送至买家手中；',
          '如果买家表示未收到货或者货物有问题，请及时联系买家积极处理，友好协商；',
        ];
      }
    }

    // 交易成功
    if (isSuccessState) {
      if (isGuangWxChannelOrder(orderInfo)) {
        // 爱逛视频号订单
        remindText = [
          '应视频号侧规则，爱逛-视频号订单将在售后期结束后结算货款（一般是确认收货后8天）；',
          '交易已成功，如果买家提出售后要求，请积极与买家协商，做好售后服务。',
        ];
      } else {
        remindText = ['交易已成功，如果买家提出售后要求，请积极与买家协商，做好售后服务。'];
      }
    }

    // 教育订单
    if (isEduGoods) {
      if (isEduOfflineOrder(orderInfo)) {
        remindText = ['线下报名订单，支付完成后，订单状态变为已完成。'];
      } else if (isSingleGoods && eduGoodsType === 'single') {
        remindText = ['在线课程订单，支持单卖的图文、音频、视频，买家付款后订单变为已完成。'];
      } else if (isSingleGoods && (eduGoodsType === 'live' || eduGoodsType === 'column')) {
        remindText = [
          '交易成功后，有赞将把货款结算至你的店铺账户余额，你可申请提现；',
          '已付款的状态最少为7天，7天后订单状态会自动变为交易成功。',
        ];
      } else if (isSingleGoods && eduGoodsType === 'offline') {
        remindText = [
          '交易成功后，有赞将把货款结算至你的店铺账户余额，你可申请提现；',
          '已付款的状态最多为7天，如是体验课买家线下签到消课后，订单状态变为交易成功，不签到或正式课则7天后订单状态会自动变为交易成功。',
        ];
      }
    }

    // 惠聚订单
    if (isHuijuOrder(orderInfo)) {
      remindText = [
        '腾讯惠聚订单，请根据腾讯惠聚平台要求及时发货，处理售后',
        '腾讯惠聚订单，待发货状态下，买家退款申请不支持直接拒绝，但可以通过发货关闭退款申请',
      ];
    }

    return (
      <div className={s.tips}>
        <ul className={s.gray}>
          {map(remindText, (remind, idx) => {
            return <li key={idx}>{remind}</li>;
          })}
          {/* 知识付费订单展示提示 */}
          {isEduGoods && <EduOrderTips />}
        </ul>
      </div>
    );
  }

  renderRelationOrders() {
    const { relationOrders } = this.props.orderInfo;
    return map(relationOrders, item => {
      return (
        <div>
          <span className={s.label}>订单号：</span>
          <span className={s.text}>
            <BlankLink href={`/v4/trade/order/detail?orderNo=${item}`}>{item}</BlankLink>
          </span>
        </div>
      );
    });
  }

  renderFxOrderInfo() {
    const parentOrder = get(this.props, 'orderInfo.tcExtra.PARENT_ORDER_NO', '');
    const relationOrders = get(this.props, 'orderInfo.relationOrders', []);

    if (relationOrders.length > 0 || parentOrder) {
      return (
        <div className={s.fxOrderInfoWrap}>
          {relationOrders.length > 0 && (
            <div>
              当前订单含分销商品，系统已按供货商生成{relationOrders.length}
              笔订单，买家付款后订单列表将按这{relationOrders.length}笔订单进行展示：
            </div>
          )}
          <div>
            {parentOrder && (
              <div>
                <span className={s.label}>合并单号：</span>
                <span className={s.text}>
                  <BlankLink href={`/v4/trade/order/detail?orderNo=${parentOrder}`}>
                    {parentOrder}
                  </BlankLink>
                </span>
              </div>
            )}
            {relationOrders.length > 0 && this.renderRelationOrders()}
          </div>
        </div>
      );
    }
  }

  render() {
    return (
      <div className={s.wrapper}>
        {this.renderFxOrderInfo()}
        {this.renderEduTips()}
        {this.renderYzTips()}
      </div>
    );
  }
}

export default Remind;
