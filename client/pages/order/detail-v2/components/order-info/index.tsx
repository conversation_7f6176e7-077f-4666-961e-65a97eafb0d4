import * as React from 'react';
import { Sweetalert, Pop, Notify } from 'zent';
import isNil from 'lodash/isNil';
import get from 'lodash/get';
import map from 'lodash/map';
import head from 'lodash/head';
import findIndex from 'lodash/findIndex';
import pick from 'lodash/pick';
import cx from 'classnames';
import { PopEllipsisText, BlankLink } from '@youzan/react-components';
import { isEduSingleStore, isPureWscSingleStore } from '@youzan/utils-shop';
import openWholesaleVerifyDialog from '../../../components/wholesale-verify-dialog';
import { getIsFormSmallShop } from '../../../common/wx-small-shop';
import DisableAction from '../../../components/disable-action';
import { OrderContext } from '../../order-context';
import BlurredQueryButton from 'components/order/blurred-query-button';
import { onShowRiskWarningDialog } from 'components/risk-warning-dialog';
import { ClickMode, ISearchKey } from 'components/order/blurred-query-button/type';
import {
  orderAddressInfoKeyMap,
  mainOrderInfoKeyMap,
  buyerInfoKeyMap,
} from 'components/order/blurred-query-button/key';
import copy from 'zent/es/copy-button/CopyToClipboard';
import {
  isGiftOrder,
  isHotelOrder,
  isSelfFetch,
  isVirtualTicket,
  isVirtualOrder,
  isTCSOrder,
  isFenxiao,
  isCheckoutOrder,
  isPeriodOrder,
  isImportOrder,
  isEduClassTransferOrder,
  getHotelUrl,
  isNewHotelOrder,
  isEduOfflineOrder,
  isHuijuOrder,
  isTradeModuleV3Order,
} from '../../common/helper';
import { getCustomer, isFromShopInShop } from 'fns/order-helper';
import { toPay, isCancel, EXPIRED, PAY_STATE_TO_PAY } from '../../common/constants';
import { formatContent, getExpressAddress } from './helper';
import ModifyAddressBtn from '../modify-address';
import { IPeriodOrderDetail } from '../../models';
import IdCard from '../../../components/id-card';
import QueryAddressHistory from '../query-address-history';
import s from './style.m.scss';
import { isWholesaleOfflinePayOrder, IS_WHOLESALE } from 'pages/order/common/wholesale';
import { getUserPrivacyStateWithCache } from 'components/order/blurred-query-button/api';
import Button from 'components/button';
import { isFromVideoShop } from 'fns/order-helper';
import { videoShopMsgNotify } from 'constants/video-shop';
import { InvoiceTypeEnum } from 'constants/order';
import { checkAccess } from '@youzan/sam-components';

enum FOLD_KEYS {
  FOLD_STUDENT = 'foldStudent',
}

// 抬头类型
enum RaiseTypeEnum {
  /** 个人 */
  personal = 'personal',
  /** 企业 */
  enterprise = 'enterprise',
}

// 订单状态
enum OrderStateEnum {
  /** 待发货 */
  toDelivery = 3,
  /** 待付款 */
  toPay = 5,
}

interface IState {
  foldStudent: boolean;
  isShowQueryBtn: boolean;
  receiverName: string;
  receiverTel: string;
  receiverAddressDetail: string;
  receiverIdcardName: string;
  receiverIdcardNo: string;
  selfFetchInfoName: string;
  buyerCustomer: string;
  invoiceInfoTitle: string;
  invoiceInfoEmail: string;
  inVoiceOpeningBankName: string;
  inVoiceBankAccount: string;
  inVoiceAddress: string;
  inVoicePhone: string;
}

interface IOrderInfo {
  renderContent: any;
  title: string;
  titleHint?: React.ReactNode;
  className: string;
}

interface ICustomAttributeItem {
  attributeId: number;
  attributeTitle: string;
  attributeKey: string;
  value: string;
}

const STUDENT_DETAIL_URL = '/v4/scrm/customer/manage#/student/detail';
const GENDER = ['', '男', '女'];
const QRCODE_SCENE = 12; // 扫码订单场景值
// 买家信息key
const buyerInfoKey: ISearchKey[] = [buyerInfoKeyMap.customer];
// 发票信息key
const invoiceTitleKey: ISearchKey[] = [mainOrderInfoKeyMap.inVoiceUserName];
const invoiceOpeningBankNameKey: ISearchKey[] = [mainOrderInfoKeyMap.inVoiceOpeningBankName];
const invoiceBankAccountKey: ISearchKey[] = [mainOrderInfoKeyMap.inVoiceBankAccount];
const invoiceAddressKey: ISearchKey[] = [mainOrderInfoKeyMap.inVoiceAddress];
const invoicePhoneKey: ISearchKey[] = [mainOrderInfoKeyMap.inVoicePhone];
// 发票信息key
const invoiceEmailKey: ISearchKey[] = [mainOrderInfoKeyMap.inVoiceEmailList];
// 收货地址信息key
const receiverInfoKey: ISearchKey[] = [
  orderAddressInfoKeyMap.userName,
  orderAddressInfoKeyMap.tel,
  orderAddressInfoKeyMap.addressDetail,
  mainOrderInfoKeyMap.selfFetchUserName,
  mainOrderInfoKeyMap.idCardName,
  mainOrderInfoKeyMap.idCardNo,
];
// 微商城单店支持个人隐私优化样式
// 其他店铺类型不支持
const blurredBtnProps = isPureWscSingleStore
  ? { clickMode: ClickMode.Fetch, style: { marginLeft: 6 }, version: 1 }
  : { text: '查看' };
// 埋点ß
const logger = window.Logger;
export default class OrderInfo extends React.Component<{}, IState> {
  static contextType = OrderContext;
  context!: React.ContextType<typeof OrderContext>;

  public state: IState = {
    foldStudent: true,
    // 收货人信息明文
    isShowQueryBtn: true,
    receiverName: '',
    receiverTel: '',
    receiverAddressDetail: '',
    receiverIdcardName: '',
    receiverIdcardNo: '',
    selfFetchInfoName: '',
    // 买家信息明文
    buyerCustomer: '',
    // 发票信息明文
    invoiceInfoTitle: '',
    invoiceInfoEmail: '',
    inVoiceOpeningBankName: '',
    inVoiceBankAccount: '',
    inVoiceAddress: '',
    inVoicePhone: '',
  };

  componentDidMount = () => {
    this.initUserPrivacyStatus();
    this.initCopyListener();
  }

  componentDidUpdate = (_, prevState) => {
    const prev = get(prevState, 'isShowQueryBtn', true);
    const current = get(this.state, 'isShowQueryBtn', true);
    if (prev === current) {
      return;
    }
    this.initUserPrivacyStatus();
  };

  componentWillUnmount = () => {
    this.removeCopyListener();
  }

  initUserPrivacyStatus = () => {
    if (!isPureWscSingleStore) {
      return;
    }
    getUserPrivacyStateWithCache().then((res) => {
      const status = !+res.value;
      // true - 脱敏数据 接口/刷新获取
      if (status) {
        return;
      }
      // false - 未脱敏数据 设置复制初始值
      const { orderInfo } = this.context;
      const initState: Pick<IState, 'receiverTel' | 'receiverName'> = Object.create({});
      initState.receiverTel = orderInfo?.tel;
      initState.receiverName = orderInfo?.userName;
      this.setState({ ...initState, isShowQueryBtn: status });
    });
  };

  initCopyListener = () => {
    document.addEventListener('copy', this.handleCopy);
  };

  removeCopyListener = () => {
    document.removeEventListener('copy', this.handleCopy);
  };

  handleCopy = () => {
    const selectedText = window.getSelection()?.toString() || '';
    // 如果复制的内容包含省、市、区，则显示风险提示弹窗
    if (
      selectedText &&
      (selectedText.includes('省') || selectedText.includes('市') || selectedText.includes('区'))
    ) {
      this.beforeCopy({ copyDetail: selectedText, isShowSuccess: false });
    }
  };

  // 复制之前，判断下是否展示风险提示弹窗
  beforeCopy = ({ copyDetail, isShowSuccess = true }) => {
    const toCopy = (isCopy = true) => {
      this.removeCopyListener();

      if (isCopy) {
        setTimeout(() => {
          const isSuccess = copy(copyDetail);
          if (isShowSuccess) {
            Notify.success(isSuccess ? '复制成功' : '复制失败');
          }
        }, 100);
      }
    };

    try {
      const { orderInfo } = this.context;
      onShowRiskWarningDialog({
        orderInfo,
        onContinue: toCopy,
      });
    } catch (err) {
      toCopy();
    }
  };

  handleFoldClick = (key: FOLD_KEYS, isFold: boolean) => () => {
    const value = !isFold;
    this.setState({ [key]: value });
  };

  // 展示身份证照片
  showIdCardPhotos = (idCardInfo) => {
    const { idCardFrontPhoto, idCardBackPhoto } = idCardInfo;

    Sweetalert.alert({
      type: 'info',
      title: '身份证照片',
      closeBtn: true,
      content: (
        <div className="dialog-content__id-photo">
          {[idCardFrontPhoto, idCardBackPhoto]
            .filter((img) => !isNil(img))
            .map((url, idx) => (
              <p key={idx}>
                <img alt="身份证照片" width={600} src={url} />
              </p>
            ))}
        </div>
      ),
    });
  };

  // 身份认证信息
  getIdentify() {
    const { orderInfo } = this.context;
    const { tcExtra } = orderInfo;
    const { idCardName } = JSON.parse(get(tcExtra, 'IDENTITY_CARD', '{}'));
    const { receiverIdcardName } = this.state;
    // 实名认证通过标志， 1表示通过
    const IS_IDENTITY_VERIFY_PASSED = +get(tcExtra, 'IS_IDENTITY_VERIFY_PASSED', 0);

    if (IS_IDENTITY_VERIFY_PASSED === 1) {
      return (
        <div className={s.identityWrap}>
          <div>{receiverIdcardName || idCardName || '-'}</div>
          <Pop trigger="hover" position="bottom-center" content="实名认证成功">
            <img
              className={s.identityImg}
              src="//b.yzcdn.cn/yuanjiu/realname-check/<EMAIL>"
              width="20"
            />
          </Pop>
        </div>
      );
    } else if (idCardName) {
      return receiverIdcardName || idCardName;
    }

    return null;
  }

  // 收货人信息
  getReceiverInfo = () => {
    const { orderInfo } = this.context;
    const { userName = '', tel = '', extraInfo, items, selfFetch, tcExtra } = orderInfo;
    const {
      receiverAddressDetail,
      receiverTel,
      receiverName,
      selfFetchInfoName,
      receiverIdcardNo,
    } = this.state;

    // 送礼订单啥都不显示
    if (isGiftOrder(orderInfo)) {
      return [formatContent('', '送礼订单请查看礼单')];
    }

    // 公共信息(电话，身份证号)
    const commonInfos = [formatContent('联系电话', receiverTel || tel)];

    // 身份信息
    const idCardNameEle = this.getIdentify();
    if (idCardNameEle) {
      commonInfos.push(formatContent('身份信息', idCardNameEle));
    }

    const idCardInfo = JSON.parse(get(tcExtra, 'IDENTITY_CARD', '{}'));
    const idCardNumber = idCardInfo.idCardNumber;
    let dispalyIdCardInfo;

    //  显示身份证号（默认**显示）
    if (idCardNumber) {
      dispalyIdCardInfo = (
        <div>
          {receiverIdcardNo || idCardNumber}
          {idCardInfo && idCardInfo.idCardFrontPhoto && (
            <a className={s.pointer} onClick={() => this.showIdCardPhotos(idCardInfo)}>
              &nbsp;查看照片
            </a>
          )}
        </div>
      );
      commonInfos.push(formatContent('身份证号', dispalyIdCardInfo));
    }

    // 酒店订单
    if (isHotelOrder(orderInfo)) {
      const {
        recipients = [],
        checkInTime = '-',
        checkOutTime = '-',
      } = JSON.parse(extraInfo || '{}');
      const days = items.length;
      // 入住人
      const people = map(recipients, (item, idx) => {
        return formatContent(`入住人${idx + 1}`, item);
      });

      return [
        formatContent('房间数', `${recipients.length}`),
        ...people,
        ...commonInfos,
        formatContent('入住时间', checkInTime),
        formatContent('离店时间', `${checkOutTime} 共${days}晚`),
      ];
    }

    // 自提订单receiverName显示提货人
    if (isSelfFetch(orderInfo)) {
      return [formatContent('提货人', selfFetchInfoName || selfFetch.userName), ...commonInfos];
    }

    // 电子卡券receiverName显示联系人
    if (isVirtualTicket(orderInfo)) {
      return [formatContent('联系人', receiverName || userName), ...commonInfos];
    }

    // 没有收货人，直接不显示收货信息
    if (!userName) {
      return [formatContent('收货人', '-')];
    }

    // 收货地址
    const addressDesc = getExpressAddress(orderInfo);
    if (addressDesc.length > 0) {
      // 数组最后一个为地址详情 需要脱敏并支持查看
      addressDesc[addressDesc.length - 1] =
        receiverAddressDetail || addressDesc[addressDesc.length - 1];
      commonInfos.push(formatContent('收货地址', addressDesc.join(' ')));
    }

    return [formatContent('收货人', receiverName || userName), ...commonInfos];
  };

  // 获取配送信息
  getExpressInfo = () => {
    const { orderInfo } = this.context;
    const { expressTypeDesc, expressTime, selfFetch, deliveryTimeDisplay, periodOrderDetail } =
      orderInfo;
    // 电子卡券、酒店订单、虚拟商品不显示配送信息
    if (isVirtualTicket(orderInfo) || isHotelOrder(orderInfo) || isVirtualOrder(orderInfo)) {
      return [formatContent('配送方式', '无需配送')];
    }

    // 默认显示
    const formatExpressInfo = [
      formatContent('配送方式', expressTypeDesc ? expressTypeDesc.join(' + ') : '快递配送'),
    ];

    // 送礼订单
    if (isGiftOrder(orderInfo)) {
      return formatExpressInfo;
    }

    // 上门自提信息
    const { province = '', city = '', county = '', addressDetail = '', name, userTime } = selfFetch;

    // 同城送
    if (isTCSOrder(orderInfo)) {
      return [
        formatContent('配送方式', '同城配送'),
        formatContent('送达日期', deliveryTimeDisplay),
      ];
    }

    // 到店自提
    if (isSelfFetch(orderInfo)) {
      const selfFetchAddress = `${name} (${province}-${city}-${county}-${addressDetail}`;

      return [
        formatContent('配送方式', '到店自提'),
        formatContent('自提地点', <PopEllipsisText count={10} text={selfFetchAddress} />),
        formatContent('提货日期', userTime),
      ];
    }

    // 周期购
    if (isPeriodOrder(orderInfo) && head(periodOrderDetail)) {
      const { periodStr, totalIssue, deliverTime } = head(periodOrderDetail) as IPeriodOrderDetail;
      let expressPeriod = '-';
      if (periodStr && totalIssue && deliverTime) {
        const sendStr = deliverTime.indexOf('送达') > -1 ? '' : '送达';
        expressPeriod = `${periodStr.split(',')[0]}, ${deliverTime}${sendStr}, 共${totalIssue}期`;
      }

      return [formatContent('配送方式', '快递'), formatContent('配送周期', expressPeriod)];
    }

    expressTime && formatExpressInfo.push(formatContent('发货时间', expressTime));

    return formatExpressInfo;
  };
  // client/pages/order/detail/components
  // 新增组件  wholesale-audit-btn
  // 付款信息
  getPaymentInfo = () => {
    const { orderInfo, eduOrderInfo } = this.context;
    const {
      payPrice,
      buyWayStr,
      orderState,
      payTime,
      closeState,
      payState,
      buyWay,
      orderNo,
    } = orderInfo || {};
    const isToPay = orderState === toPay; // 待付款
    const expiredOrder = orderState === isCancel && closeState === EXPIRED;


    // 订单关闭并且买家未支付显示"应付金额"
    const payPriceLabel =
      orderState === isCancel && payState === PAY_STATE_TO_PAY ? '应付金额' : '实付金额';
    const paymentInfo: any[] = [];

    if (isToPay || expiredOrder) {
      return [formatContent('付款状态', '待付款')];
    }

    if (isWholesaleOfflinePayOrder({ buyWay })) {
      return [
        {
          label: '付款方式：',
          text: (
            <>
              <span>线下支付</span>
              <span
                className={s.checkOfflinePayInfo}
                onClick={() =>
                  openWholesaleVerifyDialog({ scene: 'view', orderNo, title: '打款详情' })
                }
              >
                查看
              </span>
            </>
          ),
        },
      ];
    }
    // 教育店铺实物赠品订单的付款方式特殊处理逻辑
    let masterOrderNo = '';
    try {
      const buyGivePresentOrder = get(eduOrderInfo, 'extInfo.buyGivePresentOrder');
      if (buyGivePresentOrder) {
        const tempOrder = JSON.parse(buyGivePresentOrder);
        masterOrderNo = tempOrder.masterOrderNo;
      }
    } catch (error) {
      // do nothing
    }

    // 实付金额
    paymentInfo.push(formatContent(payPriceLabel, payPrice || '-'));

    if (masterOrderNo) {
      paymentInfo.push(
        formatContent(
          '付款方式',
          buyWayStr || '买赠兑换',
          '/v4/trade/order/detail?orderNo=' + masterOrderNo,
          '点击可查看买家所购课程的订单详情'
        )
      );
    } else if (buyWayStr) {
      paymentInfo.push(formatContent('付款方式', buyWayStr || '-'));
    }

    if (payTime) {
      paymentInfo.push(formatContent('付款时间', payTime || '-'));
    }

    // 教育 - 报名时选择了老师，展示老师字段信息
    if (_global.isYZEdu) {
      let teacherName = '';
      try {
        const orderAttr = get(orderInfo, 'tcExtra.BIZ_ORDER_ATTRIBUTE');
        teacherName = orderAttr && get(JSON.parse(orderAttr), 'TEACHER_NAME');
        if (teacherName) {
          paymentInfo.push(formatContent('老师', teacherName));
        }
      } catch (e) {
        // do nothing
      }
    }

    return paymentInfo;
  };

  getOfflinePaymentInfo = () => {
    const baseInfo = this.getPaymentInfo();
    const { orderInfo, eduOrderInfo } = this.context;
    const cashier = get(orderInfo, 'tcExtra.CASHIER_NAME');
    const seller = get(eduOrderInfo, 'sellerName');
    const orderAttr = get(orderInfo, 'tcExtra.BIZ_ORDER_ATTRIBUTE');
    const teacherName = orderAttr && get(JSON.parse(orderAttr), 'TEACHER_NAME');
    const addition = [
      {
        label: '收银员：',
        text: cashier,
      },
      {
        label: '课程顾问：',
        text: seller,
      },
    ];

    // 如果报名时有老师字段
    if (teacherName && !baseInfo.find((item) => item.label?.includes('老师'))) {
      addition.push(formatContent('老师', teacherName));
    }

    return baseInfo.concat(addition);
  };

  // 买家信息
  getBuyerInfo = () => {
    const { orderInfo } = this.context;
    const { customerId, tcExtra, customerType, customer, orderNo } = orderInfo;
    const _isFenxiaoOrder = isFenxiao(orderInfo);
    const commonResults: Array<ReturnType<typeof formatContent> | React.ReactNode> = [];
    const { buyerCustomer } = this.state;

    // 非供货订单订单 && 非批发场景
    if (!_isFenxiaoOrder && !IS_WHOLESALE) {
      let displayBuyer;

      if (+customerId && customerType === 1) {
        displayBuyer = (
          <BlankLink
            href={`${_global.url.www}/weixin/message/talk#list&type=new&fans_id=${customerId}`}
          >
            {buyerCustomer || customer || ''}
          </BlankLink>
        );
      } else {
        displayBuyer = buyerCustomer || customer || '';
      }

      // 加上查看按钮
      const buyerElement = !isHuijuOrder(orderInfo) && (
        <>
          {displayBuyer}
          <BlurredQueryButton
            {...blurredBtnProps}
            orderNo={orderNo}
            searchKey={buyerInfoKey}
            onShowMore={(data) => {
              this.setPlainText(data);
            }}
          />
        </>
      );
      commonResults.push(formatContent('买家', buyerElement || '匿名用户'));
    }

    try {
      const BIZ_ORDER_ATTRIBUTE = get(tcExtra, 'BIZ_ORDER_ATTRIBUTE', '{}');
      const bizOrderAttribute = JSON.parse(BIZ_ORDER_ATTRIBUTE);
      const { SALESMAN_MSG = '{}' } = bizOrderAttribute;
      const salesmanMsg = JSON.parse(SALESMAN_MSG);
      if (salesmanMsg && salesmanMsg.name && _global.showSalesman) {
        commonResults.push(formatContent('分销员', salesmanMsg.name));
      }
    } catch (error) {
      console.log('解析错误', error);
    }
    
    // 批发场景展示 批发商姓名，批发商等级
    if (IS_WHOLESALE) {
      const bizOrderAttribute = JSON.parse(get(orderInfo, 'tcExtra.BIZ_ORDER_ATTRIBUTE', '{}'));
      const wholesaleSnapshot = JSON.parse(get(bizOrderAttribute, 'WHOLESALE_SNAPSHOT', '{}'));
      const { name = '', levelName = '' } = wholesaleSnapshot;
      commonResults.push(formatContent('姓名', name));
      commonResults.push(formatContent('等级', levelName));
    }
    // 分销供货订单
    if (_isFenxiaoOrder) {
      const fxDisplayBuyer = (
        <span>
          {getCustomer(orderInfo)}&nbsp;
          <BlankLink href={`https://b-im.youzan.com/#/?userId=${customerId}&registerType=fenxiao`}>
            在线联系
          </BlankLink>
        </span>
      );
      commonResults.push(formatContent('分销商', fxDisplayBuyer || '匿名用户'));
    }

    return commonResults;
  };

  renderInvoiceField = ({ searchKey, text }) => {
    const { orderInfo } = this.context;
    const { orderNo } = orderInfo;
    return (
      <span>
        {text}
        <BlurredQueryButton
          {...blurredBtnProps}
          orderNo={orderNo}
          searchKey={searchKey}
          onShowMore={(data) => {
            this.setPlainText(data);
          }}
        />
        &nbsp;&nbsp;
      </span>
    );
  };

  // 获取发票信息
  getInvoiceInfo = () => {
    const { orderInfo } = this.context;
    const { orderNo } = orderInfo;
    const {
      invoiceInfoEmail,
      invoiceInfoTitle,
      inVoiceOpeningBankName,
      inVoiceBankAccount,
      inVoiceAddress,
      inVoicePhone,
    } = this.state;
    const invoiceInfo = [];
    // @ts-ignore
    const invoice = JSON.parse(get(orderInfo, 'tcExtra.INVOICE', '{}'));
    const {
      userName,
      raiseType,
      taxpayerId,
      emailList,
      invoiceType,
      openingBankName,
      bankAccount,
      address,
      phone,
    } = invoice;
    const isEnterprise = raiseType === RaiseTypeEnum.enterprise; // 是否为企业
    if (invoiceType) {
      invoiceInfo.push(
        formatContent(
          '发票类型',
          invoiceType === InvoiceTypeEnum.Dedicated ? '增值税专用发票' : '普通发票'
        ) as never
      );
    }

    const invoiceTitle = (
      <span>
        {invoiceInfoTitle || userName}
        {!isEnterprise && (
          <BlurredQueryButton
            {...blurredBtnProps}
            orderNo={orderNo}
            searchKey={invoiceTitleKey}
            onShowMore={(data) => {
              this.setPlainText(data);
            }}
          />
        )}
        &nbsp;&nbsp;
      </span>
    );

    invoiceInfo.push(formatContent('发票抬头', invoiceTitle) as never);

    if (isEnterprise) {
      invoiceInfo.push(formatContent('企业税号', taxpayerId) as never);
    }

    if (openingBankName) {
      invoiceInfo.push(
        formatContent(
          '开户银行',
          this.renderInvoiceField({
            searchKey: invoiceOpeningBankNameKey,
            text: inVoiceOpeningBankName || openingBankName,
          })
        ) as never
      );
    }
    if (bankAccount) {
      invoiceInfo.push(
        formatContent(
          // eslint-disable-next-line youzan/youzan-standard-words
          '银行账号',
          this.renderInvoiceField({
            searchKey: invoiceBankAccountKey,
            text: inVoiceBankAccount || bankAccount,
          })
        ) as never
      );
    }
    if (address) {
      invoiceInfo.push(
        formatContent(
          '企业地址',
          this.renderInvoiceField({
            searchKey: invoiceAddressKey,
            text: inVoiceAddress || address,
          })
        ) as never
      );
    }

    if (phone) {
      invoiceInfo.push(
        formatContent(
          '企业电话',
          this.renderInvoiceField({
            searchKey: invoicePhoneKey,
            text: inVoicePhone || phone,
          })
        ) as never
      );
    }

    // 加上查看按钮
    const emailElement = (
      <span>
        {invoiceInfoEmail || emailList[0]}
        <BlurredQueryButton
          {...blurredBtnProps}
          orderNo={orderNo}
          searchKey={invoiceEmailKey}
          onShowMore={(data) => {
            this.setPlainText(data);
          }}
        />
      </span>
    );
    if (emailList && emailList.length >= 0) {
      invoiceInfo.push(formatContent('收票邮箱', emailElement) as never);
    }

    return invoiceInfo;
  };

  getEduInfo(): { eduInfo: { [key: string]: any }; customInfo: any[] } {
    const { orderInfo } = this.context;
    return {
      eduInfo: JSON.parse(get(orderInfo, 'tcExtra.EDU_STUDENT_INFO', '{}')),
      customInfo: JSON.parse(get(orderInfo, 'customInfoMap.edu_student_info', '[]')),
    };
  }

  // 教育-上课信息
  getClassInfo = () => {
    const { eduInfo } = this.getEduInfo();
    const courseTime = get(eduInfo, 'courseAttend.courseTime');
    const address = get(eduInfo, 'courseAttend.address');
    const classInfo = [];
    const addressText = isEduSingleStore ? '上课地点' : '意向上课校区';

    classInfo.push(formatContent('上课时间', courseTime) as never);
    classInfo.push(formatContent(addressText, address) as never);

    return classInfo;
  };

  // 教育-学员信息
  getStudentInfo = () => {
    const { foldStudent } = this.state;
    const { eduInfo, customInfo } = this.getEduInfo();
    const {
      alias,
      name,
      gender,
      bornDate,
      grade,
      phoneNumber,
      wechatAccount,
      address,
      id,
      version,
    } = eduInfo;
    let studentInfo: Array<{ label?: string; text: React.ReactNode } | null> = [];
    if (!customInfo.length) {
      studentInfo.push(
        formatContent(
          '学员姓名',
          <BlankLink href={`${STUDENT_DETAIL_URL}/${alias}`}>{name}</BlankLink>
        ) as never
      );
      studentInfo.push(formatContent('性别', GENDER[gender]) as never);
      studentInfo.push(formatContent('生日', bornDate) as never);
      studentInfo.push(formatContent('年级', grade) as never);
      studentInfo.push(formatContent('手机号', phoneNumber) as never);
      studentInfo.push(formatContent('微信号', wechatAccount) as never);
      studentInfo.push(formatContent('联系地址', address) as never);
    } else {
      studentInfo = (customInfo as ICustomAttributeItem[]).map((item) => {
        if (item) {
          const standardProfileItemKey = get(item, 'attributeKey');
          if (standardProfileItemKey === 'edu_stuName') {
            return formatContent(
              '学员姓名',
              <BlankLink href={`${STUDENT_DETAIL_URL}/${alias}`}>{name}</BlankLink>
            ) as never;
          }
          const { attributeTitle, value, attributeKey, attributeId } = item;
          if (attributeKey) {
            if (attributeKey === 'edu_idCard') {
              // 如果是身份证，需要用到脱敏接口来查看真实身份证
              return formatContent(
                attributeTitle,
                <IdCard key={attributeId} value={value} version={version} id={id} />
              );
            }
          }
          if (standardProfileItemKey === 'edu_stuGender') {
            return formatContent(attributeTitle, GENDER[value]) as never;
          }
          return formatContent(attributeTitle, value);
        }
        return null;
      });
    }

    if (studentInfo.length > 3) {
      if (foldStudent) {
        studentInfo = studentInfo.slice(0, 3);
      }
      studentInfo.push({
        text: (
          <a
            className={s.foldBtn}
            onClick={this.handleFoldClick(FOLD_KEYS.FOLD_STUDENT, foldStudent)}
          >
            <span>{foldStudent ? '查看更多' : '收起'}</span>&nbsp;
            <span className={cx({ [s.iconArrow]: true, [s.iconArrowUp]: !foldStudent })} />
          </a>
        ),
      });
    }

    return studentInfo;
  };

  getHotelInfo = () => {
    const result: Array<ReturnType<typeof formatContent> | React.ReactNode> = [];
    const { orderInfo } = this.context;
    let hotelInfo = JSON.parse(get(orderInfo, 'tcExtra.BIZ_ORDER_ATTRIBUTE', '{}'));
    hotelInfo = JSON.parse(get(hotelInfo, 'NEW_HOTEL_MSG', '{}'));
    const itemNum = get(orderInfo, 'items[0].num', 1);
    result.push(
      formatContent(
        '酒店名称',
        <a rel="noopener noreferrer" target="_blank" href={getHotelUrl(hotelInfo.hotelId)}>
          {hotelInfo.hotelName}
        </a>
      )
    );
    result.push(formatContent('房型数量', `${hotelInfo.roomTypeName} ${itemNum}间`));
    // 有早餐
    if (hotelInfo.breakfast > -1) {
      result.push(formatContent('早餐数量', `${hotelInfo.breakfast}份/间夜`));
    }
    return result;
  };

  getGuideInfo = () => {
    const result: Array<ReturnType<typeof formatContent> | React.ReactNode> = [];
    const { tcExtra } = this.context.orderInfo || {};
    try {
      const guideInfo = JSON.parse(get(tcExtra, 'DAOGOU', '{}'));
      const { name, phone } = guideInfo;
      result.push(formatContent('导购员姓名', name));
      result.push(formatContent('导购员手机号', phone));
      // eslint-disable-next-line no-empty
    } catch (err) {}
    return result;
  };

  // 显示收货地址修改、复制入口
  renderModifyAndCopy() {
    const { orderInfo } = this.context;
    const { orderNo } = orderInfo;
    const { isShowQueryBtn, receiverName, receiverTel, receiverAddressDetail } = this.state;
    // 订单是否来自小商店
    const isFormSmallShop = getIsFormSmallShop(orderInfo);
    const dianzhongdianInfo = isFromShopInShop(orderInfo);
    const { state, isModifyLogistics, orderHistoryAddress } = orderInfo;
    const addressDesc = getExpressAddress(orderInfo);
    // 数组最后一个为地址详情 需要脱敏并支持查看
    addressDesc[addressDesc.length - 1] =
      receiverAddressDetail || addressDesc[addressDesc.length - 1];
    // (待发货和代付款状态，显示修改收货地址入口 || 京东待揽收状态展示编辑入口) && 有修改订单权限
    const showModifyBtn =
      ([OrderStateEnum.toDelivery, OrderStateEnum.toPay].indexOf(state) > -1 ||
        orderInfo.orderWaybillSendModifyReceiver) &&
      checkAccess('修改订单');

    const detailArr = [receiverName, receiverTel];
    detailArr.unshift(addressDesc.join(' '));

    // 查看后返回原文的复制
    const detail = detailArr.join(', ');

    if (isModifyLogistics) {
      this.logViewHistory(orderInfo);
    }
    const getDisabledText = (orderInfo) => {
      if (isHuijuOrder(orderInfo)) {
        return '腾讯惠聚订单暂不支持此操作，请前往腾讯惠聚后台完成';
      }
      if (isTradeModuleV3Order(orderInfo)) {
        return '微信视频号3.0订单暂不支持此操作';
      }
      if (isFromVideoShop(orderInfo)) {
        return videoShopMsgNotify;
      }
      return '小商店订单暂不支持此操作，请前往小商店后台完成';
    };

    // 收货地址
    return (
      // 查看
      <div className={s.modifyAndCopyWrap}>
        <BlurredQueryButton
          {...blurredBtnProps}
          orderNo={orderNo}
          searchKey={receiverInfoKey}
          onShowMore={(data) => {
            this.setPlainText(data);
          }}
        />
        {/* 复制 */}
        {!isShowQueryBtn && addressDesc.length > 0 && (
          <Button
            type="text"
            className={s.copy}
            onClick={() => this.beforeCopy({ copyDetail: detail })}
          >
            复制
          </Button>
        )}
        {/* 修改 */}
        {showModifyBtn && addressDesc.length > 0 && (
          <DisableAction
            isDisabled={
              isFormSmallShop || isHuijuOrder(orderInfo) || isTradeModuleV3Order(orderInfo)
            }
            disabledText={getDisabledText(orderInfo)}
          >
            <ModifyAddressBtn
              disabledByNotify={dianzhongdianInfo}
              clickFunc={() => {
                this.logClickModify(orderInfo);
              }}
              confirmClick={() => {
                this.logClickModifyConfirm(orderInfo);
              }}
            />
          </DisableAction>
        )}
        {/* 查看修改历史 */}
        {isModifyLogistics && (
          <QueryAddressHistory
            orderNo={orderNo}
            orderHistoryAddress={orderHistoryAddress}
            clickFunc={() => {
              this.logClickHistory(orderInfo);
            }}
          />
        )}
      </div>
    );
  }

  // 查看修改收货地址历史曝光
  logViewHistory(orderInfo) {
    const { orderNo } = orderInfo;
    logger &&
      logger.log({
        et: 'view', // 事件类型
        ei: 'seller_edit_address_history_view', // 事件标识
        en: '查看修改收货地址历史曝光', // 事件名称
        pt: 'orderdetail', // 页面类型
        params: {
          orderNo,
        }, // 事件参数
      });
  }

  // 点击修改收货地址历史
  logClickHistory(orderInfo) {
    const { orderNo } = orderInfo;
    logger &&
      logger.log({
        et: 'click', // 事件类型
        ei: 'seller_edit_history_click', // 事件标识
        en: '商家点击查看修改历史', // 事件名称
        pt: 'orderdetail', // 页面类型
        params: {
          orderNo,
        }, // 事件参数
      });
  }

  // 修改收货地址点击
  logClickModify(orderInfo) {
    const { orderNo } = orderInfo;
    logger &&
      logger.log({
        et: 'click', // 事件类型
        ei: 'seller_edit_address_click', // 事件标识
        en: '订单详情-点击修改收货地址', // 事件名称
        pt: 'orderdetail', // 页面类型
        params: {
          orderNo,
        }, // 事件参数
      });
  }
  // 修改收货地址 确认修改点击
  logClickModifyConfirm(orderInfo) {
    const { orderNo } = orderInfo;
    logger &&
      logger.log({
        et: 'click', // 事件类型
        ei: 'seller_edit_address_confirm_click', // 事件标识
        en: '订单详情-收货地址点击确认修改', // 事件名称
        pt: 'orderdetail', // 页面类型
        params: {
          orderNo,
        }, // 事件参数
      });
  }
  /**
   * 获取订单信息
   * 包括：收货人信息、配送信息、付款信息、买家信息、发票信息
   */
  getOrderInfoMap() {
    const { orderInfo } = this.context;
    const { tcExtra } = orderInfo;
    const { eduInfo } = this.getEduInfo();
    const _isFenxiaoOrder = isFenxiao(orderInfo);
    // 订单信息枚举
    const infoMap = {
      receiver: {
        renderContent: this.getReceiverInfo,
        title: isHotelOrder(orderInfo) ? (
          <div className={s.receiverTitle}>入住人信息{this.renderModifyAndCopy()}</div>
        ) : (
          <>
            <div className={s.receiverTitle}>收货人信息{this.renderModifyAndCopy()}</div>
          </>
        ),
      },
      express: {
        renderContent: this.getExpressInfo,
        title: '配送信息',
      },
      pay: {
        renderContent: this.getPaymentInfo,
        title: '付款信息',
      },
      // 线下支付的展示信息
      offlinePay: {
        renderContent: this.getOfflinePaymentInfo,
        title: '付款信息',
      },
      buyer: {
        renderContent: this.getBuyerInfo,
        title: IS_WHOLESALE ? '批发商信息' : _isFenxiaoOrder ? '分销商信息' : '买家信息',
      },
      invoice: {
        renderContent: this.getInvoiceInfo,
        title: '发票信息',
        titleHint: !window._global?.supportInvoiceProviderList?.length && (
          <BlankLink
            href={`//yingyong.youzan.com/cloud-app-detail/10003280`}
            className={s.invoiceLink}
          >
            了解电子发票服务
          </BlankLink>
        ),
      },
      class: {
        renderContent: this.getClassInfo,
        title: '上课信息',
        className: s.classItem,
      },
      student: {
        renderContent: this.getStudentInfo,
        title: '学员信息',
      },
      hotel: {
        renderContent: this.getHotelInfo,
        title: '酒店信息',
      },
      guide: {
        renderContent: this.getGuideInfo,
        title: '导购员信息',
      },
    };

    type InfoSetType<T> = Array<{
      show: boolean;
      content: Array<keyof T>;
    }>;

    // 各类订单展示的东西不同
    const infoSet: InfoSetType<typeof infoMap> = [
      {
        show: isVirtualTicket(orderInfo),
        content: ['receiver', 'pay', 'buyer'],
      },
      {
        show: isVirtualOrder(orderInfo) && isEduOfflineOrder(orderInfo),
        content: ['offlinePay', 'buyer', 'student', 'class'],
      },
      {
        show: isVirtualOrder(orderInfo) && get(eduInfo, 'alias'),
        content: ['pay', 'buyer', 'student', 'class'],
      },
      {
        // 教育转课订单
        show: isEduClassTransferOrder(orderInfo) && isEduOfflineOrder(orderInfo),
        content: ['offlinePay', 'buyer', 'student', 'class'],
      },
      {
        // 教育导入订单
        show: isImportOrder(orderInfo) && get(eduInfo, 'alias'),
        content: ['pay', 'buyer', 'student', 'class'],
      },
      {
        show: +get(tcExtra, 'SCAN_QRCODE_SCENE', 0) === QRCODE_SCENE && isCheckoutOrder(orderInfo),
        content: ['pay', 'buyer', 'guide'],
      },
      {
        show: isVirtualOrder(orderInfo) || isCheckoutOrder(orderInfo),
        content: ['pay', 'buyer'],
      },
      {
        show: isNewHotelOrder(orderInfo),
        content: ['receiver', 'hotel', 'pay', 'buyer'],
      },
      {
        show: true,
        content: ['receiver', 'express', 'pay', 'buyer'],
      },
    ];

    const idx = findIndex(infoSet, (item) => item.show);
    const content = get(infoSet[idx], 'content');

    // 是否展示发票信息
    const hasInvoice = tcExtra && tcExtra.INVOICE;
    if (hasInvoice) {
      content.push('invoice');
    }

    return pick(infoMap, content);
  }

  // 开启隐私时页面数据&状态更新回调
  userPrivacyRefreshCb = () => {
    this.context?.reload(false).then(() => {
      const { orderInfo } = this.context;
      const initState: Pick<IState, 'receiverTel' | 'receiverName'> = Object.create({});
      initState.receiverTel = orderInfo?.tel;
      initState.receiverName = orderInfo?.userName;
      this.setState({ ...initState, isShowQueryBtn: false });
    });
  };

  // 查看完整收货人信息方法
  setPlainText(data) {
    const userName = data.filter((item) => item.key === 'userName')[0]?.value;
    const tel = data.filter((item) => item.key === 'tel')[0]?.value;
    const addressDetail = data.filter((item) => item.key === 'addressDetail')[0]?.value;
    const selfFetchUserName = data.filter((item) => item.key === 'selfFetchUserName')[0]?.value;

    const idCardName = data.filter((item) => item.key === 'idCardName')[0]?.value;
    const idCardNo = data.filter((item) => item.key === 'idCardNo')[0]?.value;

    // 查看完整买家昵称信息方法
    const customer = data.filter((item) => item.key === 'customer')[0]?.value;

    // 查看完整发票信息方法
    const inVoiceUserName = data.filter((item) => item.key === 'inVoiceUserName')[0]?.value;
    const inVoiceEmailList = data.filter((item) => item.key === 'inVoiceEmailList')[0]?.value;
    const inVoiceOpeningBankName = data.filter((item) => item.key === 'inVoiceOpeningBankName')[0]
      ?.value;
    const inVoiceBankAccount = data.filter((item) => item.key === 'inVoiceBankAccount')[0]?.value;
    const inVoiceAddress = data.filter((item) => item.key === 'inVoiceAddress')[0]?.value;
    const inVoicePhone = data.filter((item) => item.key === 'inVoicePhone')[0]?.value;

    if (userName || tel || addressDetail) {
      this.setState({
        receiverName: userName,
        receiverTel: tel,
        receiverAddressDetail: addressDetail,
        receiverIdcardName: idCardName,
        receiverIdcardNo: idCardNo,
        selfFetchInfoName: selfFetchUserName,
        isShowQueryBtn: false,
      });
    }
    if (customer) {
      this.setState({
        buyerCustomer: customer,
      });
    }
    if (inVoiceUserName) {
      this.setState({
        invoiceInfoTitle: inVoiceUserName,
      });
    }
    if (inVoiceEmailList) {
      this.setState({
        invoiceInfoEmail: inVoiceEmailList,
      });
    }
    if (inVoiceOpeningBankName) {
      this.setState({
        inVoiceOpeningBankName,
      });
    }
    if (inVoiceBankAccount) {
      this.setState({
        inVoiceBankAccount,
      });
    }
    if (inVoiceAddress) {
      this.setState({
        inVoiceAddress,
      });
    }
    if (inVoicePhone) {
      this.setState({
        inVoicePhone,
      });
    }
  }
  render() {
    const orderInfoMap = this.getOrderInfoMap();

    return (
      <ul className={s.wrapper}>
        {map(orderInfoMap, (info: IOrderInfo, index) => {
          const content = info.renderContent();
          if (!content.length) {
            return null;
          }
          return (
            <li key={index} className={cx(s.item, info.className)}>
              <h1 className={s.title}>
                {info.title}
                {info.titleHint}
              </h1>
              {map(content, (data, idx) => {
                if (React.isValidElement(data)) {
                  return <React.Fragment key={idx}>{data}</React.Fragment>;
                }

                return (
                  <div className={s.info} key={idx}>
                    {data.label ? <label className={s.label}>{data.label}</label> : null}
                    {data.text ? <div className={s.text}>{data.text}</div> : null}
                  </div>
                );
              })}
            </li>
          );
        })}
      </ul>
    );
  }
}
