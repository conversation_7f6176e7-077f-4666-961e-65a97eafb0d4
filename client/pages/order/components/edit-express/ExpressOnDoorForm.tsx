import React, { use<PERSON>allback, useEffect, useState } from 'react';
import { Form } from '@zent/compat';
import moment from 'moment';
import ExpressWayBill from 'components/order/open-express-dialog/express/ExpressWayBill';
import WrapperWithFooter from 'components/order/open-express-dialog/express/WrapperWithFooter';
import { IExpressWayBill, IExpressCompany } from 'components/order/open-express-dialog/type';
import { getPrinters } from 'fns/cainiao-printer/printer';
import { getExpressPrinter } from 'components/order/open-express-dialog/storage';
import api from 'components/order/open-express-dialog/api';
import { formatExpressCompanies } from 'components/order/open-express-dialog/format';
import './style.scss';
import { EXPRESS_WAY_BILL_TYPES } from 'components/order/open-express-dialog/const';
import {
  PaymentTypeEnum,
  YZShippingStatusEnum,
  ExpressCompanyIdEnum,
  WaybillVersionEnum,
} from 'constants/express';
import ExpressExtraSystemCall from '@youzan/order-domain-pc-components/es/delivery-dialog/components/express-extra-system-call';
import '@youzan/order-domain-pc-components/css/index.css';
import { beforeConfirmShipment } from '@youzan/order-domain-pc-components/es/delivery-dialog/components/express-extra-system-call/extra';

interface IBatchDeliveryPrintProps {
  orderNo: string;
  onClose?: Function;
  onSubmit?: Function;
  stepperFooter: any;
  handleSubmit: any;
  zentForm: any;
}
const OnlineForm = (props: IBatchDeliveryPrintProps) => {
  const { zentForm, onSubmit, orderNo, stepperFooter, handleSubmit } = props;

  const [expressWayBill, setExpressWayBill] = useState<IExpressWayBill>({} as IExpressWayBill);
  const [defaultPrinter, setDefaultPrinter] = useState<string>('');
  const [printers, setPrints] = useState<any[]>([]);
  const [noPrinter, setNoPrinter] = useState<boolean>(false);
  const [expressCompanies, setExpressCompanies] = useState<IExpressCompany[]>([]);
  const [deposit, setDeposit] = useState<any>({});
  const [YZShoppingInfo, setYZShoppingInfo] = useState<IExpressWayBill['YZShoppingInfo']>({
    waitJoin: true,
    joined: false,
    suspended: false,
    overdueFee: 0,
    overdueLimitFee: 200,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [expressHelper, setExpressHelper] = useState<any>(null);

  const isNewWayBill = window._global.electronWayBillVersion === WaybillVersionEnum.new;

  const expressWayBillData = {
    ...expressWayBill,
    YZShoppingInfo,
    printerId: expressWayBill.printerId || defaultPrinter,
  };

  const handleExpressWayBillChange = (val: any) => {
    setExpressWayBill(val);

    // 如果改动的是expressWayBill并且auditNo是'',则重置表单
    if (val?.auditNo === '' && val?.fakeId === '') {
      zentForm.setFieldsValue({ expressAddress: '' });
    }
  };

  const handleExpressValueChange = useCallback((val, isCover = false) => {
    if (isCover) {
      setExpressWayBill({
        ...val,
      });
    } else {
      setExpressWayBill((prevState) => ({
        ...prevState,
        ...val,
      }));
    }
  }, []);

  const handleGenerateHelper = useCallback((helper) => {
    setExpressHelper(helper);
  }, []);

  // 获取打印机
  const fetchPrinters = () => {
    getPrinters().then((printers) => {
      setPrints(printers);
      setNoPrinter(!printers.length);
      const printerId = getExpressPrinter();
      if (printerId && printers.find((item) => item.id === printerId)) {
        setDefaultPrinter(printerId);
      }
    });
  };

  // 获取快递公司
  const fetchDeliveryExpressCompanies = () => {
    return api.getExpressByNewWayBill({}).then((data) => {
      const { expressCompanies, defaultExpress } = formatExpressCompanies(data);
      if (defaultExpress) {
        handleExpressWayBillChange({
          ...expressWayBill,
          ...defaultExpress,
        });
      }
      setExpressCompanies(expressCompanies);
    });
  };

  const fetchDeposit = () => {
    api.getDepositExpress().then((data) => {
      setDeposit(data || {});
    });
  };

  const fetchYZShoppingInfo = () => {
    api.getYZShoppingServiceInfo().then(({ serviceStatus, overdueFee, overdueLimitFee }) => {
      setYZShoppingInfo({
        waitJoin: serviceStatus === YZShippingStatusEnum.WAIT_JOIN,
        joined: serviceStatus === YZShippingStatusEnum.JOINED,
        suspended: serviceStatus === YZShippingStatusEnum.SUSPEND,
        overdueFee,
        overdueLimitFee,
      });
    });
  };

  const getWayBillData = () => {
    const {
      auditNo,
      fakeId,
      address,
      printerId,
      expressId,
      expressName,
      pickTime,
      expressWayBillType,
      logisticsServicesView,
      paymentType,
      brandCode,
      productCode,
      templateUrl,
      weight,
      YZShoppingInfo = {},
    } = expressWayBillData;

    const options: any = {};

    if (
      expressId === ExpressCompanyIdEnum.SF &&
      (expressWayBillType === EXPRESS_WAY_BILL_TYPES.printAndCallCourier.value ||
        expressWayBillType === EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value)
    ) {
      // 后端需要时间精确到秒，时间规则：今天的{pickTime.time}小时增加{pickTime.day}天
      const startAppointment =
        moment().startOf('day').hours(pickTime.time).add(pickTime.day, 'day').valueOf() / 1000;
      options.startAppointment = startAppointment;
      options.endAppointment = startAppointment + 3600;
    }
    // 增值服务特殊处理
    if (logisticsServicesView) {
      options.logisticsServices = JSON.stringify(logisticsServicesView);
    }

    const waybillData = {
      auditNo,
      address,
      expressId,
      expressName,
      fakeId,
      expressWayBillType,
      YZShoppingInfo,

      paymentType,
      productCode,
      templateUrl,
      brandCode,
      printerId,
      waybillVersion: window._global.electronWayBillVersion,
      ...options,
    } as IExpressWayBill;
    if (weight) {
      waybillData.weight = weight;
    }

    return waybillData;
  };

  // 确认打单发货
  const handleConfirm = async () => {
    const expressWayBill = isNewWayBill ? expressHelper.getExpressPostData() : getWayBillData();
    if (isNewWayBill) {
      await beforeConfirmShipment(expressWayBill).catch(() => {
        setLoading(false);
        return Promise.reject();
      });
    }
    onSubmit(expressWayBill);
  };

  useEffect(() => {
    if (!isNewWayBill) {
      fetchPrinters();
      fetchDeliveryExpressCompanies();
      fetchDeposit();
      fetchYZShoppingInfo();
    }
  }, []);

  const disabled =
    !zentForm.isValid() ||
    (!expressWayBill.auditNo && !expressWayBill.fakeId) ||
    (expressWayBill.paymentType === PaymentTypeEnum.authority &&
      expressWayBill.YZShoppingInfo?.waitJoin &&
      !expressWayBill.agreeProtocol) ||
    (expressWayBill.paymentType === PaymentTypeEnum.authority &&
      expressWayBill.YZShoppingInfo?.suspended);

  return (
    <div className="order-express-dialog-content">
      <WrapperWithFooter
        loading={loading}
        disabled={disabled}
        onSubmit={handleConfirm}
        handleSubmit={handleSubmit}
        footer={stepperFooter}
      >
        <Form horizontal className="delivery-content">
          {!isNewWayBill ? (
            !!expressCompanies.length && (
              <ExpressWayBill
                isEditExpress
                orderNo={orderNo}
                expressWayBill={expressWayBillData}
                onChange={(val) => handleExpressWayBillChange(val)}
                printers={printers}
                noPrinter={noPrinter}
                expressCompanies={expressCompanies as IExpressCompany[]}
                deposit={deposit}
                certValid
                depositValid
                exceptionInfo={{} as any}
                fetchExpressList={fetchDeliveryExpressCompanies}
                fetchPrinters={fetchPrinters}
                isNewWayBill={isNewWayBill}
              />
            )
          ) : (
            <div className="od-express-content">
              <ExpressExtraSystemCall
                orderNo={orderNo}
                express={expressWayBill}
                onExpressValueChange={handleExpressValueChange}
                waybillVersion={2}
                generateHelper={handleGenerateHelper}
              />
            </div>
          )}
        </Form>
      </WrapperWithFooter>
    </div>
  );
};

const FormContainer = Form.createForm()(OnlineForm);

const ExpressOnDoorForm = ({ orderNo, onClose, onSubmit, stepperFooter }) => {
  return (
    <FormContainer
      orderNo={orderNo}
      onClose={onClose}
      onSubmit={onSubmit}
      stepperFooter={stepperFooter}
    />
  );
};

export default ExpressOnDoorForm;
