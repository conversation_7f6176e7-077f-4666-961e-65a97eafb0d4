import React, { Component } from 'react';
import { previewImage, Notify, BlockLoading } from 'zent';
import { formatContent, isExchange } from '../../common/helper';
import fullfillImage from '@youzan/utils/fullfillImage';
import TradeSteps, { TradeStepItem } from 'components/trade-steps';
import { RefundContext } from '../../context';
import { getDeliveryInfo } from '../../api';
import { isEmpty, get } from 'lodash';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import formatDate from '@youzan/utils/date/formatDate';
import { IRefundLogistics } from '../../types';
import { formatCentMoney } from 'fns/utils';

import './style.scss';

interface IState {
  isLoading: boolean;
  expressDetail: IExpressDetail;
  logistics: IRefundLogistics;
  demandStr: string;
}

interface IExpressDetail {
  createdTime: number;
  message: string;
  logisticsList: Array<{ context: string; time: string }>;
}

export default class BuyerExpress extends Component<{}, IState> {
  static contextType = RefundContext;
  context!: React.ContextType<typeof RefundContext>;
  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      expressDetail: {} as IExpressDetail,
      logistics: {} as IRefundLogistics,
      demandStr: '退货',
    };
  }

  componentDidMount() {
    this.initLogistics();
  }

  componentWillReceiveProps() {
    this.initLogistics();
  }

  initLogistics() {
    const { refundInfo } = this.context;
    const { refundLogistics, exchangeLogistics } = refundInfo;
    let logistics = refundLogistics;
    let demandStr = '退货';

    if (isExchange(refundInfo) && exchangeLogistics) {
      logistics = exchangeLogistics;
      demandStr = '换货';
    }

    this.setState(
      {
        logistics,
        demandStr,
      },
      () => {
        this.fetchDeliveryLogistics();
      },
    );
  }

  fetchDeliveryLogistics() {
    const { logistics, demandStr } = this.state;
    const { logisticsNo, companyCode } = logistics || {};

    if (!logisticsNo || !companyCode) {
      return;
    }

    getDeliveryInfo({
      expressNo: logisticsNo,
      expressId: companyCode,
    })
      .then(res => {
        if (!isEmpty(res)) {
          let logisticsList = [];
          try {
            logisticsList = JSON.parse(res.data);
          } catch (e) {
            //
          }
          res.logisticsList = logisticsList;

          this.setState({
            expressDetail: mapKeysToCamelCase(res),
          });
        }
      })
      .catch(msg => {
        Notify.error(msg || `获取${demandStr}物流信息失败`);
      })
      .finally(() => {
        this.setState({ isLoading: false });
      });
  }

  getExpressInfos() {
    const { lastedReturnMessage, pickUpShow,pickUpFee } = this.context.refundInfo;
    const { companyName, logisticsNo, createTime } = this.state.logistics;

    const infoItems = [
      {
        content: formatContent('物流公司', companyName),
        show: true,
      },
      {
        content: formatContent(
          '物流单号',
          logisticsNo,
          false,
          pickUpShow !== undefined ? (
            <span style={{ color: '#969799' }}>（{pickUpShow}）</span>
          ) : pickUpFee !== undefined ? (
            <span style={{ color: '#969799' }}>（退运费{formatCentMoney(pickUpFee)}元已直接从退款金额中扣除）</span>
          ) : undefined,
        ),
        show: true,
      },
      {
        content: formatContent('填写时间', formatDate(createTime, 'YYYY-MM-DD HH:mm:ss')),
        show: true,
      },
      {
        content: formatContent('物流说明', lastedReturnMessage?.messageDetail?.refundDesc || '无'),
        show: true,
      },
      {
        content: formatContent('物流凭证', this.renderExpressImgs(lastedReturnMessage?.attachment)),
        show: true,
      },
    ];

    return infoItems
      .filter(item => item.show)
      .map((item, index) => {
        const { label, text, extra } = item.content;

        return (
          <div className="express-info-item" key={index}>
            <label className="info-label">{label}</label>
            <div className="info-content">
              {text}
              {extra}
            </div>
          </div>
        );
      });
  }

  renderExpressImgs(attachment: string[] = []) {
    if (attachment.length === 0) {
      return '无';
    }

    return (
      <div className="express-info__imgs">
        {attachment.map((url, index) => {
          return (
            <img
              src={fullfillImage(url, '!100x100.jpg')}
              key={index}
              onClick={() => this.handlePreview(url, attachment)}
            />
          );
        })}
      </div>
    );
  }

  handlePreview(src, imgs) {
    previewImage({
      images: imgs,
      index: imgs.indexOf(src),
    });
  }

  renderExpressLogistics() {
    const { expressDetail } = this.state;

    if (!get(expressDetail, 'logisticsList[0]', false)) {
      return null;
    }

    const stepList = expressDetail.logisticsList.map((item, index) => {
      const stepHeader = <span className="icon-express" />;
      const stepContent = (
        <div className="express-detail">
          <label className="time">{item.time}</label>
          <div className="text">{item.context}</div>
        </div>
      );

      return (
        <TradeStepItem
          className={index === 0 ? 'is-current' : ''}
          key={index}
          header={stepHeader}
          content={stepContent}
        />
      );
    });

    return <TradeSteps className="express-steps">{stepList}</TradeSteps>;
  }

  render() {
    const { refundLogistics, exchangeLogistics } = this.context.refundInfo;
    const { expressDetail, isLoading, demandStr } = this.state;
    if (exchangeLogistics) {
      // 处于换货中的商家发货环节, 且无发货单号(换货流程)
      if (!exchangeLogistics.logisticsNo) {
        return null;
      }
    } else if (!refundLogistics?.logisticsNo) {
      // 处于买家退货环节，且无退货单号
      return null;
    }

    return (
      <div className="order-block">
        <h2 className="order-block__header">{demandStr}物流</h2>
        <BlockLoading loading={isLoading}>
          <div className="buyer-express">
            <div className="express-info">{this.getExpressInfos()}</div>
            <div className="express-logistics">
              <div className="express-info-item">
                <label className="info-label">物流状态：</label>
                <div className="info-content c-express">{expressDetail?.message || '无'}</div>
              </div>
              <div className="express-steps-wrap">{this.renderExpressLogistics()}</div>
            </div>
          </div>
        </BlockLoading>
      </div>
    );
  }
}
