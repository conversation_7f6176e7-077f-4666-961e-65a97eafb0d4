import { isRetailShop } from "@youzan/utils-shop";

// 批量选择发货的类型
export enum BatchSelectType {
  /** 常规的 */
  normal = 1,
  /** 打单发货 */
  printDelivery = 2,
}

// 批量任务类型
export enum DeliveryType {
  BATCH_DELIVERY = 'BATCH_DELIVERY',
  BATCH_MODIFY_EXPRESS = 'BATCH_MODIFY_EXPRESS',
  BATCH_TICKOFF_DELIVERY = 'BATCH_TICKOFF_DELIVERY',
  BATCH_DELIVERY_PRINT = 'BATCH_DELIVERY_PRINT',
  BATCH_DELIVERY_CLOUD_PRINT = 'BATCH_DELIVERY_CLOUD_PRINT',
  BATCH_RETAIL_FULFILL_DELIVERY = 'BATCH_RETAIL_FULFILL_DELIVERY',
}

// 批量任务中文
export const BIZ_OP = {
  [DeliveryType.BATCH_DELIVERY]: '批量导入发货',
  [DeliveryType.BATCH_MODIFY_EXPRESS]: '批量修改物流',
  [DeliveryType.BATCH_TICKOFF_DELIVERY]: '批量选择发货',
  [DeliveryType.BATCH_DELIVERY_PRINT]: '批量打单发货',
  [DeliveryType.BATCH_DELIVERY_CLOUD_PRINT]: '自动打单发货',
  [DeliveryType.BATCH_RETAIL_FULFILL_DELIVERY]: '批量导入发货',
};

// 批量任务类型选择框
export const deliveryBizOptions = Object.keys(DeliveryType).map(key => {
  const value = DeliveryType[key];
  return {
    value,
    text: BIZ_OP[value],
  };
}).filter(item => {
  if (!isRetailShop) {
    return true;
  }
  // 零售店铺不支持批量选择发货
  return item.value !== DeliveryType.BATCH_TICKOFF_DELIVERY;
})

export const isUpgradedWxShop = _global?.is_upgraded_wx_shop || false;

export const videoShopTextName = isUpgradedWxShop ? '微信小店' : '视频号小店';
