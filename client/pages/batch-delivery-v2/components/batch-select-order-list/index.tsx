import React, { FC } from 'react';
import { BlockLoading } from 'zent';

import ListHeader from './list-header';
import ListItem from './list-item';
import ListFooter from './list-footer';

import { IPageOptions, ICheckedListItem } from '../BatchSelectDrawer';

import './index.scss';

interface IProps {
  list: any;
  loading: boolean;
  isCheckedAll: boolean;
  checkedList: ICheckedListItem[];
  pageOptions: IPageOptions;
  /** 是否批量打单 */
  isPrintDelivery: boolean;
  onChangeCheckedAll: (value: boolean) => void;
  onChangeChecked: (isChecked: boolean, value: ICheckedListItem) => void;
  onChangePage: (value: { pageSize: number; current: number }) => void;
  onChangeListData: (value: any) => void;
}

const BatchSelectOrderList: FC<IProps> = ({
  list,
  loading,
  isCheckedAll,
  checkedList,
  pageOptions,
  isPrintDelivery,
  onChangeCheckedAll,
  onChangeChecked,
  onChangePage,
  onChangeListData,
}) => {
  return (
    <div className="batch-select-order-list">
      <ListHeader onChange={onChangeCheckedAll} isChecked={isCheckedAll} />
      <BlockLoading loading={loading}>
        {list?.length > 0 ? (
          <div className="order-list-body">
            {list.map((item, idx) => (
              <ListItem
                listItemData={item as any}
                key={idx}
                isChecked={!!checkedList.find(v => v.orderNo === item.orderNo && v.warehouseId === item.warehouseId)}
                onChangeChecked={e =>
                  onChangeChecked(e.target.checked, item)
                }
                onChangeListData={onChangeListData}
              />
            ))}
          </div>
        ) : (
          <div className="table-empty-content">
            <i className="table-empty-content__query-icon" />
            <span>暂无订单</span>
          </div>
        )}
      </BlockLoading>
      <ListFooter
        {...pageOptions}
        isChecked={isCheckedAll}
        length={checkedList.length}
        isPrintDelivery={isPrintDelivery}
        onChangeChecked={onChangeCheckedAll}
        onChangePage={onChangePage}
      />
    </div>
  );
};

export default BatchSelectOrderList;
