import React, { FC } from 'react';
import get from 'lodash/get';
import classNames from 'classnames';
import fullfillImage from '@youzan/utils/fullfillImage';
import { isHqStore, isPureWscSingleStore, isRetailShop } from '@youzan/utils-shop';
import { Checkbox, Notify, ClampLines, Pop, Icon } from 'zent';
import buildUrl from '@youzan/utils/url/buildUrl';
import formatDate from '@youzan/utils/date/formatDate';
import findIndex from 'lodash/findIndex';
import uuidV4 from 'uuid/v4';

import BlurredQueryButton from 'components/order/blurred-query-button';

import { ClickMode, ComponentVersion } from 'components/order/blurred-query-button/type';
import { orderAddressInfoKeyMap, buyerInfoKeyMap } from 'components/order/blurred-query-button/key';
import { isFinanceWithPriority } from 'constants/role';
import { isFromShopInShop } from 'fns/order-helper';
import { formatJsonStrToArr } from 'fns/utils';
import { isImportOrder, isEduClassTransferOrder, isEduQuickOffline } from './utls';
import { IFormattedOrderListItem, IGoodsItemWithPeriod } from './types';

import './index.scss';

interface IProps {
  listItemData: IFormattedOrderListItem;
  isChecked: boolean;
  onChangeChecked: (value: any) => void;
  onChangeListData: (value: any) => void;
}

const ListItem: FC<IProps> = ({ listItemData, isChecked, onChangeChecked, onChangeListData }) => {
  const isNotCurrentOutStoreOrder = (orderInfo: IFormattedOrderListItem) => {
    return (
      typeof orderInfo.isCurrentOutStoreOrder === 'boolean' && !orderInfo.isCurrentOutStoreOrder
    );
  };

  const getIsFormSmallShop = (orderInfo: IFormattedOrderListItem) => {
    return get(orderInfo, 'tc_order_source_d_t_o.orderSourceCode') === 'wx_small_shop';
  };

  const renderGoodsItem = (data: IFormattedOrderListItem, goods: IGoodsItemWithPeriod) => {
    /**
     * 商品信息
     */
    const _renderGoodsInfo = () => {
      const tags = [] as string[];
      // if (this.state.isEnjoyBuy) {
      //   tags.push('随心订');
      // }
      if (+goods.isPresent) {
        tags.push('赠品');
      }

      const renderGoodsTitle = (canNotJump, goods) => {
        const goodsTitle = canNotJump ? (
          goods.title
        ) : (
          <a
            href={buildUrl(goods.url, '', goods.kdtId)}
            rel="noopener noreferrer"
            target="_blank"
            title={goods.title}
          >
            {goods.title}
          </a>
        );
        return goodsTitle;
      };

      const renderSkuAndProperty = (goods: IGoodsItemWithPeriod) => {
        const { sku = [], extraMap } = goods;
        const properties = formatJsonStrToArr<any>(extraMap?.GOODS_PROPERTY);
        const skuAndProperty = [
          ...sku.map((item) => item.v),
          ...properties.map((property) => property.valName),
        ];

        const skuInfo = skuAndProperty.join(',');

        return (
          <div className="sku-item" key={uuidV4()}>
            <ClampLines key={uuidV4()} lines={2} showPop={false} text={skuInfo} />
          </div>
        );
      };

      const getGoodsNoHtml = (goods: IGoodsItemWithPeriod) => {
        const { goodsInfo, sku, skuCode } = goods;
        const hasSku = sku && sku.length > 0;
        const goodsNo = get(goodsInfo, 'goods_no', '');

        if (goodsNo && hasSku && skuCode) {
          return <div className="sku-item">规格编码: {skuCode}</div>;
        }

        return null;
      };

      // 符合规则的商品标题不可点击 无法跳转
      const canNotJump =
        // 6 二维码订单 15 返利订单 75 知识付费订单
        findIndex([6, 15, 75], (item) => item === +data.orderType) > -1 ||
        isImportOrder(data) ||
        isEduClassTransferOrder(data) ||
        isEduQuickOffline(data) ||
        // 小商店订单，并且没有alias。不可跳转
        (getIsFormSmallShop(data) && !goods.alias) ||
        // 没有url不可跳转
        !goods.url;

      return (
        <div className="goods-info__info">
          {/* 商品名称 */}
          <div
            className={classNames('goods-title', {
              disabled: isNotCurrentOutStoreOrder(data),
            })}
          >
            {renderGoodsTitle(canNotJump, goods)}
          </div>
          {/* sku */}
          <div className="goods-skus">
            {renderSkuAndProperty(goods)}
            {getGoodsNoHtml(goods)}
          </div>
          {/* 赠品标签 */}
          <div className="goods-tags">
            {tags.map((tag, idx) => {
              return (
                <span key={idx} className="goods-tag">
                  {tag}
                </span>
              );
            })}
          </div>
        </div>
      );
    };

    /**
     * 商品价格
     */
    const _renderGoodsPrice = () => {
      // 酒店商品计算平均价格
      let point = get(goods, 'goodsInfo.points_price');
      let price: number | string = goods.price;
      let payPrice: number | string = goods.payPrice;
      if (!data.isPoints) {
        return <div className="goods-info__pay">￥{price}</div>;
      }
      return (
        <div className="goods-info__pay">
          {point}
          积分
          {parseFloat(payPrice as string) > 0 && ` + ￥${payPrice}`}
        </div>
      );
    };

    const imageUrl =
      isImportOrder(data) || isEduClassTransferOrder(data)
        ? 'https://b.yzcdn.cn/public_files/20b298f36e9e551f08c530e8c9dab1b2.png'
        : fullfillImage(goods.imageUrl, '!100x100.jpg');

    return (
      <td className="goods-cell">
        <div className="goods-item-cell">
          <div className="goods-info__img_block">
            <img className="goods-info__img" src={imageUrl} alt="" />
            {/* 已发货展示标签 */}
            {goods.isShipped && <div className="goods-order-state">已发货</div>}
          </div>
          {_renderGoodsInfo()}
          <div className="goods-info__price">
            {_renderGoodsPrice()}
            <div className="goods-info__unit">{goods.num}件</div>
          </div>
        </div>
      </td>
    );
  };

  // 付款金额
  const renderPayPriceCell = (data: IFormattedOrderListItem, goodsIndex: number) => {
    if (goodsIndex !== 0) {
      return;
    }
    const renderPrice = () => {
      if (data.isPoints) {
        return (
          <div>
            {data.realPointPay}
            积分
            {parseFloat(data.settlementMoney) > 0 && <span>+ ￥{data.settlementMoney}</span>}
          </div>
        );
      }
      return <span>{data.settlementMoney}</span>;
    };
    return (
      <td className="pay-price-cell" rowSpan={data.items.length}>
        <div>
          {renderPrice()}
          {+data.postage !== 0 && (
            <div>
              <span className="c-gray">(含运费: {data.postage})</span>
            </div>
          )}
          {data.isHead && (
            <div>
              <span className="orange">团长订单</span>
            </div>
          )}
          {!data.isHead && data.needAgencyReceive && (
            <div>
              <span className="orange">团长代收</span>
            </div>
          )}
        </div>
      </td>
    );
  };

  // 买家
  const renderCustomerCell = (data: IFormattedOrderListItem, goodsIndex: number) => {
    const { userName, tel, orderNo, customer } = data;
    const renderFans = () => {
      // 订单是否来自小商店
      const isFormSmallShop = getIsFormSmallShop(data);
      const dianZhongDianInfo = isFromShopInShop(data);

      const showMoreIcon =
        isPureWscSingleStore &&
        (userName.includes('*') || customer.includes('*') || tel.includes('*')) ? (
          <BlurredQueryButton
            style={{ marginLeft: 2 }}
            version={ComponentVersion.OptimizedPrivacy}
            clickMode={ClickMode.Fetch}
            searchKey={[
              buyerInfoKeyMap.customer,
              orderAddressInfoKeyMap.tel,
              orderAddressInfoKeyMap.userName,
            ]}
            orderNo={orderNo}
            onShowMore={(value) => {
              if (value?.length > 0) {
                value.forEach((item) => {
                  data[item.key] = item.value;
                });
                onChangeListData(data);
              }
            }}
          />
        ) : null;

      if (isFormSmallShop) {
        return (
          <>
            小商店用户
            {showMoreIcon}
          </>
        );
      }

      if (!data.customer) {
        return (
          <>
            非粉丝
            {showMoreIcon}
          </>
        );
      }

      const getWeappCustomerName = (customer: string) => {
        const fdStart = customer.indexOf('wx_appservice');
        let newCustomer = '';
        if (fdStart === 0) {
          newCustomer = customer.replace(/wx_appservice/i, '小程序匿名用户');
        } else {
          newCustomer = customer;
        }
        return newCustomer;
      };

      /* 粉丝昵称有可能是 span 标签，需要显示 emoji 表情 */
      if (data.buyerId) {
        // 角色是财务
        if (isFinanceWithPriority || +data.orderType === 3) {
          return (
            <>
              <span dangerouslySetInnerHTML={{ __html: getWeappCustomerName(data.customer) }} />
              {showMoreIcon}
            </>
          );
        }

        // 店中店订单
        if (dianZhongDianInfo) {
          return (
            <>
              <a
                dangerouslySetInnerHTML={{ __html: data.customer }}
                onClick={() => Notify.error(dianZhongDianInfo)}
              />
              {showMoreIcon}
            </>
          );
        }

        return (
          <>
            <a
              href={`/v4/scrm/customer/manage#/detail?yzUid=${data.buyerId}`}
              rel="noopener noreferrer"
              dangerouslySetInnerHTML={{ __html: data.customer }}
              target="_blank"
            />
            {showMoreIcon}
          </>
        );
      } else if (+data.customer !== +data.tel || +data.orderType === 35) {
        return (
          <>
            <span dangerouslySetInnerHTML={{ __html: getWeappCustomerName(data.customer) }} />
            {showMoreIcon}
          </>
        );
      }
    };
    const IS_WHOLESALE = window._global.isWholesale || false;
    const wholesalerName = data.tcExtra?.bizOrderAttribute?.wholesaleSnapshot?.name || '';
    const showBuyerLine = !!(tel || userName); // 有收货人信息则展示该行
    if (goodsIndex === 0) {
      return (
        <td className="customer-cell" rowSpan={data.items.length}>
          <div className="fans-cell">
            <span className="label">买家：</span>
            {renderFans()}
          </div>
          {showBuyerLine && (
            <div className="buyer-cell">
              <span className="label">收货人：</span>
              <>
                <span
                  className={classNames('user-name', {
                    disabled: isNotCurrentOutStoreOrder(data),
                  })}
                >
                  {IS_WHOLESALE ? wholesalerName : userName}
                </span>
                <span>{tel}</span>
              </>
            </div>
          )}
        </td>
      );
    }
  };

  // 配送方式
  const renderExpressWay = (data: IFormattedOrderListItem, goodsIndex: number) => {
    if (goodsIndex === 0) {
      const expressTypeList = ['快递发货', '上门自提', '同城配送'];
      return (
        <td className="express-cell" rowSpan={data.items.length}>
          {expressTypeList[data.expressType] || '-'}
        </td>
      );
    }
  };

  // 订单状态
  const renderStateCell = (
    data: IFormattedOrderListItem,
    item: IGoodsItemWithPeriod,
    goodsIndex: number
  ) => {
    const deliveryStateMap = {
      0: '待付款',
      1: '待发货',
      2: '已发货',
      3: '全额退款',
      4: '部分退款',
    };

    // 周期购待发货
    const isPeriodOrderPendingDelivery =
      (data.activityType === 13 || item.goodsType === 24) && data.periodOrderDetail?.length > 0;

    // 订单状态显示
    const renderState = () => {
      if (isPeriodOrderPendingDelivery) {
        const periodOrderDetail = data.periodOrderDetail[0];
        // 周期购订单状态
        const renderPeriodBuyState = () => {
          // 待配送
          if (+periodOrderDetail.deliveryState === 1) {
            return (
              <div>
                {periodOrderDetail.issue ? (
                  <div>
                    第{periodOrderDetail.issue}
                    期待发货
                  </div>
                ) : (
                  <div>等待商家发货</div>
                )}
                {/* 周期购订单显示送达时间 */}
                {periodOrderDetail.planExpressTime && (
                  <div>
                    <span className="orange">
                      {formatDate(periodOrderDetail.planExpressTime * 1000, 'YYYY年MM月DD日')}
                    </span>
                    送达
                  </div>
                )}
              </div>
            );
          }
          return <div>{deliveryStateMap[+periodOrderDetail.deliveryState]}</div>;
        };
        return renderPeriodBuyState();
      } else if (data.isPeriodBuySingle) {
        return <div>周期购待发货</div>;
      }

      return (
        <div>
          <p>{data.stateStr}</p>
        </div>
      );
    };

    // 同城送状态提示
    const renderCityState = () => {
      if (data.expressType === 2 && data.deliveryTimeDisplay) {
        const content = (
          <div>
            已开启定时达
            <br />
            预约送达时间：
            {data.deliveryTimeDisplay}
          </div>
        );
        return (
          <Pop trigger="hover" position="bottom-center" content={content}>
            <div>
              <Icon className="city-state-icon" type="info-circle-o" />
            </div>
          </Pop>
        );
      }
    };

    if (goodsIndex === 0 || isPeriodOrderPendingDelivery) {
      return (
        <td rowSpan={isPeriodOrderPendingDelivery ? 1 : data.items.length} className="state-cell">
          <div className="order-state">
            {renderState()}
            {/* 同城送订单 */}
            {renderCityState()}
          </div>
        </td>
      );
    }
  };
  const detailUrl = isRetailShop
    ? `/v2/order/orderdetail#/?order_no=${listItemData.orderNo}`
    : `/v4/trade/order/detail?orderNo=${listItemData.orderNo}`;

  return (
    <div style={{ marginBottom: 16 }}>
      <div className="list-item-header">
        <div className="list-item-header-row">
          <div className="list-item-header-row__left">
            <Checkbox checked={isChecked} onChange={onChangeChecked} />
            <div className="order-no">订单号：{listItemData.orderNo}</div>{' '}
            <span className="header-item-split" />
            <span className="book-time-wrap">
              下单时间：{formatDate(listItemData.bookTime, 'YYYY-MM-DD HH:mm:ss')}
            </span>
            {isHqStore && !!listItemData.warehouseName && (
              <>
                <span className="header-item-split" />
                <span>门店 {listItemData.warehouseName}</span>
              </>
            )}
          </div>
          <div className="list-item-header-row__right">
            {!!+listItemData.star && (
              <span className="star">
                <span className="star-img" />x {listItemData.star}
              </span>
            )}
            <a href={detailUrl} target="_blank">
              查看详情
            </a>
          </div>
        </div>
      </div>
      <table className="list-item-body-table">
        <tbody className="list-item-body">
          {(listItemData.items || []).map((goods, goodsIndex) => {
            return (
              <tr className="list-item-row" key={goodsIndex}>
                {/* 商品 */}
                {renderGoodsItem(listItemData, goods)}
                {/* 实付金额 */}
                {renderPayPriceCell(listItemData, goodsIndex)}
                {/* 买家/收货人 */}
                {renderCustomerCell(listItemData, goodsIndex)}
                {/* 配送方式 */}
                {renderExpressWay(listItemData, goodsIndex)}
                {/* 订单状态 */}
                {renderStateCell(listItemData, goods, goodsIndex)}
              </tr>
            );
          })}
          {listItemData.buyerMsg ? (
            <tr className="remark-row buyer-msg">
              <td colSpan={8}>{`买家备注：${listItemData.buyerMsg}`}</td>
            </tr>
          ) : null}
          {listItemData.remark ? (
            <tr className="remark-row seller-msg">
              <td colSpan={8}>
                <div className="seller-msg-remark_content">
                  <span>卖家备注：</span>
                  {/* 卖家备注内容需要支持换行 */}
                  <span className="seller-msg-remark_content-text">{listItemData.remark}</span>
                </div>
              </td>
            </tr>
          ) : null}
        </tbody>
      </table>
    </div>
  );
};

export default ListItem;
