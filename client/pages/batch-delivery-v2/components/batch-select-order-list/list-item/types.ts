import { IDeliveryListItem } from 'definitions/order/list';
import { FEEDBACK_TYPE, IItemExtraMap, IItemRefundInfo } from 'definitions/order/detail/order';

export interface IOrderSourceItem {
  // 回传信息
  code?: string;
  // 展示信息
  desc?: string;
}

export interface IOrderSearchQuery {
  order_label: string;
  order_no?: string;
  outer_transaction_number?: string;
  user_name?: string;
  tel?: string;
  expressNo?: string;
  tel_last4?: string;
  client_tel?: string;
  client_tel_last4?: string;
  goods_title?: string;
  type: string;
  is_star: string;
  store_id?: number;
  state: string;
  express_type: string;
  buy_way: string;
  feedback: string;
  start_time?: string;
  end_time?: string;
  tuanId?: number;
  delivery_start_time?: string;
  delivery_end_time?: string;
  period_send_time?: string;
  ext_type?: string;
  goods_id?: number;
  order?: string;
  order_source: string;
  buyer_id?: number;
  orderby?: string;
  seat_name?: string;
  seller_id?: number;
  cashier_id?: number;
  seller_name?: string;
  cashier_name?: string;
  sub_shop_kdt_id?: string;
  sub_shop_name?: string;
  marketing_channel?: string;
  eduSignUpStatus?: string; // edu
  eduSignUpType?: string; // 教育 - 报名类型
  p?: string | number;
  page_size?: string | number;
  live_room_id?: string | number;
  /** 需要排除的订单来源  如果是多个的话用 - 分割 */
  excludeOrderSource?: string;
  promoter_name?: string;
  promoterId?: number;
  weapp_union_promoter_name?: string;
}

/**
 * 订单列表 request
 */
export interface IOrderListRequest {
  goods_id?: number;
  // 根据什么条件搜索，支持：订单号、外部单号、收货人姓名、收货人手机号、收货人手机号后4位
  order_label?: string;
  // 每页显示个数
  page_size?: number;
  // 订单类型  新增tangshi <20181011 by xiaole>
  type?: string;
  // 下单人用户id
  buyer_id?: number;
  // 售后状态：
  feedback?: string;
  // 销售员id
  seller_id?: number;
  show_banner?: string;
  // 收银员id
  cashier_id?: number;
  // 订单状态  新增wait <20181011 by xiaole>
  state?: string;
  keyword?: IOrderRequestKeyword;
  // 推广渠道  CPS-分佣推广（有赞客）  THIRD_APP-三方应用推广
  marketing_channel?: string;
  // 扩展查询类型，多个类型逗号分隔
  ext_type?: string;
  // 排序类型 desc/asc
  order?: string;
  // 总店ID
  head_kdt_id?: number;
  // 订单来源
  order_source?: string;
  // 粉丝号搜索
  fans_id?: number;
  // 是否加星订单  1 加星 0 不加星 null 默认全部
  star?: number;
  // 店铺ID
  kdt_id?: string | number;
  order_e_s_tag?: string;
  // 物流方式
  express_type: string;
  // 排序字段
  orderby?: string;
  // 付款方式
  buy_way: string;
  disable_express_type?: string;
  tuan_id?: string;
  goods_title?: string;
  // 当前页
  page?: number;
  /** 兼容 */
  p?: number | string;
  // 粉丝类型搜索
  fans_type?: number;
  /** 连锁分店kdtId */
  sub_shop_kdt_id: string | number;
  is_star: string;
  /** 需要排除的订单来源  如果是多个的话用 - 分割 */
  excludeOrderSource?: string;
  promoter_name?: string; // 推广员名称
  promoterId?: number; // 推广员主键
}

/**
 * 订单列表 response
 */
export interface IOrderListResponse {
  list: IOrderListItem[];
  page: string;
  pageSize: number;
  totalItems: number;
}

/**
 * 订单加星 request
 */
export interface IPutStarRequest {
  orderNo: string;
  star: number;
}

/**
 * 根据条件查询网点名称 request
 */
export interface IStoreListRequest {
  /** 是否为线上网点 */
  isOnline?: number;
  /** 是否为线下门店 */
  isStore?: boolean;
  /** 是否包含已删除的 */
  includeDel?: boolean;
  /** 外部编码 */
  outerId?: string;
}

/**
 * 根据条件查询网点名称 response
 */
export interface IStoreListItem {
  // 是否有效， true，有效； false，删除
  valid?: boolean;
  // 网点名称
  name?: string;
  // 网点id
  id?: number;
}

/**
 * 默认地址 response
 */
export interface IDefaultAddressResponse {
  addressId: number;
  kdtId: number;
  contactName: string;
  countryIndex: number;
  countryCode: string;
  mobilePhone: string;
  areaCode: string;
  telephone: string;
  extensionNumber: string;
  regionType: string;
  countyId: string;
  province: string;
  city: string;
  county: string;
  address: string;
  createdTime: string;
  updatedTime: string;
  addressTypes: Array<{
    addressType: number;
    isDefault: boolean;
  }>;
}

/**
 * 获取单条订单数据 request
 */
export interface IGetOrderDataRequest {
  orderNo?: string;
  orderId?: number | string;
  expressType?: string;
  extType?: string;
  type?: string;
}

/**
 * 账户余额信息接口 response
 */
export interface IGetAccountBalanceResponse {
  /** 收支记录流水号 */
  userNo: string;
  /** 账户类型 */
  acctType: number;
  /** 账号 */
  acctNo: string;
  /** 可用余额，单位为分 */
  available: number;
  /** 账户余额，单位为分(账户余额=可用余额+不可用余额) */
  balance: number;
  /** 不可用余额，单位为分 */
  freeze: number;
  /** 账户状态 */
  state: number;
  /** 已提现总金额，单位为分，只有微小店可用 */
  cash: number;
  /** 币种(100?:人民币，101?:美元)目前只有人民币 */
  currency: number;
  /** 最后更新时间 */
  updateTime: number;
}

/**
 * 同城订单获取 语音提醒链接 待接单订单和配送异常订单的角标
 */
export interface IOrderNumAndVoiceBO {
  // 是否开启待接单
  confirmOrder?: boolean;
  // 配送异常订单下载链接
  deliveryExpVoiceUrl: string;
  // 待接单tab角标，最大值99
  waitConfirmOrderNum: number;
  // 是否有配送异常订单
  deliveryExp?: boolean;
  // 是否有预订单
  preOrder?: boolean;
  // 配送异常tab角标，最大值99
  deliveryExpOrderNum: number;
  // 预订单语音下载链接
  preOrderVoiceUrl: string;
  // 预订单语音提醒次数。0代表关闭
  preOrderTimes?: number;
  // 是否有待接单订单
  waitConfirm?: boolean;
  // 配送异常订单语音提醒次数。0代表关闭
  deliveryExpTimes?: number;
  // 待接单语音提醒次数。0代表关闭
  waitConfirmTimes?: number;
  // 待接单语音下载链接
  waitConfirmVoiceUrl: string;
}

export type IGetOrderDataResponse = IOrderListItem;

export interface IOrderRequestKeyword {
  // 外部单号
  outer_transaction_number?: string;
  // 订单号
  order_no?: string;
  // 同城配送订单的预约到达时间-止
  delivery_end_time?: string;
  // 堂食订单用 订单Id
  order_id?: string;
  // 收货人手机号后4位
  tel_last4?: string;
  // 买家手机号
  client_tel?: string;
  // 买家手机号后4位
  client_tel_last4?: string;
  // 收货人姓名
  user_name?: string;
  // 多门店ID
  store_id?: number;
  // 收货人手机号
  tel?: string;
  // 物流单号
  expressNo?: string;
  // 搜索开始时间
  start_time?: string;
  // 堂食订单用 桌位号
  seat_name?: string;
  // 同城配送订单的预约到达时间-起
  delivery_start_time?: string;
  // 搜索结束时间
  end_time?: string;
  // 周期购订单的送达时间
  period_send_time?: string;
}

export interface IOrderListItem {
  enjoyBuyInfoList?: Array<Partial<IPeriodOrderDetail>>;
  activityType: string | number;
  addressDetail: string;
  bankPay: string;
  bookTime: string;
  buyerName: string;
  buyWay: number;
  buyWayStr: string;
  buyerId: number;
  buyerMsg: string;
  buyerPhone?: string;
  buyerRealPay: string;
  changeExpress: string;
  city: string;
  closeState: number;
  closeTime: number;
  confirmTime: number;
  country: string;
  county: string;
  createTime: string;
  customer: string;
  customerId: number;
  customerName: string;
  customerType: number;
  customInfoMap: ICustomInfoMap;
  decrease: string;
  deductedRealPay: string;
  deductionPay: string;
  deliveryAutomaticThirdCall: IDeliveryAutomaticThirdCall;
  deliveryList: IDeliveryListItem[];
  deliveryTimeDisplay: string;
  expressType: number;
  expressTypeDesc: string[];
  extraInfo: string;
  feedback: number;
  feedbackStr: string;
  giftCardPay: string;
  giftRemains: number;
  groupMockNum: number;
  hasChild: number;
  innerTransactionNumber?: string;
  isCurrentOutStoreOrder?: boolean;
  isDownPaymentPre: boolean;
  isEatInOrder: boolean;
  isHead: boolean;
  isKnowledgeGift: boolean;
  isOversold: boolean;
  isPoints?: number;
  isSecuredTransactions: number;
  items: IGoodsItem[];
  kdtId: number;
  localOrderSerialNo: number;
  marketingChannel?: string;
  multiStoreInfo: IMultiStoreInfo;
  needAgencyReceive: boolean;
  orderExtraPrices: any[];
  orderMark?: string;
  orderNo: string;
  orderState: string;
  orderTag: IOrderTag;
  orderType: string;
  orderId: number;
  orderTypeStr: string;
  originalRealPay: number;
  outerTransactionNumber?: string;
  outShopId: string;
  pay: string;
  payNoPostage: string;
  payState: string;
  payTime?: string;
  payType: number;
  payUmpDiscountAccountMoney: string;
  payUmpDiscountDetails: any[];
  payUmpDiscountMoney: string;
  peerpayOrder: IPeerpayOrder;
  permission: IPermission;
  phasePaymentStatus: string;
  postage: string;
  postageLong: number;
  province: string;
  realPay: string;
  realPointPay: number;
  refundState: number;
  refundType: number;
  remark: string;
  seatName: string;
  serialNo: string;
  settlementMoney: string;
  shopId: number;
  shopName: string;
  shopType: string;
  star: string;
  state: number;
  stateStr: string;
  stockState: number;
  storeId: number;
  tariffPay: string;
  tcExtra: ITcExtra;
  tcTags: ITcTags;
  tc_order_source_d_t_o: ITcOrderSourceDTO;
  teamType: number;
  tel: string;
  thirdDeliveryAllSended: boolean;
  tuanId: string;
  updateTime: number;
  userName: string;
  expressTime?: string;
  successTime?: string;
  closeStateStr?: string;
  periodOrderDetail: IPeriodOrderDetail[];
  outBizNo?: string;
  isModifyLogistics?: boolean;
  newOrderState?: number;

  warehouseName?: string;
  warehouseId?: number;
}

export interface IFormattedOrderListItem extends IOrderListItem {
  deliveryList: IDeliveryListItem[];
  items: IGoodsItemWithPeriod[];
  isNewPeriodBuy?: boolean;
  isPeriodBuySingle?: boolean;
  tcExtra: ITcExtra & { invoice?: IExtraInvoice };
}

export interface IExtraInvoice {
  emailList?: string[];
  userName: string;
  raiseType: string;
  taxpayerId: string;
}

export interface IPeriodOrderDetail {
  activityId: number;
  deliverTime: string;
  deliveryState: number;
  expressPkId: number;
  flag: number;
  goodsId: number;
  itemId: string;
  issue: number;
  isDelay: number;
  id: number;
  kdtId: number;
  orderNo: string;
  period: number;
  periodStr: string;
  planExpressTime: number;
  periodSku: string;
  shopId: number;
  totalIssue: number;
}

export interface IMultiStoreInfo {
  area: string;
  address: string;
  city: string;
  isValid: string;
  id: number;
  name: string;
  phone1: string;
  phone2: string;
  phone: string;
  province: string;
}

export interface ICustomInfoMap {
  edu_student_info: string;
}

export interface ITcOrderSourceDTO {
  orderSource: string;
  orderSourceCode: string;
  platformEnum: string;
  wxEntranceEnum: string;
}

export interface ITcTags {
  IS_FENXIAO_ORDER: boolean;
  MESSAGE_NOTIFY?: boolean;
  IS_POSTAGE_FREE?: boolean;
  IS_MEMBER?: boolean;
  IS_SECURED_TRANSACTIONS: boolean;
  STOCK_DEDUCTED?: boolean;
  YZ_GUARANTEE?: boolean;
  IS_PAYED?: boolean;
  IS_VIRTUAL?: boolean;
  HAS_FREIGHT_INSURANCE?: boolean;
  IS_PURCHASE_ORDER?: boolean;
}

export interface ITcExtra {
  SUPPLIER_ALIAS: string;
  SUPPLIER_NAME: string;
  EDU_STUDENT_INFO?: string;
  FROM_CART: string;
  STOCK_DEDUCT_SCENE?: string;
  IS_USE_PARAM_PRICE?: string;
  extend_version: number;
  AD_CPS_SHOP: number;
  IS_PREPAY: boolean;
  INNER_TRANSACTION_NO?: string;
  LC_HIGH_VER?: string;
  TOURIST?: string;
  IS_OFFLINE?: string;
  ENABLE_ACROSS_SHOP_VERIFY: number;
  weAppFormId?: string;
  payTool: string;
  RISK_CONTROL_DOWNGRADED: string;
  FANS: string;
  WECHAT_SYNC_SHOPPING_LIST: number;
  ORDER_TYPE?: string;
  RISK_QUERY_PARAM_PAY_LOAD: string;
  OUTER_TRANSACTION_NO?: string;
  REAL_PAY_AMOUNT?: number;
  FEE_MIGRATE_CHARGE_BY_TC: string;
  PREPAY_SUCCESS: boolean;
  PREPAY_RESULT: IPrepayResult;
  EXTERNAL_POINT_ID?: string;
  IS_MERGE_PREPAY: string;
  BUYER_NAME?: string;
  IS_POINTS_ORDER?: string;
  IS_PARENT_ORDER: number;
  IS_MEMBER: string;
  IS_SPLIT_STOCK_DEDUCT?: boolean;
  BIZ_ORDER_ATTRIBUTE?: string;
  extraCustomerCardId?: string;
  OWNER_ID: string;
  BUYER_PHONE?: string;
  excludePayToolCode?: string;
  excludePayTool?: string;
  PAY_RETURN_URL?: string;
  ORDER_FIRST_COMMENT?: string;
  AUTO_RECEIVE_TIME?: string;
  DECREASE?: string;
  IS_PURCHASE?: string;
  FX_KDT_ID?: string;
  FENXIAO_PATCH_PAY?: string;
  IS_PURCHASE_NEW_SETTLEMENT_STRATEGY?: string;
  EXT_ORDER_STATUS?: string;
  FX_ORDER_NO?: string;
  FX_OUTER_TRANSACTION_NO?: string;
  FX_INNER_TRANSACTION_NO?: string;
  INVOICE?: string;
  PURCHASE_ORDER_NO?: string;
  bizOrderAttribute?: IExtraBizOrderAttribute;
}

export interface IExtraBizOrderAttribute {
  wholesaleSnapshot?: IExtraWholesaleSnapshot;
}

export interface IExtraWholesaleSnapshot {
  levelId?: number;
  levelName?: string;
  name?: string;
}

export interface IPrepayResult {
  cashierSign: string;
  acquireOrder: string;
  cashierSalt: string;
  partnerId: string;
  prepayId: string;
}

export interface IPermission {
  allowAccept: boolean;
  allowChangeAddress: boolean;
  allowChangePrice: boolean;
  allowCheckIn: boolean;
  allowClose: boolean;
  allowClosePeerpayOrder: boolean;
  allowConfirmCityOrder: boolean;
  allowKnowledgePayViewSingUpInfo: boolean;
  allowMarkRefund: boolean;
  allowNoExpressSend: boolean;
  allowPickOrder: boolean;
  allowPrint: boolean;
  allowPrintEatInOrder: boolean;
  allowRefund: boolean;
  allowRefundSend: boolean;
  allowRefusePickOrder: boolean;
  allowReject: boolean;
  allowRejectCityOrder: boolean;
  allowSafeguard: boolean;
  allowSellerSign: boolean;
  allowSellerUnSign: boolean;
  allowSend: boolean;
  allowSign: boolean;
  allowTicket: boolean;
  allowUnpack: boolean;
  allowUpExpressSend: boolean;
  allowRemindOrderButton: boolean;
  allowAuditWholesaleOrder?: boolean;
}

export interface IPeerpayOrder {
  refundPaySum: string;
}

export interface IOrderTag {
  isMergePay: boolean;
  isGroupBuy?: boolean;
}

export interface IGoodsItem {
  alias: string;
  csStatus: number;
  extend: IExtend;
  extraMap: IItemExtraMap;
  feedback: number;
  feedbackType: FEEDBACK_TYPE;
  goodsId: number;
  goodsInfo: IGoodsInfo;
  goodsSnap: string;
  goodsSnapUrl: string;
  goodsType: number;
  id: string;
  imageUrl: string;
  // 商品是否已发货
  isShipped: boolean;
  isPresent: boolean;
  itemId: string;
  itemTotalPrice: number;
  kdtId: number;
  markRefundAble: number;
  message?: object;
  num: number;
  orderNo: string;
  payPrice: string;
  payPriceLong: number;
  price: string;
  priceLong: number;
  refundNum: number;
  shopInfo: object;
  sku: ISku[];
  skuCode: string;
  skuId: number;
  tariff: ITariff;
  tcOrderItemId: string;
  title: string;
  url: string;
  outItemRefundInfo: IItemRefundInfo;
}

export interface IGoodsItemWithPeriod extends IGoodsItem {
  feedbackStr?: string;
  planExpressTime?: string;
  periodOrderDetail?: IPeriodOrderDetail;
  enjoyBuyInfo?: IPeriodOrderDetail;
}

export interface ITariff {
  tariffDesc: string;
  tariffDiscount: string;
  tariffFreight: string;
  tariffPay: string;
  tariffRate: string;
  tariffTag: number;
}

export interface ISku {
  k: string;
  kId: number;
  v: string;
  vId: string;
}

export interface IGoodsInfo {
  alias: string;
  buy_way: number;
  class1: number;
  class2: string;
  extra: string;
  goods_id: number;
  goods_no?: string;
  img_url: string;
  is_virtual: number;
  mark: number;
  points_price: number;
  quota: number;
  title: string;
  effective_delay_hours?: number;
  effective_type?: number;
  holidays_available?: number;
  virtual_cart_start_effect_time?: number;
}

export interface IExtend {
  feedback: number;
}

export interface IDeliveryAutomaticThirdCall {
  isShowAutoBar: boolean;
  automaticDelivery?: boolean;
  appointedDeliveryTime?: number;
  automaticStatus?: number;
  desc?: string;
}

export interface IDimension {
  category: string;
  weight: number;
  name: string;
  title: string;
}

export interface IGetCustomizedExportFieldstFieldsResponse {
  bizType: string;
  dimension: string;
  fields: string[];
  kdtId: number;
  options: string;
  extra: string;
}

export interface IExportFieldsOptions extends IOrderSearchQuery {
  exportType: IExportType;
  dimension: IDimensionType | '';
}

export type IExportType = 'default' | 'customized';
export type IDimensionType = 'order' | 'goods';
export type ISource = '' | 'wholesale'

export interface ITimeChangeOptions {
  value: [number, number];
  chooseDays: number;
}

export interface IPromoterRequestParam {
  nickName: string;
}
