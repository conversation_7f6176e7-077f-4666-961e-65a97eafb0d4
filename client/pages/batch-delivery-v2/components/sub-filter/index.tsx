import React, { FC } from 'react';
// import { IOrderSearchQuery } from '../../types';
import SubFilterSelect from './components/Select';
import { TIME_SORT_OPTIONS, SELLER_REMARK, BUYER_MEMO_LIST, STAR_LIST } from './contants';

import './sub-filter.scss';
import { isRetailShop } from '@youzan/utils-shop';

export interface IProps {
  onFilterChange: (filterOptions?: any) => void;
  filterOptions: any;
}

const FilterSelectList = isRetailShop
  ? [
      {
        placeholder: '下单时间排序',
        filterKey: 'orderby',
        subFilterKey: 'order',
        data: TIME_SORT_OPTIONS,
        width: 160,
      },
    ]
  : [
      {
        placeholder: '是否备注',
        filterKey: 'sellerRemark',
        data: SELLER_REMARK,
        width: 120,
      },
      {
        placeholder: '是否留言',
        filterKey: 'buyerMemo',
        data: BUYER_MEMO_LIST,
        width: 120,
      },
      {
        placeholder: '是否加星',
        filterKey: 'starNum',
        data: STAR_LIST,
        width: 120,
      },
      {
        placeholder: '下单时间排序',
        filterKey: 'orderby',
        subFilterKey: 'order',
        data: TIME_SORT_OPTIONS,
        width: 160,
      },
    ];

const SubFilter: FC<IProps> = ({ onFilterChange, filterOptions }) => (
  <div className="sub-filter-wrap">
    <div className="mini-selector-group">
      {FilterSelectList.map((item) => (
        <SubFilterSelect
          key={item.filterKey}
          placeholder={item.placeholder}
          width={item.width}
          filterKey={item.filterKey}
          data={item.data}
          value={
            item.subFilterKey
              ? `${filterOptions[item.filterKey]}/${filterOptions[item.subFilterKey]}`
              : filterOptions[item.filterKey]
          }
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
        />
      ))}
    </div>
  </div>
);

export default SubFilter;
