import React, { FC, useContext, useEffect, useMemo } from 'react';
import { Button, Notify } from 'zent';
import { Form } from '@zent/compat';

import { IQuery } from '../types';
import { ContainerContext } from '../context/CSVImportContext';
import { BatchDeliveryPrintTaskStatus } from 'constants/express';
import { deliveryBizOptions } from '../constants';
import getEvent from 'fns/event';
import { BatchPrintTaskProgressDialogCloseEvent } from '@youzan/order-domain-pc-components/es/batch-express-delivery/batch-print-dialog';
import { isHqStore } from '@youzan/utils-shop';
import { getWarelouseList } from '../api';

const { createForm, FormDateRangePickerField, FormSelectField, FormInputField } = Form;

const current = new Date();

const monthGap = 3;

const Filters: FC<{}> = () => {
  const { queries = {}, handleSearch, handleQueryChange } = useContext(ContainerContext);
  const { startDate, endDate, batchStatus, batchBiz, operator, deliveryPointId } = queries;
  const [warehouseList, setWarehouseList] = React.useState([]);

  const handleSubmit = () => {
    if (startDate || endDate) {
      if (!startDate) {
        Notify.error('请选择开始日期');
        return;
      }
      if (!endDate) {
        Notify.error('请选择结束日期');
        return;
      }
    }

    handleSearch();
  };

  const handleDateChange = (val) => {
    const [startDate, endDate] = val;
    handleQueryChange({
      startDate,
      endDate,
    });
  };

  const handleValChange = (key: keyof IQuery, val: string | number) => {
    handleQueryChange({
      [key]: val,
    });
  };

  const disabledRangeDate = (val) => {
    let m1 = current.getMonth();
    const y1 = current.getFullYear();

    const m2 = val.getMonth();
    const y2 = val.getFullYear();

    m1 += (y1 - y2) * 12;
    if (m2 > m1) {
      return true;
    }
    if (m1 === m2) {
      return val.getDate() > current.getDate();
    }
    if (m2 === m1 - monthGap) {
      return val.getDate() < current.getDate();
    }
    if (m2 < m1 - monthGap) {
      return true;
    }
    return false;
  };

  const handleRefresh = () => {
    handleSearch();
  };
  useEffect(() => {
    getEvent().on(BatchPrintTaskProgressDialogCloseEvent, handleRefresh);
    return () => {
      getEvent().off(BatchPrintTaskProgressDialogCloseEvent, handleRefresh);
    };
  }, []);

  const renderDateFilter = () => {
    return (
      <FormDateRangePickerField
        className="search-filter-field-wrap"
        name="date"
        label="时间："
        value={[startDate, endDate]}
        disabledDate={disabledRangeDate}
        onChange={handleDateChange}
      />
    );
  };

  const renderBatchBizFilter = () => {
    return (
      <FormSelectField
        name="batchBiz"
        label="操作类型："
        width={184}
        data={deliveryBizOptions}
        value={batchBiz}
        onChange={(val) => handleValChange('batchBiz', val)}
        resetOption
        resetText="全部"
      />
    );
  };

  const renderStatusFilter = () => {
    const status = [
      {
        text: '全部',
        value: 0,
      },
      {
        text: '进行中',
        value: 1,
      },
      {
        text: '已完成',
        value: 2,
      },
      {
        text: '任务中断',
        value: BatchDeliveryPrintTaskStatus.BreakOff,
      },
    ];
    return (
      <FormSelectField
        className="search-filter-field-wrap"
        name="status"
        width={184}
        label="状态："
        data={status}
        value={batchStatus}
        onChange={(val) => handleValChange('batchStatus', val)}
      />
    );
  };

  const renderOperatorFilter = () => {
    return (
      <FormInputField
        label="操作人："
        name="operator"
        type="text"
        width={184}
        value={operator}
        className="filter-input-field"
        showClear
        onChange={(e) => handleValChange('operator', e.target.value)}
      />
    );
  };

  const warehouseListData = useMemo(() => {
    return warehouseList.map((item) => {
      return {
        ...item,
        value: item.warehouseId,
        text: item.name,
      };
    });
  }, [warehouseList]);

  const renderStoreFilter = () => {
    return (
      <FormSelectField
        name="deliveryPointId"
        width={184}
        label="发货方："
        data={warehouseListData}
        value={deliveryPointId}
        resetOption
        resetText="全部"
        filter={(item, keyword) => {
          if (!keyword || !keyword.trim() || item.value === null) {
            return true;
          }
          return item.text.includes(keyword.trim());
        }}
        onChange={(val) => handleValChange('deliveryPointId', val)}
      />
    );
  };

  const renderSubmit = () => {
    return (
      <div className="csv-import-filters__actions">
        <Button type="primary" onClick={handleSubmit}>
          筛选
        </Button>
      </div>
    );
  };

  useEffect(() => {
    getWarelouseList().then((res) => {
      setWarehouseList(res.items);
    });
  }, []);

  return (
    <Form className="csv-import-filters" inline>
      <div className="csv-import-filters__input-group">
        {renderDateFilter()}
        {renderBatchBizFilter()}
        {renderStatusFilter()}
        {renderOperatorFilter()}
        {isHqStore && renderStoreFilter()}
      </div>
      <div>{renderSubmit()}</div>
    </Form>
  );
};

export default createForm()(Filters);
