import React, { FC, useMemo, useEffect } from 'react';
import { Form, FormControl, DateRangeQuickPicker, FormStrategy, Input } from 'zent';
import SelectCompat from 'components/zent10-compat/select-compat';
import SubFilter from './sub-filter';
import { IFilterOptions } from './BatchSelectDrawer';
import { BatchSelectType } from '../constants';
import { getRetailOrderTypeList, getWarelouseList } from '../api';
import { isHqStore, isRetailShop } from '@youzan/utils-shop';

interface IProps {
  onChangeFilters: (value: IFilterOptions) => void;
  filterOptions: IFilterOptions;
  batchSelectType: BatchSelectType;
}

const BatchSelectFilters: FC<IProps> = ({ onChangeFilters, filterOptions, batchSelectType }) => {
  const form = Form.useForm(FormStrategy.View);
  const [warehouseList, setWarehouseList] = React.useState([]);
  const [retailOrderTypeList, setRetailOrderTypeList] = React.useState([]);

  useEffect(() => {
    getWarelouseList().then((data) => {
      setWarehouseList(data.items);
    });
    getRetailOrderTypeList().then((data) => {
      const orderTypeList = data.map((item) => {
        return {
          value: item.paramValue,
          text: item.display,
        };
      });
      setRetailOrderTypeList([
        {
          value: null,
          text: '全部',
        },
        ...orderTypeList,
      ]);
    });
  }, []);

  const warehouseListData = useMemo(() => {
    return warehouseList.map((item) => ({
      value: item.warehouseId,
      text: item.name,
    }));
  }, [warehouseList]);

  const DatePickerField: FC = () => {
    const dateSearch = Form.useField('date', {
      value: [0, 0],
      chosenDays: -1,
    });

    const handleChangeOrderSearch = (value, chosenDays) => {
      dateSearch.patchValue({ value, chosenDays });
      form.submit();
    };

    const dateQuickList = [
      {
        text: '今天',
        value: 0,
      },
      {
        text: '昨天',
        value: 1,
      },
      {
        text: '近7天',
        value: 7,
      },
      {
        text: '近30天',
        value: 30,
      },
    ];

    return (
      <FormControl label="下单时间： ">
        <DateRangeQuickPicker
          format="YYYY-MM-DD HH:mm:ss"
          // @ts-ignore
          value={dateSearch.value.value}
          chosenDays={dateSearch.value.chosenDays}
          // @ts-ignore
          onChange={handleChangeOrderSearch}
          preset={dateQuickList}
        />
      </FormControl>
    );
  };

  const FormInputField: FC = () => {
    const orderSearch = Form.useField('goodsTitle', '');

    const handleChangeOrderSearch = (e) => {
      orderSearch.patchValue(e.target.value);
    };

    return (
      <FormControl label="商品名称： ">
        <Input
          value={orderSearch.value}
          width={180}
          onChange={handleChangeOrderSearch}
          onPressEnter={() => form.submit()}
          placeholder="请输入"
        />
      </FormControl>
    );
  };

  const FormSelectField: FC = () => {
    const isPrintDelivery = BatchSelectType.printDelivery === batchSelectType;
    const defaultValue = isPrintDelivery ? 'express' : 'all';
    const orderSearch = Form.useField('expressType', defaultValue);

    const handleChangeOrderSearch = (e) => {
      orderSearch.patchValue(e.target.value);
      form.submit();
    };

    const selectList = useMemo(() => {
      if (BatchSelectType.printDelivery === batchSelectType) {
        return [{ value: 'express', text: '快递发货' }];
      }
      return [
        { value: 'all', text: '全部' },
        { value: 'express', text: '快递发货' },
        { value: 'city', text: '同城配送' },
        { value: 'selffetch', text: '上门自提' },
      ];
    }, [batchSelectType]);

    return (
      <FormControl label="配送方式： " style={{ marginLeft: 20 }}>
        <SelectCompat
          width={180}
          data={selectList}
          value={orderSearch.value}
          onChange={handleChangeOrderSearch}
        />
      </FormControl>
    );
  };

  const FormOrderTypeSelectField: FC = () => {
    const orderSearch = Form.useField('type', 'all');

    const handleChangeOrderSearch = (e) => {
      orderSearch.patchValue(e.target.value);
      form.submit();
    };

    const selectList = [
      {
        value: 'all',
        text: '全部',
      },
      {
        value: 'self',
        text: '自营订单',
      },
      {
        value: 'normal',
        text: '普通订单',
      },
      {
        value: 'tuan',
        text: '多人拼团订单',
      },
      {
        value: 'period',
        text: '周期购订单',
      },
      {
        value: 'enjoy_buy',
        text: '随心订订单',
      },
      {
        value: 'cashier',
        text: '扫码收款',
      },
      {
        value: 'pointstore',
        text: '积分兑换订单',
      },
      {
        value: 'is_cross_border',
        text: '海淘订单',
      },
      {
        value: 'mall_group_buy',
        text: '社区团购订单',
      },
      {
        value: 'presale',
        text: '定金预售订单',
      },
      {
        value: 'peerpay',
        text: '代付订单',
      },
      {
        value: 'lottery',
        text: '抽奖拼团订单',
      },
      {
        value: 'giftcard',
        text: '储值卡/礼品卡订单',
      },
      {
        value: 'wish',
        text: '心愿订单',
      },
      {
        value: 'cutdown_price',
        text: '砍价0元购订单',
      },
      {
        value: 'is_fission_order',
        text: '品牌内购订单',
      },
      {
        value: 'blind_box_verification',
        text: '盲盒兑换订单',
      },
      {
        value: 'time_limit_seckill_order',
        text: '限时秒杀订单',
      },
      {
        value: 'self_pick_up',
        text: '订货商自主提货订单',
      },
      {
        value: 'wholesale',
        text: '批发订单',
      },
      {
        value: 'recharge_order_free',
        text: '充值优惠订单',
      },
      {
        value: 'fcode',
        text: 'F码订单',
      },
      {
        value: 'online_scan_buy',
        text: '扫码购订单',
      },
      {
        value: 'blind_box',
        text: '盲盒订单',
      },
      {
        value: 'purchase_pay_online',
        text: '订货商申购线上支付订单',
      },
      {
        value: 'solitaire_buy_order',
        text: '社群接龙订单',
      },
    ];
    const orderTypeList = isRetailShop ? retailOrderTypeList : selectList;

    return (
      <FormControl label="订单类型： " style={{ marginLeft: 20 }}>
        <SelectCompat
          width={180}
          data={orderTypeList}
          value={orderSearch.value}
          onChange={handleChangeOrderSearch}
        />
      </FormControl>
    );
  };

  const FormShipperField: FC = () => {
    const shipperSearch = Form.useField('warehouseId', '');

    const handleChangeShipperSearch = (e) => {
      shipperSearch.patchValue(e.target.value);
      form.submit();
    };

    return (
      <FormControl label="发货方： " style={{ marginLeft: 20 }}>
        <SelectCompat
          width={180}
          data={[{ value: '', text: '全部' }, ...warehouseListData]}
          value={shipperSearch.value}
          onChange={handleChangeShipperSearch}
        />
      </FormControl>
    );
  };

  const handleChangeValue = () => {
    const { date, goodsTitle, expressType, type, warehouseId } = form.getValue();
    onChangeFilters({ date, goodsTitle, expressType, type, warehouseId } as IFilterOptions);
  };

  return (
    <div className="batch-select-drawer__container-filters">
      <Form form={form} onSubmit={handleChangeValue}>
        <div className="form-group">
          <DatePickerField />
          {isHqStore && <FormShipperField />}
        </div>
        <div className="form-group">
          <FormInputField />
          <FormSelectField />
          <FormOrderTypeSelectField />
        </div>
      </Form>
      <SubFilter onFilterChange={onChangeFilters} filterOptions={filterOptions} />
    </div>
  );
};

export default BatchSelectFilters;
