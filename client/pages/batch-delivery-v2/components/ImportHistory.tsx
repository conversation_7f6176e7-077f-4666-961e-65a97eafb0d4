import React, { FC, useContext, useEffect } from 'react';
import format from 'date-fns/format';
import { Grid, IGridColumn } from 'zent';
import { BlankLink, SatisfactionDialog } from '@youzan/react-components';

import BatchSelectHistoryDialog from './BatchSelectHistoryDialog';

import { ContainerContext } from '../context/CSVImportContext';

import { DeliveryType, BIZ_OP } from '../constants';
import { BatchDeliveryPrintTaskStatus, BatchDeliveryPrinterTaskType } from 'constants/express';
import { openBatchPrintTaskProgressDialog } from '@youzan/order-domain-pc-components/es/batch-express-delivery/batch-print-dialog/index';
import '@youzan/order-domain-pc-components/css/batch-express-delivery/index.css';
import { closeReprintDialog, openReprintDialog } from 'components/reprint-dialog';
import { isHqStore } from '@youzan/utils-shop';

// 上报用户行为接口
const { reportActions } = SatisfactionDialog;
const RULE_NAME = 'wsc_pc_trade_batch_delivery'; // Apollo: wsc-survey

const ImportHistory: FC<{}> = () => {
  const handleContinueBatchTask = batchNo => {
    openReprintDialog({
      title: '继续操作',
      onConfirm(values) {
        closeReprintDialog();
        openBatchPrintTaskProgressDialog({
          batchNo,
          type: BatchDeliveryPrinterTaskType.Continue,
          ...values,
        });
      },
    });
  };

  const {
    list,
    totalItem,
    current: currentProps,
    loading,
    pageSize,
    satisfactionDialogVisible,
    handlePageChange,
    handleShowHistoryDialog,
  } = useContext(ContainerContext);

  // 总部新增发货方列
  const warehouseColumns = isHqStore ? [{
    title: '发货方',
    name: 'deliveryPointName',
    bodyRender: ({ deliveryPointName }) => {
      return deliveryPointName || '-';
    },
  }] : [];

  const columns = [
    {
      title: '记录编号',
      name: 'batchNo',
      className: 'name',
    },
    ...warehouseColumns,
    {
      title: '操作时间',
      name: 'operateTime',
      bodyRender: ({ operateTime }) => {
        return format(operateTime, 'YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作人',
      name: 'operator',
    },
    {
      title: '操作类型',
      name: 'batchBiz',
      bodyRender: ({ batchBiz }) => {
        const op = BIZ_OP[batchBiz];
        return op || '-';
      },
    },
    {
      title: '发货成功单数',
      name: 'successAmount',
      bodyRender: ({ reportStatus, successAmount }) => {
        return reportStatus === 1 || reportStatus === 2 ? '-' : successAmount;
      },
    },
    {
      title: '发货失败单数',
      name: 'failAmount',
      bodyRender: ({ reportStatus, failAmount }) => {
        return reportStatus === 1 || reportStatus === 2 ? '-' : failAmount;
      },
    },
    {
      title: '状态',
      name: 'batchStatus',
      bodyRender: ({ batchStatus = 1 }) => {
        if (batchStatus === BatchDeliveryPrintTaskStatus.BreakOff) {
          return '任务中断';
        }
        return batchStatus === 1 ? '进行中' : '已完成';
      },
    },
    {
      title: '操作',
      textAlign: 'right',
      bodyRender: ({
        unprocessed,
        totalAmount,
        filePath,
        reportStatus,
        batchNo,
        reportStatusDesc,
        successAmount,
        batchBiz,
        batchStatus,
      }) => {
        if (batchBiz === DeliveryType.BATCH_DELIVERY_PRINT || batchBiz === DeliveryType.BATCH_DELIVERY_CLOUD_PRINT) {
          return (
            <div>
              {batchStatus === BatchDeliveryPrintTaskStatus.BreakOff && (
                <a style={{ marginRight: 8 }} onClick={() => handleContinueBatchTask(batchNo)}>
                  继续操作
                </a>
              )}
              <a
                target="_blank"
                href={`/v4/trade/delivery/batch-detail/batch-delivery-print?batchNo=${batchNo}`}
              >
                查看详情
              </a>
            </div>
          );
        }
        switch (reportStatus) {
          case 1: {
            let progress = 0;
            if (totalAmount !== 0) {
              progress = +((totalAmount - unprocessed) / totalAmount).toFixed(2) * 100;
            }
            return `正在处理 ${progress}%`;
          }
          case 2: {
            // 批量选择发货任务进行中
            if (batchBiz === DeliveryType.BATCH_TICKOFF_DELIVERY) {
              return '-';
            }
          }
          case 3: {
            if (batchBiz === DeliveryType.BATCH_TICKOFF_DELIVERY) {
              return <a onClick={() => handleShowHistoryDialog(true, batchNo)}>查看发货记录</a>;
            }
            const opts = `?attname=${batchNo}.csv`;
            const optName = totalAmount === successAmount ? '下载发货记录' : '查看失败原因';
            return filePath ? <BlankLink href={`${filePath}${opts}`}>{optName}</BlankLink> : '-';
          }
          case 4:
            return '记录过期，无法下载';
          default:
            return reportStatusDesc || '-';
        }
      },
    },
  ] as IGridColumn[];

  const onChange = ({ current }: { current?: number }) => {
    if (currentProps !== current) {
      handlePageChange(current!);
    }
  };

  useEffect(() => {
    satisfactionDialogVisible && reportActions({ rule: RULE_NAME });
  }, [satisfactionDialogVisible]);

  return (
    <>
      <Grid
        rowKey="batchNo"
        columns={columns}
        datasets={list}
        loading={loading}
        pageInfo={{
          current: currentProps,
          pageSize,
          total: totalItem,
        }}
        onChange={onChange}
      />
      <BatchSelectHistoryDialog />
      {satisfactionDialogVisible && <SatisfactionDialog cesRule={RULE_NAME} />}
    </>
  );
};

export default ImportHistory;
