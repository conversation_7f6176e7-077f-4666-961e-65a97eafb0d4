import React from 'react';
import { Dialog, Button, Notify, Radio, Alert } from 'zent';
import filesize from 'filesize';
import Upload, { ICustomFile, IUploadInject, IUploadResponse } from './Upload';
import * as api from '../api';
import {
  isUnifiedShop,
  isPartnerStore,
  isRetailSingleStore,
  isUnifiedHqStore,
  isSingleStore,
  ShopAbilityStatus,
  isWscSingleStore,
  isEduShop,
  ShopAbility,
  isRetailShop,
} from '@youzan/utils-shop';

const openDialog = Dialog.openDialog;
const closeDialog = Dialog.closeDialog;
const RadioGroup = Radio.Group;

const isWscShop = isWscSingleStore && !isEduShop; // isWscSingleStore包含了教育类型店铺

// 微商城单店和零售店铺都支持新的弹窗样式
// https://xiaolv.qima-inc.com/#/project/PROJ-15501
const showViewV2 = isWscShop || isRetailShop;

const stylePrefix = showViewV2 ? 'wsc-' : '';

const DIALOG_ID = 'wsc-batch-delivery-dialog';

// 是否有供应链能力
const hasSupplyChainAbility =
  ShopAbilityStatus.Available ===
  window._global?.shopAbilityInfo?.[ShopAbility.SupplyChainAbility]?.abilityStatus;

// 最大只能上传 1M 大小的文件
const MAX_SIZE = 1 * 1024 * 1024;
// 库存同步模式：0:供货，1:铺货
const isVirtualSupplyMode = window._global.shopSupplyMode === 0;
const RETAIL_MODIFY_DELIVERY_TEMPLATE_URL =  '//b.yzcdn.cn/public_files/2018/10/22/batch-delivery-template.csv?attname=';
let DELIVERY_TEMPLATE_URL =
  '//b.yzcdn.cn/public_files/2018/10/22/batch-delivery-template.csv?attname=';
let DELIVERY_SPLIT_TEMPLATE_URL =
  '//b.yzcdn.cn/public_files/2020/12/31/batch-delivery-template.split.order.csv?attname=';

if (isUnifiedShop) {
  if (isUnifiedHqStore || isPartnerStore) {
    // 总店或合伙人
    if (hasSupplyChainAbility) {
      // 供应链能力生效
      DELIVERY_TEMPLATE_URL =
        '//b.yzcdn.cn/public_files/2020/03/10/batch-delivery-template.csv?attname=';
      DELIVERY_SPLIT_TEMPLATE_URL =
        '//b.yzcdn.cn/public_files/2020/12/31/batch-delivery-template.split.fulfill.csv?attname=';
    } // 否则还是订单模板
  } else {
    /** 库存同步模式：0:供货，1:铺货 */
    if (isVirtualSupplyMode) {
      // 供货是按发货单模板
      DELIVERY_TEMPLATE_URL =
        '//b.yzcdn.cn/public_files/2020/03/10/batch-delivery-template.csv?attname=';
      DELIVERY_SPLIT_TEMPLATE_URL =
        '//b.yzcdn.cn/public_files/2020/12/31/batch-delivery-template.split.fulfill.csv?attname=';
    } // 否则还是订单模板
  }
}

// 修改物流模板
const RETAIL_MODIFY_DELIVERY_TEMPLATE = `${RETAIL_MODIFY_DELIVERY_TEMPLATE_URL}${encodeURIComponent('批量发货模板')}.csv`;
const DELIVERY_TEMPLATE = `${DELIVERY_TEMPLATE_URL}${encodeURIComponent('批量发货模板')}.csv`;

const DELIVERY_SPLIT_TEMPLATE = `${DELIVERY_SPLIT_TEMPLATE_URL}${encodeURIComponent(
  '批量发货模板（支持拆单）'
)}.csv`;

const WSC_DELIVERY_TEMPLATE = `https://b.yzcdn.cn/public_files/cfe5ca23cf0f689cfa900cd2e44eacc2.xls?attname=${encodeURIComponent(
  '整单发货模板'
)}.xls`;

const WSC_DELIVERY_SPLIT_TEMPLATE = `https://b.yzcdn.cn/public_files/f8cd17248802f3487c9695f7cd5c2464.xls?attname=${encodeURIComponent(
  '拆单发货模板'
)}.xls`;

const WSC_HELP_DESC = '最大支持10000条记录，支持csv、xls、xlsx，文件大小请控制在1MB以内';

const TRADE_SETTING_URL = isSingleStore
  ? `${_global.url.store}/v2/order/trade-setting`
  : `${_global.url.store}/v4/setting/team#trade`;

export const UPLOAD_TYPES = {
  UPLOAD: '1',
  MODIFY: '2',
};

export enum TemplateType {
  /** 发货模板 */
  Normal,
  /** 发货模板（支持拆单） */
  Split,
  /** wsc多物流单号模板 */
  Multi,
}

enum UploadStatus {
  Empty = 0,
  Selected = 1,
}

// 微商城单店
const wscTemplateOptions = [
  {
    key: TemplateType.Normal,
    text: '整单发货模板',
  },
  {
    key: TemplateType.Multi,
    text: '拆单发货模板',
  },
];

// 新零售模板
const retailTemplateV2Options = [
  {
    key: TemplateType.Normal,
    text: '整单发货模板',
  },
];

// 零售模板
const retailTemplateOptions = [
  {
    key: TemplateType.Normal,
    text: '发货模板',
  },
  {
    key: TemplateType.Split,
    text: '拆单发货模板',
  },
];

interface IProps {
  type: string;
  callback: (s: string, templateType: TemplateType) => void;
}

class UploadDialogBody extends React.Component<IProps> {
  state = {
    templateType: TemplateType.Normal,
    isOpenSignMore: true, // 是否开启“单品多数量发货”开关
  };

  componentDidMount() {
    api.getTradeSettingData().then((data) => {
      const { isOpenSignMore } = data;
      this.setState({ isOpenSignMore });
    });
  }

  getToken = () => {
    return api.getUploadToken().catch((err) => {
      Notify.error('获取文件上传token失败');
      return Promise.reject(err);
    });
  };

  handleUploadSuccess = (filePath: IUploadResponse[]) => {
    const { callback } = this.props;
    if (filePath && filePath.length === 1) {
      callback(filePath[0].attachment_url, this.state.templateType);
    }
    closeDialog(DIALOG_ID);
  };

  handleUploadFail = (err: string) => {
    Notify.error(err || '文件上传失败，请重试');
  };

  onTemplateTypeChange = (e) =>
    this.setState({
      templateType: e.target.value,
    });

  // wsc下使用 下载链接样式不同
  renderTemplateLink() {
    const {type} = this.props;
    const { templateType } = this.state;
    const isModity = type === UPLOAD_TYPES.MODIFY;
    return (
      <div className={`${stylePrefix}batch-delivery-upload-link`}>
        {templateType === TemplateType.Normal ? (
          // 和模板类型Radio对齐
          <a href={ isRetailShop ? isModity ? RETAIL_MODIFY_DELIVERY_TEMPLATE : DELIVERY_TEMPLATE : WSC_DELIVERY_TEMPLATE} style={{ marginLeft: 70 }}>
            下载整单发货模板
          </a>
        ) : (
          <a href={ isRetailShop ? DELIVERY_SPLIT_TEMPLATE : WSC_DELIVERY_SPLIT_TEMPLATE} style={{ marginLeft: 193 }}>
            下载拆单发货模板
          </a>
        )}
      </div>
    );
  }

  // 零售和wsc公用 函数内存在判断逻辑
  renderTemplateTypeSelect() {
    const { type } = this.props;
    let runtimeTemplate = wscTemplateOptions;
    if (!isWscShop) {
      runtimeTemplate = retailTemplateOptions;
      // 零售修改物流不支持拆单
      if (isRetailShop && type === UPLOAD_TYPES.MODIFY) {
        runtimeTemplate = retailTemplateV2Options;
      }
    }

    const field = (
      <div className={`${stylePrefix}batch-delivery__upload-field`}>
        模板类型：
        <RadioGroup
          className="batch-delivery__upload-template"
          onChange={this.onTemplateTypeChange}
          value={this.state.templateType}
        >
          {runtimeTemplate.map((v) => (
            <Radio key={v.key} value={v.key}>
              {v.text}
            </Radio>
          ))}
        </RadioGroup>
        {/* wsc单店下 链接下载交互不同 */}
        {showViewV2 && [UPLOAD_TYPES.UPLOAD, UPLOAD_TYPES.MODIFY].includes(type)
          ? this.renderTemplateLink()
          : null}
      </div>
    );

    if (showViewV2) {
      return field;
    }

    /**
     * 仅零售单店和零售连锁支持拆单
     * 修改物流不支持拆单
     */
    if (!(isRetailSingleStore || isUnifiedShop) || type === UPLOAD_TYPES.MODIFY) {
      return null;
    }

    return field;
  }

  renderUploader(status: UploadStatus, trigger: () => void) {
    const uploadText = {
      [UploadStatus.Empty]: showViewV2 ? '上传文件' : '选择文件',
      [UploadStatus.Selected]: '重选',
    };
    return showViewV2 && status === UploadStatus.Empty ? (
      <Button onClick={trigger} className={`${stylePrefix}batch-delivery__upload-select`}>
        {uploadText[status]}
      </Button>
    ) : (
      <a onClick={trigger} className="batch-delivery__upload-select">
        {uploadText[status]}
      </a>
    );
  }

  // 零售和wsc公用 函数内存在判断逻辑
  renderFileSelect(trigger: () => void, file: ICustomFile) {
    const fieldName = showViewV2 ? '上传文件：' : '文件：';
    if (!file) {
      return (
        <div className={`${stylePrefix}batch-delivery__upload-field`}>
          {fieldName}
          {this.renderUploader(UploadStatus.Empty, trigger)}
          {showViewV2 && this.renderWscDescription()}
        </div>
      );
    }
    return (
      <div className={`${stylePrefix}batch-delivery__upload-field`}>
        {fieldName}
        {file.name}
        {this.renderUploader(UploadStatus.Selected, trigger)}
        {showViewV2 && this.renderWscDescription()}
      </div>
    );
  }

  // 上传文件大学 wsc和零售公用
  renderFileSize(file: ICustomFile) {
    if (!file) {
      return null;
    }
    return (
      <div className={`${stylePrefix}batch-delivery__upload-field`}>
        文件大小：
        {filesize(file.size)}
        {file.size > MAX_SIZE && <span className="batch-delivery__upload-maxsize">文件过大</span>}
      </div>
    );
  }

  // 零售单独使用
  renderDescription() {
    const { type } = this.props;
    const { templateType, isOpenSignMore } = this.state;

    if (type === UPLOAD_TYPES.MODIFY) {
      return (
        <ul className="batch-delivery-upload-help">
          <li>1、最大支持 10000 条记录（支持 csv、xls、xlsx，文件大小请控制在 1MB 以内）</li>
          <li>2、仅支持对72小时内自己联系物流或表格导入的已发货快递修改3次物流信息</li>
        </ul>
      );
    }

    return (
      <div className="batch-delivery-upload-help">
        最大支持 10000 条记录（支持 csv、xls、xlsx，文件大小请控制在 1MB 以内）
        <br />
        {templateType === TemplateType.Normal ? (
          <a href={DELIVERY_TEMPLATE}>下载批量发货模板</a>
        ) : (
          <>
            <a href={DELIVERY_SPLIT_TEMPLATE}>下载批量发货模板（支持拆单）</a>
            {!isOpenSignMore && (
              <span style={{ marginLeft: 10 }}>
                请开启单品多数量发货开关{' '}
                <a href={TRADE_SETTING_URL} target="_blank" rel="noreferrer">
                  去开启
                </a>
              </span>
            )}
          </>
        )}
        <div className="gray batch-delivery-upload-help-desc">
          *请仔细填写物流公司及快递单号，发货后72小时内仅支持做一次更正，逾期不可修改
        </div>
      </div>
    );
  }

  // 零售和wsc公用 函数内存在判断逻辑
  renderUploadBtn(upload, file: ICustomFile, isLoading: boolean = false) {
    const disabled = !file || file.size > MAX_SIZE;
    return (
      <div className="batch-delivery__upload-btn">
        {showViewV2 && (
          <Button className="wsc-cancel-btn" onClick={() => closeDialog(DIALOG_ID)}>
            取消
          </Button>
        )}
        <Button loading={isLoading} disabled={disabled} onClick={upload} type="primary">
          确认上传
        </Button>
      </div>
    );
  }

  renderAlert() {
    const { type } = this.props;
    const clz = 'wsc-batch-alert';
    // 不是微商城单店 不展示
    if (!showViewV2) {
      return null;
    }
    if (type === UPLOAD_TYPES.UPLOAD) {
      return (
        <Alert type="warning" className={clz}>
          请仔细填写物流公司及快递单号，发货后72小时内仅支持做3次修改，逾期不可修改。
        </Alert>
      );
    }
    if (type === UPLOAD_TYPES.MODIFY) {
      return (
        <Alert type="info" className={clz}>
          仅支持对72小时内发货方式为“自己联系物流”或“表格导入“的已发货快递修改一次物流信息。
        </Alert>
      );
    }
  }

  renderWscDescription() {
    return <div className="wsc-batch-delivery-upload-help-desc">{WSC_HELP_DESC}</div>;
  }

  renderWscContent = ({ trigger, files, upload, uploading }: IUploadInject) => {
    const file = files[0];
    return (
      <div className="batch-delivery__upload">
        {this.renderAlert()}
        {this.renderTemplateTypeSelect()}
        {this.renderFileSelect(trigger, file)}
        {this.renderFileSize(file)}
        {this.renderUploadBtn(upload, file, uploading)}
      </div>
    );
  };

  renderDefaultContent = ({ trigger, files, upload, uploading }: IUploadInject) => {
    const file = files[0];
    return (
      <div className="batch-delivery__upload">
        {this.renderTemplateTypeSelect()}
        {this.renderFileSelect(trigger, file)}
        {this.renderFileSize(file)}
        {this.renderDescription()}
        {this.renderUploadBtn(upload, file, uploading)}
      </div>
    );
  };

  render() {
    return (
      <Upload
        accept=".csv,.xls,.xlsx"
        getToken={this.getToken}
        uploadSuccess={this.handleUploadSuccess}
        uploadFail={this.handleUploadFail}
      >
        {showViewV2 ? this.renderWscContent : this.renderDefaultContent}
      </Upload>
    );
  }
}

export const openUploadDialog = (type: string, callback: any) => {
  let title = '批量发货';
  if (type === UPLOAD_TYPES.MODIFY) {
    title = '修改物流';
  }
  const width = showViewV2 ? '704px' : '580px';
  openDialog({
    title,
    dialogId: DIALOG_ID,
    className: 'batch-delivery__upload-dialog',
    footer: null,
    style: { width },
    children: <UploadDialogBody type={type} callback={callback} />,
  });
};
