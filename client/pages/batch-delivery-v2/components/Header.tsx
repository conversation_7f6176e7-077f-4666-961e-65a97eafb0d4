import React, { useContext } from 'react';
import { Button, Icon, Notify, Pop } from 'zent';
import { isRetailShop } from '@youzan/utils-shop';

import { ContainerContext } from '../context/CSVImportContext';
import { UPLOAD_TYPES, openUploadDialog } from './UploadDialog';
import { BatchSelectType, videoShopTextName } from '../constants';
import { WaybillVersionEnum } from 'constants/express';

const Header: React.FC<{}> = () => {
  const { progressing, uploadSuccess, changeDrawerVisible, modifySuccess, setBatchSelectType } =
    useContext(ContainerContext);
  // 零售不支持批量选择发货
  const opts = isRetailShop
    ? []
    : [
        {
          title: '批量选择发货',
          tips: (
            <>
              <p>暂不支持选择以下类型的订单：</p>
              <p>维权订单、{videoShopTextName}订单、交易组件3.0订单、分销</p>
              <p>买家单、分销供货单、酒店订单、我要送礼/我要送礼</p>
              <p>社群版订单、知识付费订单、堂食点单订单</p>
            </>
          ),
          desc: '针对无需物流的订单，直接选择多个进行批量发货',
          btnText: '批量发货',
          disabled: progressing,
          onClick: () => {
            setBatchSelectType(BatchSelectType.normal);
            changeDrawerVisible(true);
          },
        },
      ];

  const cardList = [
    {
      title: '批量打单发货',
      desc: (
        <>
          选择多个订单，批量打印面单并发货{' '}
          <a
            href="https://help.youzan.com/displaylist/detail_4_4-2-87294"
            target="_blank"
            rel="noopener noreferrer"
          >
            查看教程
          </a>
        </>
      ),
      btnText: '批量打单发货',
      disabled: progressing,
      onClick: () => {
        if (_global.electronWayBillVersion === WaybillVersionEnum.old) {
          return Notify.warn(
            '您当前使用的旧版电子面单系统暂不支持批量打单发货，请先到应用中心-电子面单页面升级新电子面单系统'
          );
        }
        setBatchSelectType(BatchSelectType.printDelivery);
        changeDrawerVisible(true);
      },
    },
    {
      title: '批量导入发货',
      tips: (
        <>
          · 批量发货目前仅支持快递订单，暂不支持自提订单和同城配送订单。
          <br />
          · 批量发货按模板要求填写发货信息，运单号不可写成“E+22”的格式。
          <br />· 批量发货文件若上传失败，可下载【查看失败原因】，对症修改。
        </>
      ),
      desc: (
        <>
          以表格形式导入订单，进行批量发货{' '}
          <a
            href="https://help.youzan.com/displaylist/detail_4_4-2-13711"
            target="_blank"
            rel="noopener noreferrer"
          >
            查看教程
          </a>
        </>
      ),
      btnText: '批量发货',
      disabled: progressing,
      onClick: () => openUploadDialog(UPLOAD_TYPES.UPLOAD, uploadSuccess),
    },
    ...opts,
    {
      title: '批量修改物流',
      desc: '对已发货的快递订单，批量修改物流信息',
      btnText: '批量修改',
      disabled: progressing,
      onClick: () => openUploadDialog(UPLOAD_TYPES.MODIFY, modifySuccess),
    },
  ];
  return (
    <div className="batch-delivery-header">
      <div className="batch-delivery-header__card-list">
        {cardList.map((item) => (
          <div className="batch-delivery-header__card-list-item">
            <div className="title">
              <span>{item.title}</span>
              {item.tips && (
                <Pop trigger="hover" position="top-left" centerArrow content={item.tips}>
                  <Icon type="help-circle" />
                </Pop>
              )}
            </div>
            <div className="desc">{item.desc}</div>
            <div className="btn">
              <Button type="primary" onClick={item.onClick} disabled={item.disabled}>
                {item.btnText}
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Header;
