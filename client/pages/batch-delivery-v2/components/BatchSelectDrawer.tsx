import React, { FC, useContext, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Pop, Notify } from 'zent';
import {
  openBatchDeliveryDialog,
  closeBatchDeliveryDialog,
} from '@youzan/order-domain-pc-components/es/batch-express-delivery';
import '@youzan/order-domain-pc-components/css/index.css';

import Drawer from '../../../components/drawer';
import BatchSelectFilters from './BatchSelectFilters';
import BatchSelectOrderList from './batch-select-order-list';

import { getBatchOrderList, batchSelectDelivery, getRetailBatchOrderList } from '../api';
import { ContainerContext } from '../context/CSVImportContext';
import { BatchSelectType } from '../constants';
import { isRetailShop } from '@youzan/utils-shop';
import { formatRetailBatchOrderListData, getRetailBatchOrderListParams } from '../utils';

export interface IFilterOptions {
  expressType?: string;
  goodsTitle?: string;
  date?: {
    value: string[];
    chosenDays: number;
  };
  sellerRemark?: string;
  buyerMemo?: string;
  star?: number;
  starNum?: string;
  orderby?: string;
  order?: string;
  type?: string;
  warehouseId?: string;
  // 过滤的订单类型
  excludeOrderType: string[];
  // 排除的订单来源
  excludeOrderSource: string[];
  feedback: string;
  state: string;
}

export interface IPageOptions {
  total: number;
  page: number;
  pageSize: number;
}

export interface ICheckedListItem {
  deliveryNo: any;
  fulfillNo: any;
  warehouseName: any;
  kdtId: any;
  warehouseId: any;
  orderNo: string;
  expressType: 0 | 1 | 2;
}

interface ISearchParams extends IFilterOptions, IPageOptions {}

const defaultFilterOptions = {
  order: 'desc',
  orderby: 'book_time',
  excludeOrderType: [
    // 分销买家订单
    'fx_buyer',
    // 酒店订单
    'hotel',
    // 送礼订单
    'gift',
    // 送礼社群版订单
    'gift_community',
    // 知识付费订单
    'knowledge',
    // 堂食订单
    'tangshi',
    // 分销供货订单
    'fenxiao',
    // 付费优惠券订单
    'pay_coupons',
  ],
  excludeOrderSource: [
    // 视频号
    'wx_channels',
    // 视频号小店
    'wx_xiao_dian',
  ],
  feedback: 'not_in_aftersales',
  state: 'tosend',
};

const defaultPageOptions = {
  total: 0,
  page: 1,
  pageSize: 20,
};

const defaultTotalCount = {
  express: 0,
  selfFetch: 0,
  city: 0,
};

// 限制的最多可勾选数量
const checkedLimitCount = 2000;
// 限制的最多可查看的订单数量
const searchLimitCount = 3000;

const BatchSelectDrawer: FC<{}> = () => {
  const {
    batchSelectDrawerVisile,
    batchSelectType,
    changeDrawerVisible,
    handleShowSatisfactionDialogVisible,
  } = useContext(ContainerContext);

  const [filterOptions, setFilterOptions] = useState<IFilterOptions>(defaultFilterOptions);
  // 已选的商品数据
  const [checkedList, setCheckedList] = useState<ICheckedListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [buttonLoading, setButtonLoading] = useState<boolean>(false);
  const [pageOptions, setPageOptions] = useState<IPageOptions>(defaultPageOptions);
  const [isCurrentCheckedAll, setIsCurrentCheckedAll] = useState<boolean>(false);
  const [confirmPopVisible, setConfirmPopVisible] = useState<boolean>(false);
  const [list, setList] = useState<any>([]);
  const [totalCount, setTotalCount] = useState<any>(defaultTotalCount);

  // 批量打单发货
  const isPrintDelivery = batchSelectType === BatchSelectType.printDelivery;

  const handleChangeFilters = (value: IFilterOptions) => {
    const currentValue = { ...filterOptions, ...value };
    setFilterOptions(currentValue);
    fetchData({ ...currentValue, ...pageOptions });
  };

  const handleChangeCheckedAll = (isChecked: boolean) => {
    // const checkedValue = list.map((item) => ({
    //   orderNo: item.orderNo,
    //   expressType: item.expressType,
    // }));
    handleChangeChecked(isChecked, list);
  };

  const handleChangeChecked = (isChecked, value) => {
    // 单项勾选
    const isSingeChecked = !(value instanceof Array);
    const checkedValue = isSingeChecked ? [value] : value;

    let newCheckedList = checkedList;
    if (isChecked) {
      checkedValue.forEach((item) => {
        // 不存在才添加
        if (!newCheckedList.find((v) => v.orderNo === item.orderNo && v.warehouseId === item.warehouseId)) {
          newCheckedList.push(item);
        }
      });
      if (newCheckedList.length > checkedLimitCount) {
        Notify.warn(`最多选择${checkedLimitCount}条订单`);
        newCheckedList = newCheckedList.slice(0, checkedLimitCount);
      }
    } else {
      newCheckedList = newCheckedList.filter(
        (item) => !checkedValue.find((v) => v.orderNo === item.orderNo && v.warehouseId === item.warehouseId)
      );
    }
    const isCheckedAll = list
      .map((item) => item.orderNo + item.warehouseId)
      .every((item) => newCheckedList.map((v) => v.orderNo + v.warehouseId).includes(item));

    changeTotalCount(newCheckedList);

    setCheckedList(newCheckedList);
    setIsCurrentCheckedAll(isCheckedAll);
  };

  const changeTotalCount = (checkedList) => {
    const checkedTotalCount = checkedList.reduce((total, item) => {
      const keys = Object.keys(defaultTotalCount);
      total[keys[item.expressType]] += 1;
      return total;
    }, JSON.parse(JSON.stringify(defaultTotalCount)));

    setTotalCount(checkedTotalCount);
  };

  const handleChangePage = ({ pageSize, current }) => {
    const value = {
      ...pageOptions,
      pageSize,
      page: current,
    };

    // 切换页数，但是不切换页码时，超出查询限制
    if (
      current * pageSize > searchLimitCount &&
      current !== pageOptions.page &&
      pageSize === pageOptions.pageSize
    ) {
      value.page = searchLimitCount / pageSize;
      if (value.page > 100) {
        value.page = 100;
        Notify.warn(`最多只能查看100页的数据`);
      } else {
        Notify.warn(`最多只能查看${searchLimitCount}条的数据`);
      }
    }

    // 切换页码
    if (pageSize !== pageOptions.pageSize) {
      value.page = 1;
      setCheckedList([]);
      setIsCurrentCheckedAll(false);
    }

    setPageOptions(value);
    fetchData({ ...filterOptions, ...value });
  };

  const getBatchOrderListFn = async (params) => {
    if (isRetailShop) {
      const retailParams = getRetailBatchOrderListParams(params);
      return getRetailBatchOrderList(retailParams).then(formatRetailBatchOrderListData);
    }
    return getBatchOrderList(params);
  };

  const fetchData = async ({
    date,
    sellerRemark,
    buyerMemo,
    goodsTitle,
    starNum,
    order,
    page,
    pageSize,
    orderby,
    expressType,
    type,
    excludeOrderType,
    excludeOrderSource,
    feedback,
    state,
    warehouseId,
  }: ISearchParams) => {
    const params = {
      startTime: date?.value[0] ?? '',
      endTime: date?.value[1] ?? '',
      sellerRemark,
      buyerMemo,
      goodsTitle,
      order,
      page,
      pageSize,
      orderby,
      expressType,
      type,
      excludeOrderType,
      excludeOrderSource,
      feedback,
      state,
      warehouseId,
    };
    // 批量打单发货，默认只有快递方式
    if (batchSelectType === BatchSelectType.printDelivery) {
      params.expressType = 'express';
    }
    Object.keys(params).forEach((key) => {
      if (!params[key] || params[key] === 'all') {
        delete params[key];
      }
    });
    if (starNum && starNum !== 'all') {
      const starNumFormat = JSON.parse(starNum);
      Object.assign(params, { starNum: starNumFormat, star: starNumFormat.length > 0 ? 1 : 0 });
    }
    setLoading(true);
    try {
      const { list, pageSize, pageNum, totalItems } = await getBatchOrderListFn(params);
      if (list.length > 0 && pageSize === pageOptions.pageSize) {
        setIsCurrentCheckedAll(
          list.every((item) => checkedList.map((v) => v.orderNo).includes(item.orderNo))
        );
      }
      setList(list);
      setPageOptions({ page: pageNum, pageSize, total: totalItems });
    } catch (err) {
      Notify.error(err?.message || err?.msg || err || '获取订单列表失败，请刷新重试');
    }
    if (batchSelectType === BatchSelectType.printDelivery) {
      changeTotalCount([]);
      setCheckedList([]);
      setIsCurrentCheckedAll(false);
    }

    setLoading(false);
  };

  const handleChangeListData = (data) => {
    const newList = list.map((item) => {
      if (item.orderNo === data.orderNo) {
        item = data;
      }
      return item;
    });
    setList(newList);
  };

  const handleConfirm = async () => {
    window.Logger &&
      window.Logger.log({
        et: 'click', // 事件类型
        ei: 'batch_delivery_submit', // 事件标识
        en: '批量发货确认提交', // 事件名称
        pt: 'deliveryBatch', // 页面类型
        params: {
          kdtId: window._global?.kdtId || '',
        }, // 事件参数
      });

    setButtonLoading(true);
    try {
      const result = await batchSelectDelivery({
        orderNos: checkedList.map((item) => item.orderNo),
      });
      if (result) {
        clearState();
        changeDrawerVisible(false, true);
        handleShowSatisfactionDialogVisible(true);
      }
    } catch (err) {
      Notify.error('批量发货失败，请刷新重试');
    }
    setButtonLoading(false);
  };

  const handleTriggerConfirmPop = (value: boolean) => {
    if (value && checkedList.length === 0) {
      Notify.error('请先选择订单');
      return;
    }
    setConfirmPopVisible(value);
  };

  const clearState = () => {
    setList([]);
    setCheckedList([]);
    setPageOptions(defaultPageOptions);
    setFilterOptions(defaultFilterOptions);
    setIsCurrentCheckedAll(false);
    setTotalCount(defaultTotalCount);
  };

  const handlePrintDeliveryConfirm = () => {
    if (checkedList.length === 0) {
      Notify.error('请先选择订单');
      return;
    }
    setButtonLoading(true);
    openBatchDeliveryDialog({
      orders: checkedList.map((item) => {
        return {
          orderNo: item.orderNo,
          deliveryNo: item.deliveryNo,
          fulfillNo: item.fulfillNo,
          warehouseId: item.warehouseId || item.kdtId,
          warehouseName: item.warehouseName,
        };
      }),
      onSubmit() {
        changeDrawerVisible(false, true);
        closeBatchDeliveryDialog();
      },
      onClose: () => {
        closeBatchDeliveryDialog();
        setButtonLoading(false);
      },
    });
  };

  useEffect(() => {
    if (batchSelectDrawerVisile) {
      fetchData({ ...defaultPageOptions, ...defaultFilterOptions });
    } else {
      clearState();
    }
  }, [batchSelectDrawerVisile]);

  const renderFooter = () => (
    <div className="batch-select-drawer__footer">
      <Button className="close" onClick={() => changeDrawerVisible(false)}>
        取消
      </Button>
      {batchSelectType === BatchSelectType.printDelivery ? (
        <span className="batch-select-confirm-button-wrap">
          <Button loading={buttonLoading} type="primary" onClick={handlePrintDeliveryConfirm}>
            确定
          </Button>
        </span>
      ) : (
        <Pop
          className="batch-select-drawer__footer-pop"
          trigger="click"
          header="发货确认"
          position="top-right"
          content={
            <div className="batch-select-drawer__footer-pop-content">
              <p>请仔细核对所选订单及发货方式，确定发货后不可修改。</p>
              <ul>
                <li>· 快递订单 {totalCount.express} 个，发货方式：无需物流</li>
                <li>· 同城配送订单 {totalCount.city} 个，发货方式：商家自行配送</li>
                <li>· 上门自提订单 {totalCount.selfFetch} 个，发货方式：无需验证自提码</li>
              </ul>
            </div>
          }
          type="primary"
          confirmText="确认发货"
          cancelText="取消"
          onConfirm={handleConfirm}
          visible={confirmPopVisible}
          onVisibleChange={handleTriggerConfirmPop}
        >
          <Button type="primary" loading={buttonLoading}>
            确定
          </Button>
        </Pop>
      )}
    </div>
  );

  return (
    // @ts-ignore
    <Drawer
      className="batch-select-drawer"
      title={isPrintDelivery ? '批量打单发货' : '批量选择发货'}
      width={1020}
      visible={batchSelectDrawerVisile}
      onClose={() => changeDrawerVisible(false)}
      footer={renderFooter()}
    >
      <div className="batch-select-drawer__container">
        {!isPrintDelivery && (
          <Alert type="warning">
            快递发货订单默认以"无需物流"发货，同城配送订单默认以"商家自行配送"发货，上门自提订单默认以"无需验证自提码"发货
          </Alert>
        )}
        <BatchSelectFilters
          batchSelectType={batchSelectType}
          onChangeFilters={handleChangeFilters}
          filterOptions={filterOptions}
        />
        <BatchSelectOrderList
          list={list}
          loading={loading}
          isCheckedAll={isCurrentCheckedAll}
          checkedList={checkedList}
          pageOptions={pageOptions}
          isPrintDelivery={isPrintDelivery}
          onChangeCheckedAll={handleChangeCheckedAll}
          onChangeChecked={handleChangeChecked}
          onChangePage={handleChangePage}
          onChangeListData={handleChangeListData}
        />
      </div>
    </Drawer>
  );
};

export default BatchSelectDrawer;
