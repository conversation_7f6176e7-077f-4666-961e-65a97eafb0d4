import get from 'lodash/get';
import { IWscPCBaseShopInfo } from '@youzan/wsc-pc-base/definitions/core';
import moment from 'moment';
import formatPrice from '@youzan/utils/money/format';

export const isRetailSingle = () => {
  const { shopType, shopRole } = get(window._global, 'shopInfo', {} as IWscPCBaseShopInfo);
  if (shopType === 7 && shopRole === 0) {
    return true;
  }
  return false;
};

// 零售店铺批量选择商品列表接口参数映射
const expressTypeList = ['express', 'selffetch', 'city'];
export const getRetailBatchOrderListParams = (wscParams) => {
  const {
    startTime,
    endTime,
    goodsTitle,
    order,
    page,
    pageSize,
    expressType,
    warehouseId = '',
    type,
  } = wscParams;
  const sort = order === 'desc' ? 0 : 1;
  const orderType = type ? +type : '';
  const postData = {
    sort,
    expressType: expressTypeList.indexOf(expressType),
    startTime: startTime ? moment(startTime).valueOf() : '',
    endTime: endTime ? moment(endTime).valueOf() : '',
    orderType,
    pageNo: page,
    pageSize,
    goodsTitle,
    warehouseId,
    fromOrderShipment: 1,
    showAllTabFlag: 0,
    displayQuickSearch: 0,
    quickSearch: 96,
  };
  return Object.keys(postData).reduce((obj, key) => {
    const val = postData[key];
    if (val === '' || val === undefined) {
      return obj;
    } else {
      return {
       ...obj,
        [key]: val,
      };
    }
  }, {});
};
// 零售店铺批量选择商品列表接口返回值映射
export const formatRetailBatchOrderListData = (res) => {
  const { data, pageNo, pageSize, total } = res;
  const list = data.map((item) => {
    const { mainOrderInfo, itemInfo, remarkInfo, buyerInfo, paymentInfo, fulfillOrder } = item;
    return {
      ...fulfillOrder,
      orderNo: mainOrderInfo.orderNo,
      bookTime: mainOrderInfo.createTime,
      buyerMsg: remarkInfo.buyerMemoDesc,
      remark: remarkInfo.sellerMemoDesc,
      buyerId: buyerInfo.buyerId,
      customer: buyerInfo.name,
      userName: buyerInfo.nickName,
      stateStr: mainOrderInfo.stateDesc,
      expressType: mainOrderInfo.expressType,
      settlementMoney: formatPrice(paymentInfo.realPay),
      postage: formatPrice(paymentInfo.postage),
      items: itemInfo.map((item) => {
        return {
          ...item,
          num: item.goodsNum,
          price: formatPrice(item.unitPrice),
          imageUrl: item.imgUrl,
        };
      }),
    };
  });
  return {
    list,
    pageSize,
    pageNum: pageNo,
    totalItems: total,
  };
};
