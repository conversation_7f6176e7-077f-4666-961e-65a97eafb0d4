/**
 * 筛选表单筛选项字段定义
 */
export interface IQuery {
  batchStatus?: number;
  operator?: string;
  startDate: string;
  endDate: string;
  page?: number;
  pageSize?: number;
  batchBiz?: number;
  deliveryPointId?: number;
}

/**
 * 获取批量发货记录请求字段定义
 */
export type IGetListRequest = Omit<IQuery, 'startDate' | 'endDate'> & {
  startDate: number | '';
  endDate: number | '';
};

/**
 * 获取批量发货记录返回值
 */
export interface IGetListResponse {
  batchDetails: IBatchDetails[];
  totalCount: number;
}

/**
 * 批量批量发货单条记录字段格式
 */
export interface IBatchDetails {
  batchBiz: string;
  batchNo: string;
  batchStatus: number;
  filePath?: string;
  operateTime: number | Date;
  operator: string;
  remark: string;
  reportStatus: number;
  reportStatusDesc: string;
  successAmount: number;
  totalAmount: number;
  unprocessed: number;
}

/**
 * 获取批量发货进度
 */
export interface IQueryProgressResponse {
  fail: number;
  reportStatus: number;
  success: number;
  timeout: number;
  total: number;
  unprocessed: number;
  batchStatus: number;
}

export interface IBatchDeliveryDetailRequest {
  batchNo: string;
  itemStatus: 0 | 1 | 2;
  pageNum: number;
  pageSize: number;
}

export interface IBatchOrderListRequest {
  expressType?: string;
  goodsTitle?: string;
  startTime?: string;
  endTime?: string;
  sellerRemark?: string;
  buyerMemo?: string;
  star?: number;
  starNum?: number[];
  orderby?: string;
  order?: string;
  type?: string;
  page: number;
  pageSize: number;
}
