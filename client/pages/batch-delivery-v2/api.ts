import ajax from 'zan-pc-ajax';
import retailAjax from '@youzan/retail-ajax';
import { Notify } from 'zent';
import { yzLocation } from '@youzan/url-utils';
import { shopMetaInfo } from '@youzan/utils-shop';
import { IGetListRequest, IGetListResponse, IQueryProgressResponse } from './types';

export const request = (url, options) => {
  return ajax({
    url,
    ...options,
    rawResponse: true,
  })
    .then((resp) => {
      const { data } = resp;
      return data;
    })
    .catch((resp) => {
      const { code, extra = {}, msg } = resp;
      if (code === 10302) {
        Notify.error(msg);
        if (extra.redirectUrl) {
          yzLocation.href = extra.redirectUrl;
        }
      }
      return Promise.reject(msg);
    });
};

export const getList = (queries: IGetListRequest): Promise<IGetListResponse> => {
  const {
    batchStatus = 0,
    operator = '',
    startDate,
    endDate,
    page = 1,
    pageSize = 10,
    batchBiz = null,
    deliveryPointId = null,
  } = queries;
  return request('batch/list.json', {
    method: 'GET',
    data: {
      batchStatus,
      operator,
      startDate,
      endDate,
      page,
      pageSize,
      batchBiz,
      deliveryPointId,
    },
  });
};

export const getUploadToken = (): Promise<string> => {
  return request('batch/token.json', {
    method: 'GET',
  });
};

export const uploadBatch = (
  filePath: string,
  isSplit?: boolean,
  isWscSplit?: boolean
): Promise<string> => {
  const data: {
    filePath: string;
    isSplit?: boolean;
    isWscSplit?: boolean;
  } = { filePath };

  if (isSplit) {
    data.isSplit = isSplit;
  }

  if (isWscSplit) {
    data.isWscSplit = isWscSplit;
  }

  return request('batch/upload.json', {
    method: 'POST',
    // 新接口请使用 contentType: 'application/json'
    contentType: 'application/x-www-form-urlencoded',
    data,
  });
};

export const modifyBatch = (filePath: string, isWscSplit?: boolean): Promise<string> => {
  const data: {
    filePath: string;
    isWscSplit?: boolean;
  } = {
    filePath,
  };
  if (isWscSplit) {
    data.isWscSplit = isWscSplit;
  }
  return request('batch/modify.json', {
    method: 'POST',
    // 新接口请使用 contentType: 'application/json'
    contentType: 'application/x-www-form-urlencoded',
    data,
  });
};

export const queryProgress = (batchNo: string): Promise<IQueryProgressResponse> => {
  return request('batch/progress.json', {
    method: 'GET',
    data: {
      batchNo,
    },
  });
};

// 获取订单设置信息
export const getTradeSettingData = () => {
  return request('/v4/setting/api/team/setting', {
    method: 'GET',
  });
};

// 获取批量选择发货记录
export const getBatchDeliveryDetail = (data) => {
  return request('/v4/trade/delivery/batch/detail.json', {
    method: 'GET',
    data,
  });
};

// 获取批量选择发货订单列表
export const getBatchOrderList = (data) => {
  return request('/v4/trade/delivery/batch/light_order_search.json', {
    method: 'POST',
    contentType: 'application/json',
    data,
  });
};

// 批量选择发货
export const batchSelectDelivery = (data) => {
  return request('/v4/trade/delivery/batch/delivery.json', {
    method: 'POST',
    contentType: 'application/json',
    data,
  });
};

// 获取仓库列表
export const getWarelouseList = () => {
  return retailAjax({
    url: '/youzan.retail.trade.warehouse/1.0.0/query',
    data: {
      pageSize: 100,
    },
  });
};

export const getRetailBatchOrderList = (data) => {
  return retailAjax({
    url: '/youzan.retail.trademanager.fulfillorder.search/1.0.0',
    data,
  });
};

// 获取零售订单类型
export const getRetailOrderTypeList = () => {
  const { shopRole, shopTopic, shopType, saasSolution } = shopMetaInfo;
  return retailAjax({
    url: '/youzan.retail.trade.manager.ordersearch.condition.fetch/1.0.0',
    data: {
      type: 'ordertype',
      shopRole,
      shopTopic,
      shopType,
      saasSolution,
      isFetchOrderList: false,
      isFulfillOrderList: true,
    },
  });
};
