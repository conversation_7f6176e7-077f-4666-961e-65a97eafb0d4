import { setUrlDomain } from '@youzan/retail-utils';
import classNames from 'classnames';
import styles from './styles.m.scss';

export function JavisIcon({
  className,
  style,
}: {
  className?: string;
  style?: React.CSSProperties;
}) {
  return (
    <img
      src={setUrlDomain('/upload_files/2024/03/15/FtZHAquuBeYcglUUo3prNuMsDQJO.png', 'imgqn')}
      className={classNames(styles.icon, className)}
      style={style}
      alt="加我智能图标"
    />
  );
}
