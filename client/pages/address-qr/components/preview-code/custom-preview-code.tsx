import { useEffect, useState } from 'react';
import clsx from 'classnames';
import Rnd from 'react-rnd';

import { ICodeRect } from 'pages/address-qr/models/form';

import styles from './styles.m.scss';
import { InitialBackgroundImage } from 'pages/address-qr/constants';
import { BlockLoading, Icon, Pop } from 'zent';

export function CustomPreviewCode({
  className,
  backgroundImage,
  codeImage,
  codeRect,
  isReadOnly = false,
  isLoading = false,
  onChange,
}: {
  className?: string;
  codeImage: string;
  backgroundImage: string;
  codeRect: ICodeRect;
  isReadOnly?: boolean;
  isLoading?: boolean;
  onChange?: (codeRect: ICodeRect) => void;
}) {
  const [isPopVisible, setIsPopVisible] = useState(false);

  useEffect(() => {
    if (isReadOnly) {
      return;
    }

    const id = window.setTimeout(() => {
      setIsPopVisible(true);
    }, 100);

    return () => {
      window.clearTimeout(id);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      className={clsx(className, styles['custom-preview-code'])}
      style={
        backgroundImage
          ? {
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'contain',
              backgroundPosition: 'center center',
            }
          : {
              backgroundImage: `url(${InitialBackgroundImage})`,
              backgroundRepeat: 'repeat',
            }
      }
      aria-label="寄件码"
    >
      <Rnd
        size={{ width: codeRect.width, height: codeRect.height }}
        style={{
          backgroundColor: '#fff',
        }}
        position={{ x: codeRect.x, y: codeRect.y }}
        bounds="parent"
        lockAspectRatio
        disableDragging={isReadOnly}
        onDragStart={() => setIsPopVisible(false)}
        onDragStop={(_e, d) => {
          onChange?.({
            x: d.x,
            y: d.y,
            width: codeRect.width,
            height: codeRect.height,
          });
        }}
        enableResizing={{
          top: !isReadOnly,
          right: !isReadOnly,
          bottom: !isReadOnly,
          left: !isReadOnly,
          topLeft: !isReadOnly,
          topRight: !isReadOnly,
          bottomLeft: !isReadOnly,
          bottomRight: !isReadOnly,
        }}
        onResizeStop={(_e, _direction, ref, _delta, position) => {
          onChange?.({
            x: position.x,
            y: position.y,
            width: parseInt(ref.style.width, 10),
            height: parseInt(ref.style.height, 10),
          });
        }}
      >
        <Pop
          visible={isPopVisible}
          content={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              支持拖拽及缩放
              <Icon
                type="close"
                style={{ marginLeft: 8, fontSize: 18, cursor: 'pointer' }}
                onClick={() => setIsPopVisible(false)}
              />
            </div>
          }
        >
          <BlockLoading className={styles['custom-code-image-container']} loading={isLoading}>
            {!isLoading ? (
              <img
                className={styles['custom-code-image']}
                src={codeImage}
                alt="寄件码"
                draggable={false}
              />
            ) : (
              <span className={styles['custom-code-image']} />
            )}
          </BlockLoading>
        </Pop>
      </Rnd>
    </div>
  );
}
