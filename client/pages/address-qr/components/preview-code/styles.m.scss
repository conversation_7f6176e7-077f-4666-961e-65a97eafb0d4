.preview-code {
  position: relative;
}

/** 模板预览 */

.default-preview-code {
  width: 100%;
}

.default-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.default {
  &-title {
    color: #fff;
    text-align: center;
    word-break: break-word;
  }

  &-code-image-container {
    position: relative;
    margin: 0 auto;
    background-color: #fff;
  }

  &-code-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &-guide-text {
    text-align: center;
    color: #fff;
    overflow: hidden;
    word-break: break-word;
  }
}

.default-preview-code--110x140 {
  .default-title {
    font-size: 30px;
    line-height: 42px;
    min-height: 42px;
    margin-bottom: 32px;
    padding: 0 26px;
  }

  .default-code-image-container {
    width: 144px;
    padding-top: 144px;
  }

  .default-guide-text {
    margin-top: 32px;
    font-size: 16px;
    line-height: 26px;
    min-height: 26px;
    padding: 0 26px;
  }
}

.default-preview-code--148x210 {
  .default-title {
    font-size: 30px;
    line-height: 42px;
    min-height: 42px;
    margin-bottom: 40px;
    padding: 0 26px;
  }

  .default-code-image-container {
    width: 144px;
    padding-top: 144px;
  }

  .default-guide-text {
    margin-top: 40px;
    font-size: 16px;
    line-height: 26px;
    min-height: 26px;
    padding: 0 26px;
  }
}

.default-preview-code--210x140 {
  .default-title {
    font-size: 18px;
    line-height: 28px;
    min-height: 28px;
    margin-bottom: 6px;
    padding: 0 30px;
  }

  .default-code-image-container {
    width: 100px;
    padding-top: 100px;
  }

  .default-guide-text {
    margin-top: 8px;
    font-size: 14px;
    line-height: 20px;
    min-height: 20px;
    padding: 0 30px;
  }
}

/** 自定义预览 */

.custom-preview-code {
  width: 100%;
  padding-top: 100%; /* 高度 = 宽度 */
}

.custom-code {
  &-image-container[data-zv] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100% !important;
  }

  &-image {
    display: block;
    width: 100%;
    height: 100%;
  }
}
