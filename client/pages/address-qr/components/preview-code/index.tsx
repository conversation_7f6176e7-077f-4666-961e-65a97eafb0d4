import clsx from 'classnames';

import {
  TBackgroundColor,
  BackgroundType,
  TemplateSize,
  ICodeRect,
} from 'pages/address-qr/models/form';

import { TemplatePreviewCode } from './default-preview-code';
import { CustomPreviewCode } from './custom-preview-code';

import styles from './styles.m.scss';
import { AddressQrPreviewContainerWidth } from 'pages/address-qr/constants';

export interface IPreviewCodeProps {
  className?: string;
  isReadOnly?: boolean;
  isLoading?: boolean;

  /** 背景类型 */
  backgroundType: BackgroundType;

  /** 模板尺寸 */
  templateSize: TemplateSize;
  /** 背景颜色 */
  backgroundColor: TBackgroundColor;

  /** 页面标题 */
  pageTitle: string;
  /** 引导文案 */
  guideCopy: string;

  /** 背景图片 */
  backgroundImage: string;
  /** 码图片 */
  codeImage: string;
  /** 码坐标 */
  codeRect: ICodeRect;
  onChange?: (codeRect: ICodeRect) => void;
}

export default function PreviewCode(props: IPreviewCodeProps) {
  let elem;
  switch (props.backgroundType) {
    case BackgroundType.Default:
      elem = <TemplatePreviewCode {...props} />;
      break;
    case BackgroundType.Custom:
      elem = <CustomPreviewCode {...props} />;
      break;
    default:
      elem = null;
      break;
  }
  return (
    <div
      className={clsx(styles['preview-code'], props.className)}
      style={{
        width: AddressQrPreviewContainerWidth,
      }}
    >
      {elem}
    </div>
  );
}
