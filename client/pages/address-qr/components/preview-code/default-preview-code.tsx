import clsx from 'classnames';

import { TBackgroundColor, TemplateSize } from 'pages/address-qr/models/form';

import styles from './styles.m.scss';
import { BlockLoading } from 'zent';

function toPercentStr(ratio: number) {
  return `${ratio * 100}%`;
}

const SizeToHeightRatioBasedOnWidthMap = {
  [TemplateSize.Size110x140]: toPercentStr(1.4 / 1.1),
  [TemplateSize.Size148x210]: toPercentStr(2.1 / 1.48),
  [TemplateSize.Size210x140]: toPercentStr(1.4 / 2.1),
};

export function TemplatePreviewCode({
  templateSize,
  backgroundColor,
  pageTitle,
  guideCopy,
  codeImage,
  isLoading,
}: {
  templateSize: TemplateSize;
  backgroundColor: TBackgroundColor;
  pageTitle: string;
  guideCopy: string;
  codeImage: string;
  isLoading?: boolean;
}) {
  const ratio = SizeToHeightRatioBasedOnWidthMap[templateSize];

  return (
    <div
      className={clsx(
        styles['default-preview-code'],
        styles[`default-preview-code--${templateSize}`]
      )}
      style={{ backgroundColor, paddingTop: ratio }}
    >
      <div className={styles['default-content']}>
        <div className={styles['default-title']}>{pageTitle}</div>
        <BlockLoading className={styles['default-code-image-container']} loading={isLoading}>
          {!isLoading ? (
            <img className={styles['default-code-image']} src={codeImage} alt="寄件码" />
          ) : (
            <span className={styles['default-code-image']} />
          )}
        </BlockLoading>
        <div className={styles['default-guide-text']}>{guideCopy}</div>
      </div>
    </div>
  );
}
