import StaffComplexSelect, {
  IStaffComplexSelectProps,
  IStaffComplexSelectValue,
} from '@youzan/weass-b-pc-components/lib/components/wa-biz/staff-select/staff-complex-select';

import { Form, FormControl, FormDescription, FormError, IValidator, ValidateOption } from 'zent';

export const FormStaffComplexSelect = ({
  name,
  model,
  label,
  validators,
  props,
  style,
  required,
  helpDesc,
}: {
  name?: string;
  model?: any;
  label: string;
  validators?: Array<IValidator<IStaffComplexSelectValue>>;
  props?: Omit<IStaffComplexSelectProps, 'onConfirm' | 'value'>;
  required?: boolean | string;
  helpDesc?: string;
  style?: React.CSSProperties;
}) => {
  if (!name && !model) {
    throw new Error('name 和 model 是必填的');
  }

  const id = Form.useField(
    name || model,
    {
      staff: [],
      department: [],
      staffGroups: [],
    },
    [
      ...(validators || []),
      ...(required
        ? [
            (value) => {
              if (value.department.length === 0 && value.staff.length === 0) {
                return {
                  name: 'FormStaffComplexSelectRequiredError',
                  message: typeof required === 'string' ? required : '请选择承接员工',
                };
              }
              return null;
            },
          ]
        : []),
    ]
  );

  return (
    <FormControl label={label} style={style}>
      <StaffComplexSelect
        {...props}
        value={id.value}
        onConfirm={(value) => {
          id.patchValue(value);
          id.validate(ValidateOption.IncludeUntouched);
        }}
      />
      {helpDesc ? <FormDescription>{helpDesc}</FormDescription> : null}
      {id.error ? <FormError>{id.error?.message}</FormError> : null}
    </FormControl>
  );
};
