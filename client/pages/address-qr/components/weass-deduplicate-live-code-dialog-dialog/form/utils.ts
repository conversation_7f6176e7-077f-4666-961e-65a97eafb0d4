import { setUrlDomain } from '@youzan/retail-utils';

import { getAddressQrWeappPath, WeappId } from 'pages/address-qr/constants/weapp';
import { IDeduplicateCodeCreateUpdateCommand } from 'pages/address-qr/api/models/create-weass-deduplicate-live-code';

import { IWeassDeduplicateLiveCodeDialogFormData } from '../models';

export function makeCreatingWeassDeduplicateLiveCodeData(
  values: IWeassDeduplicateLiveCodeDialogFormData
): IDeduplicateCodeCreateUpdateCommand {
  const appId = WeappId;

  const path = getAddressQrWeappPath();

  /** 本表单动态填写的字段 */
  const dynamicData = {
    appId,
    name: values.name,
    newCustomizePageFurnish: {
      bgImageUrl: '',
      content: values.guideText,
      contentTitle: values.guideTitle,
      customize: false,
      distanceX: 199,
      distanceY: 234,
      pageTitle: '扫码寄件',
    },
    path,
    shiftType: 1 /** 承接员工: 全天在线 */,
    shifts: [
      {
        deptIds: values.shifts.department,
        staffIds: values.shifts.staff,
      },
    ],
    welcomeMsgConfig: {
      msgAttachments: [
        ...(values.welcomeText
          ? [
              {
                type: 1 /** 文本 */,
                text: values.welcomeText,
              },
            ]
          : []),
        {
          customerTags: [],
          miniProgram: {
            appId,
            cover: setUrlDomain(
              '/upload_files/2025/05/06/Fuku2RgTI7xeG_SSZA0RwwYmIm20.png',
              'imgqn'
            ),
            linkOriginTitle: 'packages/retail-shelf/shipping-form/index',
            linkType: 'customWeappLink',
            path,
            title: '扫码寄件',
          },
          type: 4 /** 小程序 */,
        },
      ],
      msgOption: 1,
      timeConfigs: [],
      timeSharing: false,
    },
  };

  return {
    ...dynamicData,
    addLimitState: 0,
    addLimits: [],
    // @ts-ignore
    backupStaff: [],
    contactWayGroupId: 1,
    deduplicateRange: {
      deptIds: [],
      staffIds: [],
    },
    depInfoName: [],
    depName: [],
    deps: [null],
    hasDeduplicateRange: 0,
    oldCustomizePageFurnish: {
      content: '长按识别二维码，联系您的专属客服',
      contentTitle: '您的专属客服随时为你服务',
      pageTitle: '扫码寄件',
    },
    qrCodeAvatarUrl: '',
    remark: '',
    skipVerify: 1,
    staffs: [],
    tagScope: 0 /** 客户标签: 所有客户 */,
    undertakeContent: '长按识别二维码，联系您的专属客服',
    undertakeContentTitle: '您的专属客服随时为你服务',
    undertakeMethod: 2 /** 跳转指定链接 */,
    undertakeStaffName: null,
    undertakeTitle: '扫码寄件',
    undertakeUrl: '',
    wecomTagIds: [],
  };
}
