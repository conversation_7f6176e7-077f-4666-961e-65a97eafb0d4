import React, { useEffect } from 'react';
import {
  Form,
  Radio,
  Button,
  FormStrategy,
  FormInputField,
  FormRadioGroupField,
  FormDescription,
  FormControl,
  FormContext,
  Validators,
} from 'zent';

import { SelectType } from '@youzan/weass-b-pc-components/lib/components/wa-biz/staff-select/staff-complex-select';

import { getAddressQrWeappPath } from 'pages/address-qr/constants/weapp';

import { createWeassDeduplicateLiveCode } from 'pages/address-qr/api';

import { IWeassDeduplicateLiveCodeDialogFormData } from '../models';
import { makeCreatingWeassDeduplicateLiveCodeData } from './utils';
import { FormStaffComplexSelect } from './form.staff-complex-select';

import styles from './styles.m.scss';

export interface IWeassDeduplicateLiveCodeDialogValue {
  liveCodeId: number;
  name?: string;
}

interface IWeassDeduplicateLiveCodeDialogFormProps {
  onNext: (values: IWeassDeduplicateLiveCodeDialogValue) => void;
  onPrev: () => void;
}

const initialValues: IWeassDeduplicateLiveCodeDialogFormData = {
  name: '扫码寄件加粉',
  shiftType: 0,
  shifts: {
    staff: [],
    department: [],
    staffGroups: [],
  },
  welcomeText:
    '你好！为了方便您的寄件服务，请点击下方链接填写收件信息。在收银时，只需向店员报您的手机号即可完成寄件~',
  guideTitle: '长按二维码进行寄件登记',
  guideText: '添加您的专属客服，获取专属寄件链接',
};

export function WeassDeduplicateLiveCodeDialogForm({
  onNext,
  onPrev,
}: IWeassDeduplicateLiveCodeDialogFormProps) {
  const form = Form.useForm(FormStrategy.View);

  const handleSubmit = async () => {
    const data = makeCreatingWeassDeduplicateLiveCodeData(
      form.getValue() as IWeassDeduplicateLiveCodeDialogFormData
    );

    const liveCodeId = await createWeassDeduplicateLiveCode(data);

    onNext({
      name: data.name,
      liveCodeId,
    });
  };

  useEffect(() => {
    form.initialize(initialValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Form form={form} layout="horizontal" onSubmit={handleSubmit}>
      <FormContext.Provider value={{ labelStyle: { flexBasis: 130 } }}>
        <div className={styles.title}>活动细节</div>
        <div className={styles.description}>
          点击快速执行后智能助手将会自动执行以下步骤，如需调整执行细节可修改输入框中的内容
        </div>
        <div className={styles.form}>
          <>
            <FormInputField
              name="name"
              label="活动名称："
              props={{
                placeholder: '最长不超过 20 个字',
                maxLength: 20,
              }}
              required="请输入活动名称"
              validators={[Validators.maxLength(20, '最长不超过 20 个字')]}
            />
          </>

          <>
            <div className={styles['sub-title']}>老客户承接方式</div>
            <FormControl label="小程序录入地址页：">
              <div>{getAddressQrWeappPath({ shouldIgnoreKdtId: true })}</div>
              <FormDescription>
                您已开通微信小程序，已智能链接到小程序承入地址页面。
              </FormDescription>
            </FormControl>
          </>

          <>
            <div className={styles['sub-title']}>新客户承接方式</div>
            <FormRadioGroupField
              name="shiftType"
              className={styles['shift-type-radio-group']}
              label="承接员工："
              required
            >
              <Radio value={0}>全天在线</Radio>
            </FormRadioGroupField>

            <FormStaffComplexSelect
              name="shifts"
              label=""
              props={{
                multiSelect: true,
                selectType: SelectType.StaffDep,
                trigger: 'button',
              }}
              required
              style={{ marginTop: -10 }}
              helpDesc="可添加多名员工承接，客户扫码后随机分配一名员工。若选择的是部门，则部门下有新入职员工，将自动添加为使用员工。"
            />

            <FormInputField
              name="welcomeText"
              label="文字消息："
              props={{
                type: 'textarea',
                autoSize: false,
                width: 400,
                style: {
                  resize: 'none',
                },
                maxLength: 1000,
                placeholder: '请输入',
              }}
              validators={[Validators.maxLength(1000, '最长不超过 1000 个字')]}
            />
            <FormControl label="附件消息：">
              <div>小程序录入地址页：{getAddressQrWeappPath({ shouldIgnoreKdtId: true })}</div>
              <FormDescription>
                您已开通微信小程序，已智能链接到小程序录入地址页面。
              </FormDescription>
            </FormControl>
            <FormInputField
              name="guideTitle"
              label="承接页引导标题："
              required="请输入承接页引导标题"
              validators={[Validators.maxLength(20, '最长不超过 20 个字')]}
              props={{
                width: 400,
                placeholder: '最长不超过 20 个字',
              }}
            />
            <FormInputField
              name="guideText"
              label="承接页引导文案："
              required="请输入承接页引导文案"
              validators={[Validators.maxLength(50, '最长不超过 50 个字')]}
              props={{
                width: 400,
                placeholder: '最长不超过 50 个字',
              }}
            />
          </>
        </div>
        <div className={styles.actions}>
          <Button
            type="primary"
            htmlType="submit"
            loading={form.isSubmitting}
            disabled={form.isSubmitting}
          >
            快速执行
          </Button>
          <Button onClick={onPrev}>上一步</Button>
        </div>
      </FormContext.Provider>
    </Form>
  );
}
