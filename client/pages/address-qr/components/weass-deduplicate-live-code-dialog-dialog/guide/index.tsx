import { But<PERSON>, <PERSON>, Tag } from 'zent';

import {
  HasWecomStaff,
  IsWecomAccountAccessForWeapp,
  IsWecomValid,
} from 'pages/address-qr/constants/weass';

import styles from './styles.m.scss';

export function WeassDeduplicateLiveCodeDialogGuide({ onNext }: { onNext: () => void }) {
  const validateList = [
    () =>
      IsWecomValid
        ? { isValid: true }
        : {
            isValid: false,
            error: (
              <>
                本功能由企业微信助手提供支持，
                {/* eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name */}
                <Link href="https://qiwei.youzan.com/wa/channel/auth">点击订购</Link>
              </>
            ),
          },
    () =>
      IsWecomAccountAccessForWeapp
        ? { isValid: true }
        : {
            isValid: false,
            error: (
              <>
                <p>当前企微客户未与商城客户打通，需要打通后才可获取短链。</p>
                <p>
                  打通后将可识别客户在商场的订单、资产等信息。
                  <a
                    // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
                    href={'https://help.youzan.com/displaylist/detail_26_26-2-77011'}
                    target="_blank"
                    rel="noreferrer"
                  >
                    查看打通说明
                  </a>
                </p>
              </>
            ),
          },
    () =>
      HasWecomStaff
        ? { isValid: true }
        : { isValid: false, error: '当前账号因企业微信助手权限不足，无法执行此操作' },
  ];

  let error = null;
  for (let i = 0; i < validateList.length; i++) {
    const validator = validateList[i];
    const validationResult = validator();
    if (!validationResult.isValid) {
      error = validationResult.error;
      break;
    }
  }

  return (
    <div className={styles.content}>
      <div
        className={styles['image-section']}
        aria-label="扫码寄件 —— 扫码填写收件信息，收银时向店员报手机号完成寄件"
      />

      <div className={styles['right-section']}>
        <div className={styles.tag}>
          <Tag size="large" theme="grey">
            私域留存方案
          </Tag>
        </div>
        <div className={styles.title}>扫码即锁客，开启寄件留存新玩法</div>
        <div className={styles.desc}>
          当顾客进店被您家优质的商品吸引，有邮寄需求时，这是门店拓展私域流量的好时机。顾客扫寄件码后先加企微，实现强制锁客，加好友后通过欢迎语链接填邮寄地址。这既满足顾客需求，又能转化私域会员。您可以借此积累精准客源，并提供会员专享服务，实现顾客留存与业务增长的双赢。
        </div>
        <div className={styles.footer}>
          <Button type="primary" disabled={Boolean(error)} onClick={onNext}>
            开始设置
          </Button>
          {error ? <div className={styles.tip}>{error}</div> : null}
        </div>
      </div>
    </div>
  );
}
