import { useState } from 'react';
import { closeDialog, Dialog } from 'zent';
import { WeassDeduplicateLiveCodeDialogGuide } from './guide';
import { IWeassDeduplicateLiveCodeDialogValue, WeassDeduplicateLiveCodeDialogForm } from './form';
import styles from './styles.m.scss';

const { openDialog } = Dialog;

function WeassDeduplicateLiveCodeDialogContent({
  onSuccess,
}: {
  onSuccess: (value: IWeassDeduplicateLiveCodeDialogValue) => void;
}) {
  const [guide, setGuide] = useState<'guide' | 'form'>('guide');

  switch (guide) {
    case 'guide':
      return <WeassDeduplicateLiveCodeDialogGuide onNext={() => setGuide('form')} />;
    case 'form':
      return (
        <WeassDeduplicateLiveCodeDialogForm onNext={onSuccess} onPrev={() => setGuide('guide')} />
      );
  }
}

export function openWeassDeduplicateLiveCodeDialog() {
  const dialogId = `weass-deduplicate-live-code-dialog-${Date.now()}`;

  return new Promise((resolve) => {
    openDialog({
      dialogId,
      className: styles.dialog,
      title: null,
      children: (
        <WeassDeduplicateLiveCodeDialogContent
          onSuccess={(data) => {
            resolve(data);
            closeDialog(dialogId);
          }}
        />
      ),
      footer: null,
    });
  });
}
