import React from 'react';

import { PromotionDialog } from '@youzan/react-components';

import {
  IsWeappBound,
  IsWeappValid,
  WeappBindUrl,
  WeappOrderUrl,
} from 'pages/address-qr/constants/weapp';
import { IAddressQrConfig } from 'pages/address-qr/api/models/address-qr';
import { fetchCodeImage } from 'pages/address-qr/utils';
import { JumpType } from 'pages/address-qr/models/form';

import styles from './styles.m.scss';
import PreviewCode from '../preview-code';
import { AddressQrPreviewContainerWidth } from 'pages/address-qr/constants';

const { openPromotionDialog } = PromotionDialog;

const openDownload = ({ data, codePath }: { data: IAddressQrConfig; codePath: string }) => {
  openPromotionDialog({
    className: styles.download,
    title: '下载寄件码',
    weappUrl: codePath,
    mode: 'poster',
    containerWidth: AddressQrPreviewContainerWidth,
    createPoster: ({ codeImg, activity: detail }) => {
      return <PreviewCode {...detail} codeImage={codeImg} isReadOnly />;
    },
    getActivity: () => Promise.resolve(data),
    getWeappCode: async () => {
      return fetchCodeImage({
        jumpType: data.jumpType as unknown as JumpType,
        liveCodeId: data.liveCodeId,
        id: data.id,
      });
    },
    weappConfig: {
      hasBoundApp: IsWeappBound,
      hasOrderedApp: IsWeappValid,
      appOrderUrl: WeappOrderUrl,
      appBindUrl: WeappBindUrl,
    },
    supportH5: false,
    supportWeapp: true,
    customRenderOperationTip: {
      weapp: () => null,
    },
    supportSetting: false,
    getDownloadFileName: () => data.configName,
  });
};

export default openDownload;
