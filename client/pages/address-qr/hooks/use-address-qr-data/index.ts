import { useState, useEffect } from 'react';

import { getAddressQrDetail } from 'pages/address-qr/api';
import { formatInputData } from 'pages/address-qr/pages/form/utils/format';
import { InitialFormData } from 'pages/address-qr/constants';
import {
  IAddressQrForm,
  IAddressQrFormData,
  IWeassDeduplicateLiveCode,
  JumpType,
} from 'pages/address-qr/models/form';
import { fetchCodeImage } from 'pages/address-qr/utils';
import { useAddressQrFormPageContext } from 'pages/address-qr/pages/form/context/form-page';

function useAddressQrData({ form }: { form: IAddressQrForm }) {
  const { id, isEdit } = useAddressQrFormPageContext();
  const [isLoading, setIsLoading] = useState(false);
  const [oldData, setOldData] = useState({} as IAddressQrFormData);

  const [codeImage, setCodeImage] = useState('');
  const [isCodeImageLoading, setIsCodeImageLoading] = useState(false);

  const changeCodeImageBasedOnDeps = async ({
    jumpType,
    weassDeduplicateLiveCode,
  }: Partial<{
    jumpType: JumpType;
    weassDeduplicateLiveCode: IWeassDeduplicateLiveCode;
  }>) => {
    setIsCodeImageLoading(true);

    const newJumpType = jumpType ?? form.getValue().jumpType;
    const newLiveCodeId =
      weassDeduplicateLiveCode?.liveCodeId ?? form.getValue().weassDeduplicateLiveCode?.liveCodeId;

    const image = await fetchCodeImage({
      jumpType: newJumpType,
      liveCodeId: newLiveCodeId,
    }).finally(() => {
      setIsCodeImageLoading(false);
    });

    const currentJumpType = form.getValue().jumpType as JumpType;
    if (currentJumpType === newJumpType) {
      setCodeImage(image);
    }
  };

  const initData = async () => {
    let data;
    if (isEdit) {
      form.initialize(InitialFormData);

      setIsLoading(true);
      try {
        const result = await getAddressQrDetail({ id });

        const formData = formatInputData(result);

        setOldData(formData);

        data = formData;
      } finally {
        setIsLoading(false);
      }
    } else {
      data = InitialFormData;
    }

    form.initialize(data);

    await changeCodeImageBasedOnDeps(data);
  };

  useEffect(() => {
    initData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { isLoading, oldData, codeImage, changeCodeImageBasedOnDeps, isCodeImageLoading };
}

export default useAddressQrData;
