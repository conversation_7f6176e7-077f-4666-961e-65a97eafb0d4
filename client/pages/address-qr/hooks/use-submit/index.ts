import { useCallback, useState } from 'react';
import { Notify } from 'zent';

import history from 'pages/address-qr/history';

import { createOrUpdateAddressQr } from 'pages/address-qr/api';

import { formatOutputData } from 'pages/address-qr/pages/form/utils/format';
import { AddressQrPagePath } from 'pages/address-qr/constants';
import { IAddressQrForm, IAddressQrFormData } from 'pages/address-qr/models/form';

function useSubmit({
  id,
  isEdit,
}: {
  id: number;
  isEdit: boolean;
}): [boolean, (data: any) => void] {
  const [loading, setLoading] = useState(false);

  const onSubmit = useCallback(
    async (form: IAddressQrForm) => {
      try {
        setLoading(true);
        const data = formatOutputData(form.getValue() as unknown as IAddressQrFormData);

        if (isEdit) {
          data.id = id;
        }

        await createOrUpdateAddressQr(data);

        Notify.success('保存成功');

        history.push({
          pathname: AddressQrPagePath.List,
        });
      } finally {
        setLoading(false);
      }
    },
    [id, isEdit]
  );

  return [loading, onSubmit];
}

export default useSubmit;
