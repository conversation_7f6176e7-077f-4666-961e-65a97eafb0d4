import compareVersions from '@youzan/utils/string/compareVersions';

const { url, shopInfo } = window._global || {};

const { www } = url || {};

/**
 * 获取根 KDT_ID
 */
export const getRootKdtId = () => {
  const { rootKdtId, kdtId } = shopInfo;
  return rootKdtId ?? kdtId;
};

/**
 * 小程序是否有效
 */
export const IsWeappValid = window._global?.weappStatusInfo?.isValid ?? false;

/**
 * 小程序是否绑定
 */
export const IsWeappBound = Boolean(window._global?.weappStatusInfo?.bindStatus);

/**
 * 扫码寄件要求小程序最小版本
 */
export const RequiredMinimumWeappVersionForAddressQr = '3.162.4';

/**
 * 小程序版本是否有效
 */
export const IsWeappVersionValid = window._global?.weappVersion
  ? compareVersions(RequiredMinimumWeappVersionForAddressQr, window._global.weappVersion) === -1
  : false;

/**
 * 获取小程序路径
 */
export const getAddressQrWeappPath = ({
  id,
  shouldIgnoreKdtId,
}: {
  id?: number;
  shouldIgnoreKdtId?: boolean;
} = {}) => {
  const paramsStr = [shouldIgnoreKdtId ? '' : `kdt_id=${getRootKdtId()}`, id ? `id=${id}` : '']
    .filter(Boolean)
    .join('&');
  return `packages/retail-shelf/shipping-form/index${paramsStr ? `?${paramsStr}` : ''}`;
};

/**
 * 小程序绑定入口
 */
export const WeappBindUrl = `${www}/showcase/weapp/dashboard/index`;

/**
 * 小程序订购入口
 */
export const WeappOrderUrl = `${www}/appmarket/appdesc?id=1407#/feature`;

/**
 * 小程序ID
 */
export const WeappId = window._global?.weappId;
