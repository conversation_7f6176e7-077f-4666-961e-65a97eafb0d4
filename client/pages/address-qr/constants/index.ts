import { setUrlDomain } from '@youzan/retail-utils';

import {
  IAddressQrFormData,
  JumpType,
  BackgroundType,
  TemplateSize,
  BackgroundColors,
} from '../models/form';

export const TemplateSizes: Array<{ value: TemplateSize; label: string }> = [
  { value: TemplateSize.Size110x140, label: '110mm×140mm' },
  { value: TemplateSize.Size148x210, label: '148mm×210mm' },
  { value: TemplateSize.Size210x140, label: '210mm×140mm' },
];

export const BackgroundTypes: Array<{ value: BackgroundType; label: string }> = [
  { value: BackgroundType.Default, label: '默认模板' },
  { value: BackgroundType.Custom, label: '自定义背景' },
];

export const AddressQrPagePath = {
  List: '/v4/trade/address-qr',
  Create: '/v4/trade/address-qr/create',
  Edit: '/v4/trade/address-qr/edit',
  View: '/v4/trade/address-qr/view',
};

export const InitialBackgroundImage = setUrlDomain(
  '/upload_files/2025/04/08/FjCgYW8hNEFqxkWSOXMlPiUW4uWi.png',
  'imgqn'
);

/**
 * 自定义模式二维码预览容器大小(正方形)
 */
export const AddressQrPreviewContainerWidth = 300;

export const getInitialCodeRect = (containerSize: { width: number; height: number }) => {
  const width = containerSize.width / 3;
  const height = containerSize.height / 3;
  return {
    x: containerSize ? containerSize.width / 2 - width / 2 : 0,
    y: containerSize ? containerSize.height / 2 - height / 2 : 0,
    width,
    height,
  };
};

export const InitialFormData: IAddressQrFormData = {
  configName: '',
  jumpType: JumpType.WeassDeduplicateLiveCode,
  weassDeduplicateLiveCode: null,
  backgroundType: BackgroundType.Default,
  templateSize: TemplateSize.Size110x140,
  backgroundColor: BackgroundColors[0],
  pageTitle: '微信扫码寄件',
  guideCopy: '扫码填写收件信息,收银时向店员报手机号完成寄件',
  backgroundImage: '',
  codeRect: getInitialCodeRect({
    width: AddressQrPreviewContainerWidth,
    height: AddressQrPreviewContainerWidth,
  }),
};
