/**
 * com.youzan.wecom.helper.api.operate.contactway.dto.response.OnlineTimeIntervalDTO
 * 时段详情
 */
interface IOnlineTimeIntervalDTO {
  /** 开始时间 HH:mm:ss */
  startTime?: string;
  /** 结束时间 HH:mm:ss */
  endTime?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.contactway.dto.request.ShiftDTO
 * 排班
 */
interface IShiftDTO {
  /** 每周工作日列表，1代表周一，2代表周二，...，7代表周日  例如：周一，周三，周日工作时，传入 [1, 3, 7] */
  workDays?: number[];
  /** 此排班内的员工 id 列表  要求：1、员工必须认证  2、最大数量限制 100 */
  staffIds?: number[];
  /** 例如：08:00-19:00，表示早八点到晚七点  当 shiftType 为全天在线时，此列表为空 */
  onlineTimeIntervals?: IOnlineTimeIntervalDTO[];
  /** 此排班内的部门id列表 */
  deptIds?: number[];
}

/**
   * com.youzan.wecom.helper.api.common.dto.Operator 
   * 操作人对象，记录操作记录时用到
   Created by shibin on 2020/11/1.
   */
interface IOperator {
  /** 有赞内部员工CAS登录系统的帐号ID，如客满 */
  yzCasId?: number;
  /** 有赞帐号域的UserID（如果有了staffId，可不传，某些B端场景无法拿到staffId则传yzUserId） */
  yzUserId?: number;
  /** 调用方的应用名称 */
  appName?: string;
  /** 客户端ip地址 */
  clientIp?: string;
  /** 手机号 */
  mobile?: number;
  /** 昵称 */
  nickname?: string;
  /** 请求来源 参考 com.youzan.wecom.helper.api.common.enums.OperatorAppSourceEnum */
  appSource?: string;
  /** 企微助手域的操作员工ID */
  staffId?: number;
}

/**
 * com.youzan.wecom.helper.api.operate.contactway.common.PageFurnishConfigDTO
 */
interface IPageFurnishConfigDTO {
  /** 页面标题 */
  pageTitle?: string;
  /** 自定义二维码，背景图Url */
  bgImageUrl?: string;
  /** 内容标题 */
  contentTitle?: string;
  /** 自定义二维码，锚点纵轴边距(单位:px,默认0px) */
  distanceY?: number;
  /** 自定义二维码，锚点横轴边距(单位:px,默认0px) */
  distanceX?: number;
  /** 是否自定义,默认false-非自定义 */
  customize?: boolean;
  /** 内容文案 */
  content?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.welcomemsg.dto.common.WelcomeMsgContentConfigDTO
 */
interface IWelcomeMsgContentConfigDTO {
  /** 小程序消息标题 */
  miniprogramTitle?: string;
  /** 欢迎语2，媒体类型  0:无媒体消息  1：图片  2：链接  3：小程序 */
  mediaType?: number;
  /** 自定义链接图片URL */
  linkImageUrl?: string;
  /** 小程序封面URL */
  miniprogramImageUrl?: string;
  /** 自定义链接描述信息 */
  linkDesc?: string;
  /** 小程序AppID */
  miniprogramAppId?: string;
  /** 自定义链接标题 */
  linkTitle?: string;
  /** 视频素材地址 */
  videoUrl?: string;
  /** 图片URL */
  imageUrl?: string;
  /** 链接URL */
  linkUrl?: string;
  /** 文件素材地址 */
  fileUrl?: string;
  /** 欢迎语1，客户昵称占位符：%NICKNAME% （为客户昵称占位标识，大小写敏感） */
  text: string;
  /** 小程序页面路径 */
  miniprogramPage?: string;
  /** 统一消息附件类型，支持多附件后必传  1:文本  2：图片  3：链接  4：小程序  5：视频  6：文件 */
  msgAttachmentType?: number;
  /** 欢迎语附件名称 */
  mediaName?: string;
  /** 是否自定义 */
  customLink?: boolean;
  /** 图片mediaId（url和mediaId优先mediaId） */
  imageMediaId?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.welcomemsg.dto.common.TimeWelcomeMsgContentConfigDTO.TimeAllocation
 * 详细时间配置
 */
interface ITimeAllocation {
  /** 开始时间 HH:mm:ss */
  startTime?: string;
  /** 结束时间 HH:mm:ss */
  endTime?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.welcomemsg.dto.common.TimeWelcomeMsgContentConfigDTO
 */
interface ITimeWelcomeMsgContentConfigDTO {
  /** 小程序消息标题 */
  miniprogramTitle?: string;
  /** 欢迎语2，媒体类型  0:无媒体消息  1：图片  2：链接  3：小程序 */
  mediaType?: number;
  /** 自定义链接图片URL */
  linkImageUrl?: string;
  /** 小程序封面URL */
  miniprogramImageUrl?: string;
  /** 自定义链接描述信息 */
  linkDesc?: string;
  /** 小程序AppID */
  miniprogramAppId?: string;
  /** 自定义链接标题 */
  linkTitle?: string;
  /** 视频素材地址 */
  videoUrl?: string;
  /** 普通欢迎语配置--支持多附件  文本和附件分开传，多附件数量不能超过9个，所以欢迎语配置最多不超过10个 */
  contents?: IWelcomeMsgContentConfigDTO[];
  /** 星期几 1 星期一 2 星期二 ... */
  weekDay?: number[];
  /** 图片URL */
  imageUrl?: string;
  /** 链接URL */
  linkUrl?: string;
  /** 文件素材地址 */
  fileUrl?: string;
  /** 详细时间配置 */
  time?: ITimeAllocation[];
  /** 欢迎语1，客户昵称占位符：%NICKNAME% （为客户昵称占位标识，大小写敏感） */
  text?: string;
  /** 小程序页面路径 */
  miniprogramPage?: string;
  /** 统一消息附件类型，支持多附件后必传  1:文本  2：图片  3：链接  4：小程序  5：视频  6：文件 */
  msgAttachmentType?: number;
  /** 欢迎语附件名称 */
  mediaName?: string;
  /** 是否自定义 */
  customLink?: boolean;
  /** 图片mediaId（url和mediaId优先mediaId） */
  imageMediaId?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.response.corpmoment.ImageDTO
 */
interface IImageDTO {
  /** 多附件三期下掉 */
  imgUrl?: string;
  /** 图片名称 */
  name?: string;
  /** 图片的素材id */
  mediaId?: string;
  url?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.CouponDTO
 * 优惠券类型
 */
interface ICouponDTO {
  /** 封面图 */
  cover?: string;
  /** 小程序地址 */
  path?: string;
  /** 链接类型 */
  urlType?: number;
  /** 关联商城的kdtId */
  mallKdtId?: number;
  /** 链接原始标题 */
  linkOriginTitle?: string;
  /** 小程序ID */
  appId?: string;
  /** 优惠券别名 */
  couponAlias?: string;
  /** 链接后的带参集合 */
  urlParams?: Record<string, string>;
  /** 描述 */
  digest?: string;
  /** 优惠券id */
  couponId?: number;
  /** 优惠券标题 */
  title?: string;
  /** 优惠券url */
  url?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.MiniProgramDTO
 * 小程序信息
 */
interface IMiniProgramDTO {
  /** 封面图片 */
  cover: string;
  /** 封面图片的mediaId */
  coverMediaId?: string;
  /** 小程序路径 */
  path: string;
  /** 链接原始标题 */
  linkOriginTitle?: string;
  /** 小程序 appId */
  appId?: string;
  /** 链接后的带参集合 */
  urlParams?: Record<string, string>;
  /** 链接类型 */
  linkType?: string;
  /** 小程序标题 */
  title: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.LinkDTO
 */
interface ILinkDTO {
  /** 链接封面 */
  cover?: string;
  /** 链接封面老的，废弃字段，请使用 cover  todo 三期下掉 */
  image?: string;
  /** 链接原始标题 */
  linkOriginTitle?: string;
  /** 链接 */
  link: string;
  /** 链接摘要 */
  digest?: string;
  /** 链接后的带参集合 */
  urlParams?: Record<string, string>;
  /** 链接类型 */
  linkType?: string;
  /** 链接类型  1. 小程序  2. h5 */
  type?: number;
  /** 链接标题 */
  title?: string;
  /** 小程序封面 */
  mediaId?: string;
  /** 客户标签 */
  customerTags?: string[];
  /** 是否自定义  <p>  无意义字段，后续会下掉 */
  customLink?: boolean;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.GoodsDTO
 * 商品类型
 */
interface IGoodsDTO {
  /** 封面图 */
  cover?: string;
  /** 小程序地址 */
  path?: string;
  /** 链接类型 */
  urlType?: number;
  /** 关联商城的kdtId */
  mallKdtId?: number;
  /** 商品别名 */
  goodsAlias?: string;
  /** 链接原始标题 */
  linkOriginTitle?: string;
  /** 商品id */
  goodsId?: number;
  /** 小程序ID */
  appId?: string;
  /** 链接后的带参集合 */
  urlParams?: Record<string, string>;
  /** 描述 */
  digest?: string;
  /** 商品标题 */
  title?: string;
  /** 商品url */
  url?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.VideoDTO
 */
interface IVideoDTO {
  /** 视频素材封面mediaId */
  thumbMediaId?: string;
  /** 视频素材名称 */
  name?: string;
  /** 视频素材id */
  mediaId?: string;
  /** 视频封面图片地址 */
  thumbUrl?: string;
  /** 视频素材地址 */
  url?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.PointDTO
 * 积分类型
 */
interface IPointDTO {
  /** 积分 */
  amount?: number;
  /** 关联商城的kdtId */
  mallKdtId?: number;
}

/**
 * com.youzan.wecom.helper.api.fansgrow.request.AddStaffDTO
 * AddStaffDTO
 */
interface IAddStaffDTO {
  /** 员工id */
  staffIds?: number[];
  /** 个人活码标识 */
  contactWayAlias?: string;
  /** 链接 */
  link: string;
  /** 部门id */
  deptIds?: number[];
  /** 链接标题 */
  title?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.FileDTO
 */
interface IFileDTO {
  /** 文件素材名称 */
  name?: string;
  /** 文件素材id */
  mediaId?: string;
  /** 文件素材地址 */
  url?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.MicroPageDTO
 * 微页面
 */
interface IMicroPageDTO {
  /** 店铺id */
  mallKdtId?: number;
  /** wePageId */
  wePageId?: number;
  /** alias */
  alias?: string;
  /** 链接标题 */
  title?: string;
  /** 链接 */
  url: string;
}

/**
 * com.youzan.wecom.helper.api.operate.mission.dto.request.CardDTO
 * 权益卡类型
 */
interface ICardDTO {
  /** 关联商城的kdtId */
  mallKdtId?: number;
  /** 权益卡id */
  cardId?: number;
  /** 链接后的带参集合 */
  urlParams?: Record<string, string>;
}

/**
 * com.youzan.wecom.helper.api.common.msgattachment.dto.MsgAttachmentDTO
 * 消息附件对象
 */
interface IMsgAttachmentDTO {
  /** 图片URL */
  image?: IImageDTO;
  /** 优惠券 */
  coupon?: ICouponDTO;
  /** 小程序 */
  miniProgram?: IMiniProgramDTO;
  /** 链接 */
  link?: ILinkDTO;
  /** 商品 */
  goods?: IGoodsDTO;
  /** 视频 */
  video?: IVideoDTO;
  /** 消息附件类型  1：文本  2：图片  3：链接  4：小程序  5：视频  6：文件  7：商品  8：优惠券  9：积分  10：权益卡  11：企微好友  12: 微页面 */
  type: number;
  /** 积分 */
  point?: IPointDTO;
  /** 加企微好友 */
  addStaff?: IAddStaffDTO;
  /** 文件 */
  file?: IFileDTO;
  /** 微页面 */
  wePage?: IMicroPageDTO;
  /** 素材表【msg_attachment】对应唯一ID，创建时非必填  MsgAttachmentId */
  id?: number;
  /** 文本  <p>  支持动态客户昵称，占位符：%NICKNAME%  如：你好，%NICKNAME%！  会在发送的时候转换成客户昵称，以张三为客户为例，实际发送：  你好，张三!  FBI WARING!!!表情包的问题：  企业微信客户端的显示逻辑和微信不一致，会导致[礼物]无法在企微客户端转换成表情，但是微信侧是可以正常显示表情🎁 */
  text?: string;
  /** 客户标签 */
  customerTags?: string[];
  /** 权益卡 */
  card?: ICardDTO;
  /** 排序号 */
  order?: number;
}

/**
 * com.youzan.wecom.helper.api.operate.welcomemsg.dto.common.TimeWelcomeMsgConfigCommand.TimeAllocation
 * 详细时间配置
 */
interface ITimeAllocation {
  /** 开始时间 HH:mm:ss */
  startTime?: string;
  /** 结束时间 HH:mm:ss */
  endTime?: string;
}

/**
 * com.youzan.wecom.helper.api.operate.welcomemsg.dto.common.TimeWelcomeMsgConfigCommand
 */
interface ITimeWelcomeMsgConfigCommand {
  /** 小程序消息标题 */
  miniprogramTitle?: string;
  /** 欢迎语2，媒体类型  0:无媒体消息  1：图片  2：链接  3：小程序 */
  mediaType?: number;
  /** 自定义链接图片URL */
  linkImageUrl?: string;
  /** 小程序封面URL */
  miniprogramImageUrl?: string;
  /** 自定义链接描述信息 */
  linkDesc?: string;
  /** 多附件欢迎语配置  文本和附件分开传，多附件数量不能超过9个，所以欢迎语配置最多不超过10个 */
  msgAttachments?: IMsgAttachmentDTO[];
  /** 小程序AppID */
  miniprogramAppId?: string;
  /** 自定义链接标题 */
  linkTitle?: string;
  /** 视频素材地址 */
  videoUrl?: string;
  /** 星期几 1 星期一 2 星期二 ... */
  weekDay?: number[];
  /** 图片URL */
  imageUrl?: string;
  /** 链接URL */
  linkUrl?: string;
  /** 文件素材地址 */
  fileUrl?: string;
  /** 详细时间配置 */
  time?: ITimeAllocation[];
  /** 欢迎语1，客户昵称占位符：%NICKNAME% （为客户昵称占位标识，大小写敏感） */
  text?: string;
  /** 小程序页面路径 */
  miniprogramPage?: string;
  /** 统一消息附件类型，支持多附件后必传  1:文本  2：图片  3：链接  4：小程序  5：视频  6：文件 */
  msgAttachmentType?: number;
  /** 欢迎语附件名称 */
  mediaName?: string;
  /** 是否自定义 */
  customLink?: boolean;
}

/**
 * com.youzan.wecom.helper.api.operate.contactway.dto.request.WelcomeMsgConfigDTO
 */
interface IWelcomeMsgConfigDTO {
  /** 分时配置列表 */
  timeSharingConfigs?: ITimeWelcomeMsgContentConfigDTO[];
  /** 分时配置列表-新欢迎语模型 */
  timeConfigs?: ITimeWelcomeMsgConfigCommand[];
  /** 是否开启分时 */
  timeSharing?: boolean;
  /** 欢迎语类型  0：不发送欢迎语，1：渠道欢迎语，2：默认欢迎语 */
  msgOption: number;
  /** 兼容老接口 */
  content?: IWelcomeMsgContentConfigDTO;
  /** 普通欢迎语配置--支持多附件  文本和附件分开传，多附件数量不能超过9个，所以欢迎语配置最多不超过10个 */
  msgAttachments?: IMsgAttachmentDTO[];
}

/**
 * com.youzan.wecom.helper.api.operate.contactway.dto.request.AddLimitDTO
 */
interface IAddLimitDTO {
  /** 部门id */
  deptId?: number;
  /** 单日此渠道的加人上限 */
  addLimit?: number;
  /** 员工 id */
  staffId?: number;
}

/**
 * com.youzan.wecom.helper.api.operate.contactway.dto.request.DeduplicateRangeDTO
 */
interface IDeduplicateRangeDTO {
  /** 去重范围内的员工id */
  staffIds?: number[];
  /** 去重范围内的部门id */
  deptIds?: number[];
}

/**
 * com.youzan.wecom.helper.api.operate.deduplicateLiveCode.dto.DeduplicateCodeCreateUpdateCommand
 * 创建/更新指令
 */
export interface IDeduplicateCodeCreateUpdateCommand {
  yzKdtId: number;
  /** 老客承接跳转链接 */
  undertakeUrl?: string;
  /** 新客排班表  后续建议使用 shifts */
  shift?: IShiftDTO;
  /** 创建去重活码：普通二维码 */
  generalLiveCode?: boolean;
  /** 老客承接引导标题  后续使用 PageFurnishConfigDTO */
  undertakeContentTitle?: string;
  /** 客户备注 */
  remark?: string;
  /** 老客承接页面标题  后续使用 PageFurnishConfigDTO */
  undertakeTitle?: string;
  /** 操作人信息 */
  operator?: IOperator;
  /** 添加人数上限状态：0-没有上限。1-有上限 */
  addLimitState?: number;
  /** 老客承接员工展示名称 */
  undertakeStaffName?: string;
  /** 小程序路径 */
  path?: string;
  /** 标签范围，0：全部，1：仅新客 */
  tagScope?: number;
  /** 新客承页面接配置 */
  newCustomizePageFurnish?: IPageFurnishConfigDTO;
  /** 客户标签 id 列表  最长 50 个标签 */
  wecomTagIds?: string[];
  /** 小程序 appId */
  appId?: string;
  /** 老客承接页面配置 */
  oldCustomizePageFurnish?: IPageFurnishConfigDTO;
  /** 排班列表  做多20个排班 */
  shifts?: IShiftDTO[];
  /** 新老客承接区分（0-区分、1-不区分） */
  undertakeDistinguish?: number;
  /** 新客加好友欢迎语 */
  welcomeMsgConfig?: IWelcomeMsgConfigDTO;
  /** 老客承接方式 1:显示已添加成员二维码 2：跳转指定链接 */
  undertakeMethod?: number;
  /** 排班类型，1：全天在线，2：自动上下线 */
  shiftType?: number;
  /** 员工单日此渠道的加人上限 */
  addLimits?: IAddLimitDTO[];
  /** 自动通过好友验证，0：关闭，1：开启 */
  skipVerify?: number;
  /** 二维码头像 url */
  qrCodeAvatarUrl?: string;
  /** 老客承接引导文案  后续使用 PageFurnishConfigDTO */
  undertakeContent?: string;
  /** 所属分组ID */
  contactWayGroupId?: number;
  /** 创建去重活码：小程序 */
  miniProgramLiveCode?: boolean;
  /** 是否有去重范围 0或null 否 1 是 */
  hasDeduplicateRange?: number;
  /** 去重活码名称 */
  name: string;
  /** 去重活码ID */
  liveCodeId?: number;
  /** 去重范围 */
  deduplicateRange?: IDeduplicateRangeDTO;
  contactWayType?: number;
  /** 备用员工 id */
  backupStaffId?: number;
}

// IDeduplicateCodeCreateUpdateCommand
