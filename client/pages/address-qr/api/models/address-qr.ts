/**
 * com.youzan.retail.trade.misc.api.dto.request.shopsetting.saveOrUpdateAddressConfig
 */
export interface ISaveOrUpdateAddressQrConfigRequest {
  /** 寄件码名称 */
  configName?: string;
  /** 跳转方式编码 DIRECT ADD_ENTERPRISE_QW */
  jumpType?: string;
  /** 背景颜色（十六进制代码，如#FFFFFF） */
  backgroundColor?: string;
  /** 引导文案 */
  guideCopy?: string;
  /** 店铺ID */
  kdtId?: number;
  /** 页面标题 */
  pageTitle?: string;
  /** 模板宽度 */
  templateSize?: string;
  createdAt?: string;
  /** 操作人ID */
  adminId?: number;
  liveCodeId?: number;
  /** 主键ID（空或<=0为新增，>0为修改） */
  id?: number;
  /** 背景设置类型编码 */
  backgroundType?: string;
  /** 背景图片 */
  backgroundImage?: string;
  /** 自定义码 */
  codeRect?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  updatedAt?: string;
}

// IExpressCodeConfigRequest

/**
 * 请求对象
 */
export interface IAddressQrConfig {
  /**
   * 主键ID（空或<=0为新增，>0为修改）
   */
  id: number;

  /**
   * 寄件码名称
   */
  configName?: string;

  /**
   * 跳转方式编码
   * @see JumpTypeEnum
   */
  jumpType?: JumpTypeEnum;

  /**
   * 背景设置类型编码
   * @see BackgroundTypeEnum
   */
  backgroundType?: string;

  /**
   * 去重活码
   */
  liveCodeId?: number;

  /**
   * 模板大小
   */
  templateSize?: string;

  /**
   * 背景颜色（十六进制代码，如#FFFFFF）
   */
  backgroundColor?: string;

  /**
   * 页面标题
   */
  pageTitle?: string;

  /**
   * 引导文案
   */
  guideCopy?: string;

  /** 背景图片 */
  backgroundImage?: string;

  /** 自定义码 */
  codeRect?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };

  /**
   * 店铺ID
   */
  kdtId?: number;

  /**
   * 操作人ID
   */
  adminId?: number;

  /**
   * 操作人名称
   */
  adminName?: string;

  /**
   * 创建时间
   */
  createdAt?: number;

  /**
   * 企微活码
   */
  weassDeduplicateLiveCode?: {
    name: string;
    liveCodeId: number;
  };

  /**
   * 是否可以编辑
   */
  canEdit?: boolean;
}

// 跳转方式枚举（服务端使用）
export enum JumpTypeEnum {
  DIRECT = 'DIRECT', // 直达地址录入
  ADD_ENTERPRISE_QW = 'ADD_ENTERPRISE_QW', // 先添加企微
}

// 背景设置类型枚举（服务端使用）
export enum BackgroundTypeEnum {
  DEFAULT = 'DEFAULT', // 默认模板
  CUSTOM = 'CUSTOM', // 自定义模板
}
