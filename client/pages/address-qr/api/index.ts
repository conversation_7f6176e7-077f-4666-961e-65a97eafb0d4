import { Notify } from 'zent';
import ajax from 'zan-pc-ajax';

import { IListParam } from 'pages/address-qr/models';

import { IDeduplicateCodeCreateUpdateCommand } from './models/create-weass-deduplicate-live-code';
import { IDeduplicateCodeDTO } from './models/get-weass-deduplicate-live-code-detail';
import { IDeduplicateCodeUrlDto } from './models/get-weass-deduplicate-live-code-promotion-url';
import { IAddressQrConfig, ISaveOrUpdateAddressQrConfigRequest } from './models/address-qr';

const prefix = '/v4/trade/address-qr/api';

/** 获取活动列表 */
export function getAddressQrList(
  data: IListParam & {
    configName?: string;
  }
): Promise<{
  items: IAddressQrConfig[];
  total: number;
  pageSize: number;
  pageNo: number;
}> {
  data.pageSize = data.pageSize || 10;
  data.pageNo = data.pageNo || 1;
  return ajax({
    url: `${prefix}/get-address-qr-list`,
    method: 'GET',
    data,
  }).catch((err) => {
    Notify.error(err.message || err.msg || err);
    throw err;
  });
}

// 获取活动详情
export function getAddressQrDetail(data = {}): Promise<IAddressQrConfig> {
  return ajax({
    url: `${prefix}/get-address-qr-detail`,
    method: 'get',
    data,
  }).catch((err) => {
    Notify.error(err.message || err.msg || err);
    throw err;
  });
}

// 创建/编辑活动
export function createOrUpdateAddressQr(
  data: ISaveOrUpdateAddressQrConfigRequest
): Promise<number> {
  return ajax({
    url: `${prefix}/create-or-update-address-qr`,
    contentType: 'application/json',
    method: 'post',
    data,
  }).catch((err) => {
    Notify.error(err.message || err.msg || err);
    throw err;
  });
}

// 删除活动
export function deleteAddressQr(data: { id: number }): Promise<boolean> {
  return ajax({
    url: `${prefix}/delete-address-qr`,
    contentType: 'application/json',
    method: 'post',
    data,
  }).catch((err) => {
    Notify.error(err.message || err.msg || err);
    throw err;
  });
}

export const createWeassDeduplicateLiveCode = async (
  data: IDeduplicateCodeCreateUpdateCommand
): Promise<number> => {
  return ajax({
    url: `${prefix}/create-deduplicate-live-code`,
    method: 'POST',
    contentType: 'application/json',
    data,
  }).catch((err) => {
    Notify.error(err.message || err.msg || err);
    throw err;
  });
};

export const invalidWeassDeduplicateLiveCode = async (data: {
  liveCodeId: number;
}): Promise<boolean> => {
  return ajax({
    url: `${prefix}/invalid-deduplicate-live-code`,
    method: 'POST',
    contentType: 'application/json',
    data,
  }).catch((err) => {
    Notify.error(err.message || err.msg || err);
    throw err;
  });
};

export const getWeassDeduplicateLiveCodeDetail = async (
  data: {
    liveCodeId: number;
  },
  {
    shouldNotify = true,
  }: {
    shouldNotify?: boolean;
  } = {}
): Promise<IDeduplicateCodeDTO> => {
  return ajax({
    url: `${prefix}/get-deduplicate-live-code-detail`,
    method: 'GET',
    data,
  }).catch((err) => {
    if (shouldNotify) {
      Notify.error(err.message || err.msg || err);
    }
    throw err;
  });
};

export const getWeassDeduplicateLiveCodePromotionUrl = async (
  data: {
    liveCodeId: number;
  },
  {
    shouldNotify = true,
  }: {
    shouldNotify?: boolean;
  } = {}
): Promise<IDeduplicateCodeUrlDto> => {
  return ajax({
    url: `${prefix}/get-deduplicate-live-code-promotion-url`,
    method: 'GET',
    data,
  }).catch((err) => {
    if (shouldNotify) {
      Notify.error(err.message || err.msg || err);
    }
    throw err;
  });
};
