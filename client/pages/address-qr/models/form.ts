import { $FieldSetBuilderChildren, UnknownFieldSetBuilderChildren } from 'zent/es/form/formulr';
import { ZentForm } from 'zent/es/form/ZentForm';

import { BackgroundTypeEnum, JumpTypeEnum } from '../api/models/address-qr';

export enum JumpType {
  /** 扫码直达地址录入页 */
  Direct = JumpTypeEnum.DIRECT,
  /** 扫码先添加企微，再跳转地址录入页 */
  WeassDeduplicateLiveCode = JumpTypeEnum.ADD_ENTERPRISE_QW,
}

export enum BackgroundType {
  /** 默认模板 */
  Default = BackgroundTypeEnum.DEFAULT,
  /** 自定义背景 */
  Custom = BackgroundTypeEnum.CUSTOM,
}

export enum TemplateSize {
  /** 110mm×140mm */
  Size110x140 = '110x140',
  /** 148mm×210mm */
  Size148x210 = '148x210',
  /** 210mm×140mm */
  Size210x140 = '210x140',
}

export interface ICodeRect {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const BackgroundColors = [
  '#63b359',
  '#2c9f67',
  '#509fc9',
  '#5885cf',
  '#9062c0',
  '#d09a45',
  '#e4b138',
  '#ee903c',
  '#f08500',
  '#a9d92d',
  '#dd6549',
  '#cc463d',
  '#cf3e36',
  '#5e6671',
  '#dcac6c',
] as const;

export type TBackgroundColor = (typeof BackgroundColors)[number];

export interface IWeassDeduplicateLiveCode {
  name: string;
  liveCodeId: number;
}

export interface IAddressQrFormData {
  /** 寄件码名称 */
  configName: string;
  /** 跳转链接 */
  jumpType: JumpType;

  /**
   * 企微去重活码
   */
  weassDeduplicateLiveCode: IWeassDeduplicateLiveCode | null;

  /**
   * 模板
   */

  /** 背景类型 */
  backgroundType: BackgroundType;
  /** 模板尺寸 */
  templateSize: TemplateSize;
  /** 背景颜色 */
  backgroundColor: TBackgroundColor;
  /** 页面标题 */
  pageTitle: string;
  /** 引导文案 */
  guideCopy: string;

  /**
   * 自定义
   */

  /** 背景图片 */
  backgroundImage: string;
  /** 二维码坐标 */
  codeRect: ICodeRect;
}

export type IAddressQrForm = ZentForm<$FieldSetBuilderChildren<UnknownFieldSetBuilderChildren>>;
