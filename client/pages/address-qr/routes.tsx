import React from 'react';
import { IndexRoute, Route } from 'react-router';
import { ErrorPage } from '@youzan/react-components';

import NavApp from './nav-app';
import List from './pages/list';
import Form from './pages/form';

const { NotFoundPage } = ErrorPage;

const Edit = (props) => <Form {...props} isEdit />;

export default (
  <Route path="/v4/trade/address-qr" component={NavApp}>
    <IndexRoute component={List} />
    <Route path="create" component={Form} />
    <Route path="edit" component={Edit} />
    <Route path="*" component={NotFoundPage} />
  </Route>
);
