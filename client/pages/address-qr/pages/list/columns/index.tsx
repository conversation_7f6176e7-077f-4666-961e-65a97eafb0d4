import { IGridColumn } from 'zent';

import { IAddressQrConfig } from 'pages/address-qr/api/models/address-qr';

import { createRenderingOperators } from './operators';

export const getColumns = ({ onRefresh }: { onRefresh: () => void }) => {
  const columns: Array<IGridColumn & { show?: boolean }> = [
    {
      title: '寄件码名称',
      width: 200,
      name: 'configName',
    },
    {
      title: '创建时间',
      name: 'createdAt',
      width: 190,
    },
    {
      title: '创建人',
      width: 200,
      name: 'adminName',
    },
    {
      title: '操作',
      name: 'operator',
      fixed: 'right',
      textAlign: 'right',
      bodyRender: (item: IAddressQrConfig) => {
        return createRenderingOperators({ onRefresh })(item);
      },
    },
  ];

  return columns.filter((item) => {
    if (item.show == null) return true;
    return item.show;
  });
};
