import './index.m.scss';

import React from 'react';

import { Operations } from '@youzan/react-components';

import { checkCanEditAddressQr } from 'pages/address-qr/constants/perms';
import { IAddressQrConfig } from 'pages/address-qr/api/models/address-qr';

import { EditBtn, DelBtn, DownloadBtn } from './items';

export const createRenderingOperators =
  ({ onRefresh }: { onRefresh: () => void }) =>
  (item: IAddressQrConfig) => {
    const canEdit = checkCanEditAddressQr(item);

    const operators = [
      {
        name: 'edit',
        show: canEdit,
        component: <EditBtn data={item} />,
      },
      {
        name: 'share',
        show: true,
        component: <DownloadBtn data={item} />,
      },
      {
        name: 'delete',
        show: canEdit,
        component: <DelBtn data={item} onDeleted={onRefresh} />,
      },
    ];

    return (
      <Operations
        items={operators
          .filter((item) => item.show || typeof item.show === 'undefined')
          .map((item) => item.component)}
      />
    );
  };
