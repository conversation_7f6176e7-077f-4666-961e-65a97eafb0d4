import React, { useState } from 'react';
import { Button, Notify, Pop } from 'zent';

import { IAddressQrConfig } from 'pages/address-qr/api/models/address-qr';
import { deleteAddressQr } from 'pages/address-qr/api';
import { AddressQrPagePath } from 'pages/address-qr/constants';
import openDownload from 'pages/address-qr/components/download';
import history from 'pages/address-qr/history';
import { JumpType } from 'pages/address-qr/models/form';
import { fetchCodePath } from 'pages/address-qr/utils';
import { confirmInvalidWeassDeduplicateLiveCode } from 'pages/address-qr/utils/weass-depulicate-live-code';

export const DownloadBtn = ({ data = {} as IAddressQrConfig }) => {
  const [isLoading, setIsLoading] = useState(false);
  const handleClick = async () => {
    setIsLoading(true);
    const codePath = await fetchCodePath({
      id: data.id,
      jumpType: data.jumpType as unknown as JumpType,
      liveCodeId: data.liveCodeId,
    }).finally(() => {
      setIsLoading(false);
    });

    openDownload({
      data,
      codePath,
    });
  };
  return (
    <Button type="text" disabled={isLoading} loading={isLoading} onClick={handleClick}>
      下载寄件码
    </Button>
  );
};

export const EditBtn = ({ data }) => {
  return (
    <Button
      type="text"
      onClick={() => {
        history.push(`${AddressQrPagePath.Edit}?id=${data.id}`);
      }}
    >
      编辑
    </Button>
  );
};

export const DelBtn = ({ data, onDeleted }) => {
  const { id } = data;
  const clickFunc = () => {
    return deleteAddressQr({ id }).then(() => {
      Notify.success('删除成功');
      onDeleted({ current: 1 });

      if (data.liveCodeId) {
        confirmInvalidWeassDeduplicateLiveCode(data.liveCodeId);
      }
    });
  };

  return (
    <Pop
      trigger="click"
      position="top-right"
      content={<>删除后将不可恢复，确定删除？</>}
      onConfirm={clickFunc}
    >
      <Button type="text">删除</Button>
    </Pop>
  );
};
