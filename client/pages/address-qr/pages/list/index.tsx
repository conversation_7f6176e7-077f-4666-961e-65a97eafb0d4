import React from 'react';
import { <PERSON><PERSON>, Grid } from 'zent';
import { useFetchTable } from '@youzan/react-hooks';

import { getAddressQrList } from 'pages/address-qr/api';
import history from 'pages/address-qr/history';
import { AddressQrPagePath } from 'pages/address-qr/constants';
import { useAddressQrAppContext } from 'pages/address-qr/app-context';
import { IAddressQrConfig } from 'pages/address-qr/api/models/address-qr';
import { CanOperateAddressQr } from 'pages/address-qr/constants/perms';

import Filters from './components/filters';

import { getColumns } from './columns';

import styles from './styles.m.scss';

const PageSize = 10;

const List = () => {
  const {
    configName: defaultConfigName,
    pageNo: defaultPageNo,
    setConfigName,
    setPageNo,
  } = useAddressQrAppContext();

  const defaultFilters = {
    configName: defaultConfigName,
  };

  const { items, tableProps, filterProps, reload } = useFetchTable(getAddressQrList, {
    current: defaultPageNo,
    handleParams: (params: { pageNo: number; configName: string }) => {
      setPageNo(params.pageNo);
      setConfigName(params.configName);
      return params;
    },
    transformRes(resp: {
      items: IAddressQrConfig[];
      total: number;
      pageNo: number;
      pageSize: number;
    }) {
      return {
        paginator: {
          totalCount: resp.total,
          page: resp.pageNo,
          pageSize: resp.pageSize,
        },
        items: resp.items,
      };
    },
    defaultFilters,
    pageSize: PageSize,
    pageSizeOptions: null,
  });

  return (
    <>
      <header className={styles.header}>扫码寄件</header>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: 16,
        }}
      >
        {CanOperateAddressQr ? (
          <Button
            type="primary"
            onClick={() => {
              history.push(AddressQrPagePath.Create);
            }}
          >
            新建寄件码
          </Button>
        ) : null}
        <Filters
          defaultFilters={defaultFilters}
          onFilter={(filters) => {
            filterProps.onFilter(filters);
          }}
        />
      </div>
      <Grid
        {...tableProps}
        rowKey="id"
        datasets={items}
        scroll={{ x: 1000 }}
        columns={getColumns({ onRefresh: reload })}
        pageInfo={{ ...tableProps.pageInfo }}
      />
    </>
  );
};

export default List;
