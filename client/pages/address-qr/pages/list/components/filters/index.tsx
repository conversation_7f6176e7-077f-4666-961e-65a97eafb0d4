import React from 'react';
import { Input } from 'zent';

interface IFilterData {
  configName?: string;
}

export const defaultFilters: IFilterData = {
  configName: '',
};

export default function Filters({
  defaultFilters,
  onFilter,
}: {
  defaultFilters: IFilterData;
  onFilter: (data: IFilterData) => void;
}) {
  return (
    <Input
      defaultValue={defaultFilters.configName}
      icon="search"
      placeholder="搜索寄件码名称"
      onPressEnter={(e) => {
        onFilter({ configName: (e.target as HTMLInputElement).value });
      }}
    />
  );
}
