import './styles.m.scss';

import React, { useMemo } from 'react';
import { WithRouterProps } from 'react-router';
import { BlockLoading, Button, Form, FormStrategy } from 'zent';

import { AddressQrPagePath } from 'pages/address-qr/constants';
import AppActions from 'pages/address-qr/components/app-actions';
import {
  AddressQrFormPageContextProvider,
  IAddressQrFormPageProvider,
  useAddressQrFormPageContext,
} from 'pages/address-qr/pages/form/context/form-page';
import history from 'pages/address-qr/history';
import useAddressQrData from 'pages/address-qr/hooks/use-address-qr-data';
import useSubmit from 'pages/address-qr/hooks/use-submit';

import { AddressQrFormFields } from './fields';

function AddressQrForm() {
  const form = Form.useForm(FormStrategy.View);

  const { id, isEdit } = useAddressQrFormPageContext();

  const { isLoading, codeImage, changeCodeImageBasedOnDeps, isCodeImageLoading } = useAddressQrData(
    { form }
  );
  const [isSubmiting, onSubmit] = useSubmit({ id, isEdit });

  return (
    <Form form={form} layout="horizontal" onSubmit={onSubmit}>
      <BlockLoading loading={isLoading}>
        <AddressQrFormFields
          form={form}
          codeImage={codeImage}
          isLoading={isLoading}
          onCodeImageChangeBasedOnDeps={changeCodeImageBasedOnDeps}
          isCodeImageLoading={isCodeImageLoading}
        />
      </BlockLoading>

      <AppActions>
        <Button
          type="primary"
          htmlType="submit"
          loading={isSubmiting || isLoading}
          disabled={isSubmiting || isLoading}
        >
          保存
        </Button>
        <Button onClick={() => history.push(AddressQrPagePath.List)}>取消</Button>
      </AppActions>
    </Form>
  );
}

function AddressQrFormWrapper(props: WithRouterProps & { isEdit?: boolean }) {
  const { location, isEdit } = props;
  const { id } = location.query as { [key: string]: string };

  const provider: IAddressQrFormPageProvider = useMemo(
    () => ({ id: Number(id), isEdit }),
    [id, isEdit]
  );

  return (
    <AddressQrFormPageContextProvider {...provider}>
      <AddressQrForm />
    </AddressQrFormPageContextProvider>
  );
}

export default AddressQrFormWrapper;
