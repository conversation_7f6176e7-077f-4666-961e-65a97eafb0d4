import { FormDescription, Link } from 'zent';

import {
  getAddressQrWeappPath,
  IsWeappBound,
  IsWeappValid,
  RequiredMinimumWeappVersionForAddressQr,
  WeappBindUrl,
  WeappOrderUrl,
} from 'pages/address-qr/constants/weapp';

import styles from './styles.m.scss';

export function WeappFormDescription() {
  return (
    <FormDescription>
      {IsWeappValid ? (
        IsWeappBound ? (
          <>
            已开通微信小程序，已智能链接到小程序录入地址页：
            {getAddressQrWeappPath({ shouldIgnoreKdtId: true })}
            <span style={{ color: '#ed6a18' }}>
              （小程序需升级至 {RequiredMinimumWeappVersionForAddressQr} 版本后生效）
            </span>
          </>
        ) : (
          <>
            请先绑定微信小程序，
            <Link className={styles['weapp-invalid-link']} href={WeappBindUrl} target="_blank">
              点击绑定
            </Link>
          </>
        )
      ) : (
        <>
          请先开通微信小程序，
          <Link className={styles['weapp-invalid-link']} href={WeappOrderUrl} target="_blank">
            点击开通
          </Link>
        </>
      )}
    </FormDescription>
  );
}
