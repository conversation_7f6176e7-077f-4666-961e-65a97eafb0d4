import { FormInputField, FormRadioGroupField, IValidator, Radio, Validators } from 'zent';

import { TemplateSizes } from 'pages/address-qr/constants';

import { FormColorField } from './form-color-field';
import { BackgroundType } from 'pages/address-qr/models/form';
import { createWhenInBackgroundTypeValidator } from '../../utils';

export function TemplateFormFields() {
  return (
    <>
      <FormRadioGroupField name="templateSize" label="模板尺寸：">
        {TemplateSizes.map(({ value, label }) => (
          <Radio key={value} value={value}>
            {label}
          </Radio>
        ))}
      </FormRadioGroupField>

      <FormColorField name="backgroundColor" label="背景颜色：" />

      <FormInputField
        name="pageTitle"
        label="页面标题："
        props={{
          placeholder: '请输入',
          width: 240,
          maxLength: 20,
        }}
        required
        validators={[
          Validators.required('页面标题不能为空'),
          Validators.maxLength(20, '页面标题最多可输入 20 个字'),
        ].map((validator) =>
          createWhenInBackgroundTypeValidator<IValidator<string>>(BackgroundType.Default, validator)
        )}
      />

      <FormInputField
        name="guideCopy"
        label="引导文案："
        props={{
          type: 'textarea',
          width: 240,
          placeholder: '请输入',
          showCount: true,
          autoSize: true,
          maxLength: 30,
        }}
        required
        validators={[
          Validators.required('引导文案不能为空'),
          Validators.maxLength(30, '引导文案最多可输入 30 个字'),
        ].map((validator) =>
          createWhenInBackgroundTypeValidator<IValidator<string>>(BackgroundType.Default, validator)
        )}
      />
    </>
  );
}
