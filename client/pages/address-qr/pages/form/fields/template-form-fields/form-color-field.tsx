import { useRef } from 'react';
import { Form, FormControl, FormError, IValidators, useFormChild } from 'zent';

import clsx from 'classnames';

import { BackgroundColors } from 'pages/address-qr/models/form';

import styles from './styles.m.scss';

export function FormColorField({
  name,
  model,
  defaultValue = '',
  validators = [],
  label,
  required,
  withoutLabel,
  style,
  className,
}: {
  name?: string;
  model?: string;
  defaultValue?: string;
  validators?: IValidators<string>;
  label?: string;
  required?: boolean;
  withoutLabel?: boolean;
  style?: React.CSSProperties;
  className?: string;
}) {
  if (name && model) {
    throw new Error('Cannot use name and model together.');
  }

  const id = Form.useField(name || model, defaultValue, [...validators]);

  const anchorRef = useRef();
  useFormChild(id, anchorRef);

  return (
    <FormControl
      style={style}
      className={className}
      label={label}
      required={required}
      withoutLabel={withoutLabel}
    >
      <div className={styles['color-container']}>
        {BackgroundColors.map((value) => (
          <div
            key={value}
            className={clsx(styles['color-block'], {
              [styles['color-block--selected']]: id.value === value,
            })}
          >
            <div
              className={styles['color-block-inner']}
              style={{ backgroundColor: value }}
              onClick={() => {
                id.patchValue(value);
              }}
            />
          </div>
        ))}
      </div>
      <FormError>{id.error?.message}</FormError>
    </FormControl>
  );
}
