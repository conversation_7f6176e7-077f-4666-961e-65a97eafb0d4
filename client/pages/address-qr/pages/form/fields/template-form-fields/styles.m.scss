.color-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  flex-wrap: wrap;
}

.color-block {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  padding: 4px;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  cursor: pointer;

  &-inner {
    width: 100%;
    height: 100%;
  }

  &--selected {
    border-color: #07d;
  }
}
