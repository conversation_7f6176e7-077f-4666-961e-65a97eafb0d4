.container {
  margin-top: 10px;
}

.tag {
  display: inline-block;
  padding: 8px 12px;
  max-width: 250px;
  background: #f7f7f7;
  border-radius: 2px;
  box-sizing: border-box;
}

.tag-title {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 20px;
  color: #999;
  padding-bottom: 8px;
  margin-bottom: 8px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #e0e0e0;
  }
}

.tag-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-name {
  flex-grow: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  line-height: 20px;
  color: #333;
}

.tag-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;

  :global {
    .zent-btn {
      padding: 0;
      font-size: 14px;
    }
  }
}

.button {
  background-color: initial !important;
}

.icon {
  margin-right: 4px;
}
