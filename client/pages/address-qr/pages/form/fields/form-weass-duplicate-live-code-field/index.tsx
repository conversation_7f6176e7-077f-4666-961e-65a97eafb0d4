import { Button, Form, FormControl, FormError, IValidators, Pop, ValidateOption } from 'zent';

import { IWeassDeduplicateLiveCode } from 'pages/address-qr/models/form';

import { openWeassDeduplicateLiveCodeDialog } from 'pages/address-qr/components/weass-deduplicate-live-code-dialog-dialog';
import { JavisIcon } from 'pages/address-qr/components/javis-icon';

import styles from './styles.m.scss';
import { trackClick } from 'pages/address-qr/utils/track';

export function FormWeassDuplicateLiveCodeField({
  name,
  model,
  style,
  validators,
  onSuccess,
}: {
  name?: string;
  model?: string;
  style?: React.CSSProperties;
  validators?: IValidators<IWeassDeduplicateLiveCode>;
  onSuccess?: (data: IWeassDeduplicateLiveCode) => void;
}) {
  if (name && model) {
    throw new Error('Cannot use name and model together.');
  }

  const id = Form.useField<IWeassDeduplicateLiveCode | null>(name || model, null, [...validators]);

  return (
    <FormControl style={style} label="">
      {id.value ? (
        <div className={styles.tag}>
          <div className={styles['tag-title']}>
            <JavisIcon className={styles.icon} />
            智能生成扫码寄件私域留存方案
          </div>
          <div className={styles['tag-content']}>
            <div className={styles['tag-name']} title={id.value.name}>
              {id.value.name}
            </div>
            <div className={styles['tag-actions']}>
              <Button
                type="text"
                className={styles.button}
                style={{ marginLeft: 8 }}
                onClick={() => {
                  window.open(
                    // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
                    `https://qiwei.youzan.com/wa/management#/operation/live-qrcode/distinct/edit/${id.value.liveCodeId}`
                  );
                  trackClick({ name: 'edit' });
                }}
              >
                编辑
              </Button>
              <Pop trigger="click" content={<>确定删除？</>} onConfirm={() => id.patchValue(null)}>
                <Button
                  type="text"
                  className={styles.button}
                  style={{ marginLeft: 12 }}
                  onClick={() => {
                    trackClick({ name: 'del' });
                  }}
                >
                  删除
                </Button>
              </Pop>
            </div>
          </div>
        </div>
      ) : (
        <Button
          type="text"
          size="large"
          onClick={() => {
            openWeassDeduplicateLiveCodeDialog().then((data: IWeassDeduplicateLiveCode) => {
              id.patchValue(data);
              id.validate(ValidateOption.IncludeUntouched);
              onSuccess?.(data);
            });
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <JavisIcon className={styles.icon} />
            点击生成扫码寄件私域留存方案
          </div>
        </Button>
      )}
      <FormError>{id.error?.message}</FormError>
    </FormControl>
  );
}
