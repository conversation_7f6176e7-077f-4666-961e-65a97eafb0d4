import {
  FormDescription,
  FormInputField,
  FormRadioGroupField,
  IMaybeError,
  IValidator,
  LayoutCol,
  LayoutRow,
  Radio,
  ValidatorMiddlewares,
  Validators,
} from 'zent';
import { useFieldValue } from 'zent/es/form/formulr';

import { BackgroundTypes, AddressQrPreviewContainerWidth } from 'pages/address-qr/constants';
import { IsWeappBound, IsWeappValid, IsWeappVersionValid } from 'pages/address-qr/constants/weapp';
import {
  BackgroundType,
  IAddressQrForm,
  IAddressQrFormData,
  IWeassDeduplicateLiveCode,
  JumpType,
} from 'pages/address-qr/models/form';

import { TemplateFormFields } from './template-form-fields';
import { CustomFormFields } from './custom-form-fields';
import { FormPreviewCode } from './form-preview-code';

import { FormWeassDuplicateLiveCodeField } from './form-weass-duplicate-live-code-field';
import { WeappFormDescription } from './weapp-form-description';

import styles from './styles.m.scss';

export function AddressQrFormFields({
  form,
  codeImage,
  onCodeImageChangeBasedOnDeps,
  isCodeImageLoading,
  isLoading,
}: {
  form: IAddressQrForm;
  codeImage: string;
  onCodeImageChangeBasedOnDeps: ({
    jumpType,
    weassDeduplicateLiveCode,
  }: Partial<{
    jumpType: JumpType;
    weassDeduplicateLiveCode: IWeassDeduplicateLiveCode;
  }>) => void;
  isCodeImageLoading: boolean;
  isLoading: boolean;
}) {
  const jumpType = useFieldValue(form.model.get('jumpType'));
  const backgroundType = useFieldValue(form.model.get('backgroundType'));
  const templateSize = useFieldValue(form.model.get('templateSize'));
  const backgroundColor = useFieldValue(form.model.get('backgroundColor'));
  const pageTitle = useFieldValue(form.model.get('pageTitle'));
  const guideCopy = useFieldValue(form.model.get('guideCopy'));
  const backgroundImage = useFieldValue(form.model.get('backgroundImage'));

  return (
    <>
      <section className={styles.section}>
        <header>基础信息</header>

        <FormInputField
          name="configName"
          label="寄件码名称："
          required="请输入寄件码名称"
          props={{
            width: 240,
            placeholder: '请输入',
            maxLength: 50,
          }}
          validators={[Validators.maxLength(50, '寄件码名称最多可输入 50 个字')]}
        />

        <FormRadioGroupField
          name="jumpType"
          label="跳转链接："
          required
          validators={[
            () => {
              if (!IsWeappValid) {
                return {
                  name: 'jumpTypeInvalidWeapp',
                  message: '请先开通微信小程序',
                } as IMaybeError<string>;
              }

              if (!IsWeappBound) {
                return {
                  name: 'jumpTypeInvalidWeapp',
                  message: '请先绑定微信小程序',
                } as IMaybeError<string>;
              }

              if (!IsWeappVersionValid) {
                return {
                  name: 'jumpTypeInvalidWeapp',
                  message: '请先升级微信小程序',
                } as IMaybeError<string>;
              }
              return null;
            },
            Validators.required('请选择跳转链接'),
          ]}
          onChange={async (value) => {
            onCodeImageChangeBasedOnDeps({
              jumpType: value,
            });
          }}
        >
          <div>
            <Radio value={JumpType.Direct}>
              扫码直达地址录入页
              {jumpType === JumpType.Direct ? <WeappFormDescription /> : null}
            </Radio>
          </div>
          <div style={{ marginTop: 16 }}>
            <Radio value={JumpType.WeassDeduplicateLiveCode}>
              扫码先添加企微，再跳转地址录入页
              {jumpType === JumpType.WeassDeduplicateLiveCode ? (
                <FormDescription>需订购有赞企微助手</FormDescription>
              ) : null}
            </Radio>
          </div>
        </FormRadioGroupField>

        <FormWeassDuplicateLiveCodeField
          name="weassDeduplicateLiveCode"
          style={{
            display: jumpType === JumpType.WeassDeduplicateLiveCode ? '' : 'none',
            marginTop: -12,
          }}
          validators={[
            ValidatorMiddlewares.when(
              (ctx) =>
                (ctx.getFormValue() as IAddressQrFormData)?.jumpType ===
                JumpType.WeassDeduplicateLiveCode
            )(
              Validators.required('请生成扫码寄件私域留存方案')
            ) as IValidator<IWeassDeduplicateLiveCode>,
          ]}
          onSuccess={async (data) => {
            onCodeImageChangeBasedOnDeps({
              weassDeduplicateLiveCode: data,
            });
          }}
        />
      </section>

      <section className={styles.section} style={{ minHeight: 700 }}>
        <header>模板样式</header>

        <LayoutRow>
          <LayoutCol
            span={6}
            style={{
              width: AddressQrPreviewContainerWidth,
              flexShrink: 0,
              visibility: isLoading ? 'hidden' : 'visible',
            }}
          >
            <FormPreviewCode
              name="codeRect"
              props={{
                backgroundType,
                templateSize,
                backgroundColor,
                pageTitle,
                guideCopy,
                backgroundImage,
                codeImage,
                isLoading: isCodeImageLoading,
              }}
            />
          </LayoutCol>
          <LayoutCol span={18}>
            <FormRadioGroupField
              name="backgroundType"
              label="背景设置："
              required
              validators={[Validators.required('请选择背景设置')]}
            >
              {BackgroundTypes.map(({ value, label }) => (
                <Radio key={value} value={value}>
                  {label}
                </Radio>
              ))}
            </FormRadioGroupField>
            <div
              style={{ display: backgroundType === BackgroundType.Default ? 'initial' : 'none' }}
            >
              <TemplateFormFields />
            </div>
            <div style={{ display: backgroundType === BackgroundType.Custom ? 'initial' : 'none' }}>
              <CustomFormFields />
            </div>
          </LayoutCol>
        </LayoutRow>
      </section>
    </>
  );
}
