import { Form } from 'zent';

import PreviewCode, { IPreviewCodeProps } from 'pages/address-qr/components/preview-code';

export function FormPreviewCode({
  name,
  model,
  props,
}: {
  name: string;
  model?: string;
  props: Omit<IPreviewCodeProps, 'codeRect' | 'onChange'>;
}) {
  if (name && model) {
    throw new Error('Cannot use name and model together.');
  }

  const id = Form.useField(name || model, {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  return (
    <PreviewCode
      {...props}
      codeRect={id.value}
      onChange={(codeRect) => {
        id.patchValue(codeRect);
      }}
    />
  );
}
