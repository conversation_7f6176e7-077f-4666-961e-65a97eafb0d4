import { useCallback, useRef } from 'react';
import { Form, FormControl, FormDescription, FormError, IValidators, useFormChild } from 'zent';

import ImageUpload from 'components/image-upload';

import styles from './styles.m.scss';

export function FormImageUploadField({
  name,
  model,
  defaultValue = '',
  validators = [],
  label,
  required,
  withoutLabel,
  style,
  className,
}: {
  name?: string;
  model?: string;
  defaultValue?: string;
  validators?: IValidators<string>;
  label?: string;
  required?: boolean;
  withoutLabel?: boolean;
  style?: React.CSSProperties;
  className?: string;
}) {
  if (name && model) {
    throw new Error('Cannot use name and model together.');
  }

  const id = Form.useField(name || model, defaultValue, [...validators]);

  const anchorRef = useRef();
  useFormChild(id, anchorRef);

  const onSuccess = useCallback(
    // eslint-disable-next-line camelcase
    (data: Array<{ attachment_url: string }>) => {
      id.isTouched = true;
      const url = data[0]?.attachment_url;
      if (!url) {
        return;
      }
      id.patchValue(url);
    },
    [id]
  );

  return (
    <FormControl
      style={style}
      className={className}
      label={label}
      required={required}
      withoutLabel={withoutLabel}
    >
      <div className={styles['upload-image-container']}>
        <ImageUpload
          materials
          channel="wsc_web"
          maxSize={5 * 1024 * 1024}
          maxAmount={1}
          onSuccess={onSuccess}
        />
        {id.value ? (
          <>
            <img className={styles['upload-image']} src={id.value} alt="自定义背景图片" />
            <div className={styles['upload-image-overlay']}>更换图片</div>
          </>
        ) : null}
      </div>
      <FormDescription>最大支持 5M，可拖拽二维码调整位置、尺寸</FormDescription>
      <FormError>{id.error?.message}</FormError>
    </FormControl>
  );
}
