import { IValidator, Validators } from 'zent';

import { BackgroundType } from 'pages/address-qr/models/form';

import { createWhenInBackgroundTypeValidator } from '../../utils';

import { FormImageUploadField } from './form-image-upload-field';

export function CustomFormFields() {
  return (
    <>
      <FormImageUploadField
        name="backgroundImage"
        label=""
        validators={[
          createWhenInBackgroundTypeValidator<IValidator<string>>(
            BackgroundType.Custom,
            Validators.required('请上传背景图片')
          ),
        ]}
      />
    </>
  );
}
