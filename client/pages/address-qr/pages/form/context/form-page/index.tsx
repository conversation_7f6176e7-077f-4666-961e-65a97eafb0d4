import React, { useMemo, useContext } from 'react';

export interface IAddressQrFormPageProvider {
  id: number;
  isEdit: boolean;
}

const editorContext = React.createContext<IAddressQrFormPageProvider>(
  {} as IAddressQrFormPageProvider
);

export const AddressQrFormPageContextProvider: React.FC<IAddressQrFormPageProvider> = ({
  children,
  ...otherProps
}) => {
  // 这里要注意了，只有 activityId 变更了之后才会触发 context 的变化 (用个Memo可以避免重复渲染)
  const provider = useMemo(
    () => ({ id: otherProps.id, isEdit: otherProps.isEdit }),
    [otherProps.id, otherProps.isEdit]
  );

  return <editorContext.Provider value={provider}>{children}</editorContext.Provider>;
};

export const useAddressQrFormPageContext = () => {
  return useContext(editorContext);
};
