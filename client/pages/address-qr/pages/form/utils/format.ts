import {
  IAddressQrConfig,
  ISaveOrUpdateAddressQrConfigRequest,
} from 'pages/address-qr/api/models/address-qr';
import {
  IAddressQrFormData,
  TBackgroundColor,
  BackgroundType,
  TemplateSize,
  JumpType,
} from 'pages/address-qr/models/form';

/** 接口参数格式化到表单 */
export function formatInputData(data: IAddressQrConfig): IAddressQrFormData {
  /** 这里用Partial是为了演示，实际开发的时候要去掉 */
  const formData: IAddressQrFormData = {
    configName: data.configName,
    jumpType: data.jumpType as unknown as JumpType,
    backgroundType: data.backgroundType as unknown as BackgroundType,
    templateSize: data.templateSize as TemplateSize,
    backgroundColor: data.backgroundColor as TBackgroundColor,
    pageTitle: data.pageTitle,
    guideCopy: data.guideCopy,
    backgroundImage: data.backgroundImage,
    weassDeduplicateLiveCode: data.weassDeduplicateLiveCode,
    codeRect: data.codeRect,
  };
  return formData;
}

/** 表单格式化到接口参数 */
export function formatOutputData(data: IAddressQrFormData): ISaveOrUpdateAddressQrConfigRequest {
  const paramData: ISaveOrUpdateAddressQrConfigRequest = {
    configName: data.configName,
    jumpType: data.jumpType as unknown as string,
    backgroundType: data.backgroundType as unknown as string,
    templateSize: data.templateSize as string,
    backgroundColor: data.backgroundColor,
    pageTitle: data.pageTitle,
    guideCopy: data.guideCopy,
    backgroundImage: data.backgroundImage,
    liveCodeId: data.weassDeduplicateLiveCode?.liveCodeId,
    codeRect: data.codeRect,
  };
  return paramData;
}
