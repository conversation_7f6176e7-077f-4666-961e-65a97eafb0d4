import { IAddressQrFormData, BackgroundType } from 'pages/address-qr/models/form';
import { ValidatorMiddlewares, IValidator } from 'zent';

export const createWhenInBackgroundTypeValidator = <T extends IValidator<unknown>>(
  backgroundType: BackgroundType,
  validate: T
) => {
  return ValidatorMiddlewares.when(
    (ctx) => (ctx.getFormValue() as IAddressQrFormData)?.backgroundType === backgroundType
  )(validate) as T;
};
