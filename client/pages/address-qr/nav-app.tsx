import { useEffect } from 'react';
import { render } from 'react-dom';
import history from 'pages/address-qr/history';

import { Breadcrumb } from 'zent';
import { AddressQrPagePath } from './constants';

const NavList = [
  { name: '扫码寄件', path: AddressQrPagePath.List },
  { name: '新建活动', path: AddressQrPagePath.Create },
  { name: '编辑活动', path: AddressQrPagePath.Edit },
];

export const renderNav = (path: string) => {
  // 不能直接修改 baseBreads, 因为 SPA 下, 这个变量会继续保持下去
  const breadcrumItemList = NavList.filter((obj) => path.includes(obj.path)).map(
    (obj, index, list) => {
      return index === list.length - 1 ? (
        <span key={obj.path}>{obj.name}</span>
      ) : (
        <Breadcrumb.Item key={obj.path}>
          <a
            href={obj.path}
            onClick={(e) => {
              e.preventDefault();
              history.push(obj.path);
            }}
          >
            {obj.name}
          </a>
        </Breadcrumb.Item>
      );
    }
  );

  render(
    <Breadcrumb>
      <Breadcrumb.Item>
        <a href="/v4/apps/app-center">应用中心</a>
      </Breadcrumb.Item>
      {breadcrumItemList}
    </Breadcrumb>,
    document.getElementById('thirdbar-nav')
  );
};

export default function NavApp({ children, location }) {
  useEffect(() => {
    renderNav(location.pathname);
  }, [location.pathname]);

  return children;
}
