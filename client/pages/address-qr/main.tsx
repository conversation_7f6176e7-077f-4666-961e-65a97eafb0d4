import * as React from 'react';
import { render, unmountComponentAtNode } from 'react-dom';

import { defineApp } from '@youzan/micro-app-react';
import { Router, browserHistory } from 'react-router';

import pageHelp from 'shared/components/page-help';

import { AddressQrAppProvider } from './app-context';

import routes from './routes';

defineApp({
  mount: () => {
    pageHelp('trade_address_qr');
    browserHistory;
    render(
      <AddressQrAppProvider>
        <Router history={browserHistory} routes={routes} />
      </AddressQrAppProvider>,
      document.getElementById('js-react-container')
    );
  },
  unmount: () => {
    unmountComponentAtNode(document.getElementById('js-react-container') as HTMLElement);
  },
});
