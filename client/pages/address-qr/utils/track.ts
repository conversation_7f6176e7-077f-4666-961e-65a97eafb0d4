const logger = window.Logger;

export function track({
  et,
  ei,
  params,
}: {
  et: string;
  ei: string;
  params: Record<string, unknown>;
}) {
  logger &&
    logger.log({
      et,
      ei,
      en: undefined,
      pt: 'addressQr',
      params,
    });
}

export function trackClick({ name }: { name: string }) {
  track({
    et: 'click',
    ei: 'click_item',
    params: { name },
  });
}
