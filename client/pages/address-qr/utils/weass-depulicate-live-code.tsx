import { useState } from 'react';
import { Button, Notify, openDialog } from 'zent';

import { getWeassDeduplicateLiveCodeDetail, invalidWeassDeduplicateLiveCode } from '../api';
import { ExponentialBackoffStrategy, retry } from 'fns/retry';

function InvalidButton({ liveCodeId, onSuccess }: { liveCodeId: number; onSuccess: () => void }) {
  const [loading, setLoading] = useState(false);
  return (
    <Button
      type="primary"
      loading={loading}
      disabled={loading}
      onClick={() => {
        setLoading(true);
        retry(
          () =>
            invalidWeassDeduplicateLiveCode({ liveCodeId }).then((data) => {
              if (data) {
                Notify.success('失效成功');
                onSuccess();
              } else {
                throw new Error('失效失败');
              }
            }),
          {
            strategy: new ExponentialBackoffStrategy({ maxRetries: 3 }),
          }
        ).finally(() => {
          setLoading(false);
        });
      }}
    >
      失效
    </Button>
  );
}

export function confirmInvalidWeassDeduplicateLiveCode(liveCodeId: number) {
  getWeassDeduplicateLiveCodeDetail({ liveCodeId }).then((res) => {
    const closeDialog = openDialog({
      title: '提示',
      children: <>当前寄件码关联了企业微信助手的去重活码「{res.name}」，是否一起失效？</>,
      footer: (
        <>
          <Button
            onClick={() => {
              closeDialog();
            }}
          >
            取消
          </Button>
          <InvalidButton liveCodeId={liveCodeId} onSuccess={() => closeDialog()} />
        </>
      ),
    });
  });
}
