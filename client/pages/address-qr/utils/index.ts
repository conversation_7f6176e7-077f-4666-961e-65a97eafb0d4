import { Notify } from 'zent';

import { getWeappCodeUltra } from 'fns/weapp';
import { retry, ExponentialBackoffStrategy } from 'fns/retry';

import { JumpType } from '../models/form';

import { getWeassDeduplicateLiveCodePromotionUrl } from '../api';
import { getAddressQrWeappPath, getRootKdtId } from '../constants/weapp';

export const fetchCodeImage = async (data: {
  id?: number;
  jumpType: JumpType;
  liveCodeId?: number;
}) => {
  const kdtId = getRootKdtId();
  const guestKdtId = kdtId;

  const params: Record<string, unknown> = {};

  switch (data.jumpType) {
    case JumpType.WeassDeduplicateLiveCode:
      {
        if (!data.liveCodeId) {
          return '';
        }

        const result = await retry(
          () =>
            getWeassDeduplicateLiveCodePromotionUrl(
              {
                liveCodeId: Number(data.liveCodeId),
              },
              {
                shouldNotify: false,
              }
            ),
          {
            strategy: new ExponentialBackoffStrategy({
              maxRetries: 5,
              maxDelay: 2000,
              jitter: true,
            }),
          }
        ).catch((err) => {
          Notify.error(err.message || err.msg || err);
          throw err;
        });

        Object.assign(params, {
          page: result.webappPagePath,
        });
      }
      break;

    case JumpType.Direct:
      Object.assign(params, {
        page: getAddressQrWeappPath({
          id: data.id,
        }),
      });
      break;

    default:
      break;
  }

  const result = await getWeappCodeUltra({
    kdtId,
    guestKdtId,
    config: params,
    hyaLine: false,
  });

  return result ? `data:image/png;base64,${result}` : '';
};

export const fetchCodePath = async (data: {
  id?: number;
  jumpType: JumpType;
  liveCodeId?: number;
}) => {
  switch (data.jumpType) {
    case JumpType.WeassDeduplicateLiveCode: {
      if (!data.liveCodeId) {
        return '';
      }

      const result = await retry(
        () =>
          getWeassDeduplicateLiveCodePromotionUrl(
            {
              liveCodeId: Number(data.liveCodeId),
            },
            {
              shouldNotify: false,
            }
          ),
        {
          strategy: new ExponentialBackoffStrategy({
            maxRetries: 5,
            maxDelay: 2000,
            jitter: true,
          }),
        }
      ).catch((err) => {
        Notify.error(err.message || err.msg || err);
        throw err;
      });

      return result.webappPagePath || ' ';
    }

    case JumpType.Direct:
      return getAddressQrWeappPath({
        id: data.id,
      });

    default:
      return '';
  }
};
