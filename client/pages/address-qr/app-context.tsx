import { createContext, useContext, useMemo, useState } from 'react';

export interface IAddressQrAppContext {
  configName?: string;
  pageNo?: number;

  setConfigName: (configName: string) => void;
  setPageNo: (pageNo: number) => void;
}

export const AddressQrAppContext = createContext<IAddressQrAppContext>({
  configName: '',
  pageNo: 1,
  setConfigName: () => {},
  setPageNo: () => {},
});

export const useAddressQrAppContext = () => {
  return useContext(AddressQrAppContext);
};

export interface IAddressQrAppContextDispatch {
  setConfigName: (configName: string) => void;
  setPageNo: (pageNo: number) => void;
}

export const AddressQrAppProvider = ({ children }: { children: React.ReactNode }) => {
  const [configName, setConfigName] = useState('');
  const [pageNo, setPageNo] = useState(1);

  const value = useMemo(() => {
    return { configName, pageNo, setConfigName, setPageNo };
  }, [configName, pageNo]);

  return <AddressQrAppContext.Provider value={value}>{children}</AddressQrAppContext.Provider>;
};
