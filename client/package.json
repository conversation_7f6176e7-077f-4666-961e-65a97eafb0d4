{"name": "trade", "version": "1.0.0", "license": "MIT", "scripts": {"dev": "koko clean && koko dev", "dev-analyze": "koko clean && koko dev --analyze-bundle --analyze-speed", "cdn": "koko cdn", "clean": "koko clean", "build": "koko build && koko cdn", "preinstall": "npm_config_registry=http://registry.npm.qima-inc.com npx @kokojs/preinstall", "gen-cloud-info": "node cloud.config.js", "test": "NODE_ENV=test koko fe-test --coverage --runInBand"}, "dependencies": {"@youzan/captcha-verify-web": "1.0.2", "@youzan/cert-state": "1.1.3-beta.2", "@youzan/content-loader-react": "1.0.2", "@youzan/dynamic-table": "0.0.8", "@youzan/ebiz-components": "0.1.24", "@youzan/ebiz-select": "0.0.14", "@youzan/hummer-browser": "3.0.25", "@youzan/im-base-pure": "0.2.1", "@youzan/micro-app-react": "2.2.20", "@youzan/micro-transfer-dialog": "2.0.7", "@youzan/order-domain-pc-components": "1.1.1", "@youzan/payback-ads": "1.0.14", "@youzan/pc-ajax": "6.1.0", "@youzan/react-amap": "6.1.0", "@youzan/react-components": "6.0.7-rc.14", "@youzan/react-components-injector": "2.5.3", "@youzan/react-hooks": "1.1.1", "@youzan/retail-components": "4.9.5", "@youzan/sam-components": "1.2.1", "@youzan/shop-ability": "1.1.0", "@youzan/subsidy-ads": "1.0.6", "@youzan/url-generator": "0.0.2", "@youzan/url-utils": "1.0.0", "@youzan/utils": "2.3.2", "@youzan/utils-shop": "2.3.0", "@youzan/weass-b-pc-components": "2.8.10", "@youzan/web-biz-skynet-monitor": "1.2.1", "@youzan/wsc-pc-base": "5.0.10-beta.13", "@youzan/zan-hasaki": "2.19.14", "@youzan/zan-web-logger": "1.0.2", "@zent/compat": "2.3.1", "classnames": "^2.2.6", "date-fns": "^1.29.0", "filesize": "^3.6.1", "immer": "^10.1.1", "lodash": "^4.17.10", "moment": "^2.29.4", "prop-types": "^15.6.2", "react-dnd": "2.5.4", "react-dnd-html5-backend": "2.5.4", "react-redux": "^6.0.1", "react-rnd": "8.0.2", "react-router": "3.2.1", "redux": "4.0.1", "zan-pc-ajax": "4.7.0", "zan-shuai": "1.3.7", "zan-utils": "1.2.14", "zent": "10.0.13"}, "resolutions": {"@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "react": "^17.0.2", "zent": "10.0.13", "react-dom": "^17.0.2", "@kokojs/core": "4.0.27", "@kokojs/shared": "4.0.27"}, "devDependencies": {"@hot-loader/react-dom": "^17.0.0", "@testing-library/react": "^10.0.3", "@types/classnames": "2.2.9", "@types/history": "3.2.2", "@types/jest": "^25.2.1", "@types/react": "^17.0.0", "@types/react-calendar": "^3.5.0", "@types/react-dom": "^17.0.0", "@types/react-redux": "6.0.13", "@types/react-router": "3.0.15", "@youzan-cloud/cloud-biz-types": "1.1.53", "@youzan/eslint-config-biz-cloud": "2.12.3", "@youzan/koko-plugin-biz-cloud": "2.14.26", "@youzan/koko-preset-pc": "4.0.27", "@youzan/ranta-cloud-react": "2.12.3", "babel-plugin-import": "^1.9.1", "core-js": "2.6.12", "eslint": "^6.8.0", "querystring-es3": "0.2.1", "react": "^17.0.2", "react-amap": "^1.2.7", "react-calendar": "^3.6.0", "react-dom": "^17.0.2", "react-hot-loader": "^4.3.3", "redux-devtools-extension": "2.13.7", "redux-logger": "3.0.6", "typescript": "4.2.2"}, "browserslist": ["> 1% in alt-as", "Chrome >= 39", "ie >= 10", "not ie <= 8"], "husky": {"hooks": {"pre-commit": "koko pre-commit", "commit-msg": "koko commit-msg"}}}