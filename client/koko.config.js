/* eslint-disable no-undef */
const { resolve } = require('path');
const { isDev } = require('@kokojs/shared');
const webpack = require('webpack');

function getOutputPath() {
  return resolve(__dirname, isDev() ? '../static/koko-local' : '../static/koko-build');
}

const getZentDll = (name) => {
  return [`zent/es/${name}/index`, `zent/css/${name}`];
};

const getRCDll = (name) => {
  return [
    `@youzan/react-components/es/components/${name}/index`,
    `@youzan/react-components/es/components/${name}/style`,
  ];
};

module.exports = {
  name: 'wsc-pc-trade',
  presets: ['pc'],
  presetOptions: {
    pc: {
      libraryDll: {
        include: [],
      },
      componentDll: {
        include: [
          ...getZentDll('alert'),
          // design里会用的，为了防止样式加载顺序错乱的问题
          ...getRCDll('choose-dialog'),
          ...getRCDll('choose-link-menu'),
          ...getRCDll('upload-v2'),
        ],
      },
    },
  },
  outputPath: getOutputPath(),
  alias: {
    fns: resolve(__dirname, 'fns'),
    hooks: resolve(__dirname, 'hooks'),
    pages: resolve(__dirname, 'pages'),
    constants: resolve(__dirname, 'constants'),
    components: resolve(__dirname, 'components'),
    definitions: resolve(__dirname, 'definitions'),
    'shared/design-components': resolve(__dirname, 'design-components'),
    shared: resolve(__dirname, 'shared'),
    'zent/css/datetimepicker.css': '@zent/compat/css/datetimepicker.css',
  },
  configureWebpack: {
    output: {
      libraryTarget: 'window',
    },
    resolve: {
      fallback: {
        "querystring": require.resolve("querystring-es3")
      }
    },
    optimization: {
      splitChunks: {
        cacheGroups: {
          design: {
            chunks: 'all',
            minChunks: 1,
            test: /(design-components)|(captain-ui)|(captain-showcase)|(@zent\/design)/,
            priority: 10,
            reuseExistingChunk: true,
            name: 'design',
          },
        },
      },
    },
    ...(!isDev() && {
      externals: {
        react: 'React',
        'react-dom': 'ReactDOM',
        '@youzan/ranta-cloud-react': 'RantaCloudReact',
      },
    }),
    output: {
      libraryTarget: 'window',
      globalObject: 'window',
    },
    plugins: [
      new webpack.ProvidePlugin({
        process: 'process/browser',
      }),
    ],
  },
  baseScripts: ['./pages/global/main.js'],

  plugins: {
    'es-guard': {
      enable: true,
    },
    version: {
      name: { js: 'version_js', css: 'version_css' },
    },
    style: {
      loaderOptions: {
        sass: {
          // sass-loader 选项
          sassOptions: {
            includePaths: [resolve(__dirname, 'sass'), resolve(__dirname, 'shared/sass')],
          },
          sourceMap: isDev(),
        },
      },
    },
    babel: {
      compilePackages: [
        '@youzan-cloud/react-cloud-api',
        '@youzan/dynamic-table',
        'react-rnd',
        ...(!isDev() ? ['lunar-typescript', 'immer'] : []),
      ],
    },
    'biz-cloud': {},
  },
};
