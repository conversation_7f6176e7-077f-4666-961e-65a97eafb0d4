import React from 'react';
import head from 'lodash/head';
import get from 'lodash/get';
import { Notify, Checkbox } from 'zent';
import addDays from 'date-fns/add_days';
import getYear from 'date-fns/get_year';
import getMonth from 'date-fns/get_month';
import getDate from 'date-fns/get_date';
import setHours from 'date-fns/set_hours';
import getTime from 'date-fns/get_time';
import ExpressSelection from './ExpressSelection';
import SingleGoodsMultiExpress from './SingleGoodsMultiExpress';
import ExpressWayBill from './ExpressWayBill';
import XdExpressWayBill from './XdExpressWayBill';
import WeixinDelivery from './WeixinDelivery';
import WrapperWithFooter from './WrapperWithFooter';
import DeliveryBtn from './DeliveryBtn';
import api from '../api';
import { isFromGuangWxChannelOrder } from 'fns/order-helper';
import { getDefaultExpressId, setDefaultExpressId } from '../utils';
import {
  DELIVERY_MODE,
  XD_ELECTRONIC_FORM_SWITCH,
  EXPRESS_WAY_BILL_TYPES,
  SF_EXPRESS_CODE,
  expressLocalStorageKey,
} from '../const';
import {
  IExpressCompany,
  IPrinter,
  IGetDepositExpressRes,
  IExpressWayBill,
  IDeliveryWindowItemDetailInfo,
  IDeliveryInfo,
  IItemPack,
  IModel,
  IWechatDeliveryExpress,
  IWechatExpressWayBill,
} from '../type';
import { IExpressDTO } from 'definitions/electron-way-bill';
import { getPrinters } from 'fns/cainiao-printer/printer';
import { getExpressPrinter } from '../storage';
import { getManager } from '../xd-printer-manager';
import { YZShippingStatusEnum, PaymentTypeEnum } from 'constants/express';
import ExpressExtraSystemCall from '@youzan/order-domain-pc-components/es/delivery-dialog/components/express-extra-system-call';

import '@youzan/order-domain-pc-components/css/index.css';

interface IProps {
  deliveryType: number;
  model: IModel;
  selectedItems: IDeliveryWindowItemDetailInfo[];
  onSubmit: (deliveryInfo: IDeliveryInfo) => void;
  balance: number;
  submitting: boolean;
  orderNo: string;
  zentForm: any;
  handleSubmit: any;
  availableWechatExpress: IWechatDeliveryExpress[];
  isFromVideoShop: boolean;
  XdExpressWaybillMode?: string;
  orderInfo: any;
}

interface IState {
  expressId: number | '';
  expressName: string;
  expressNo: string;
  expressWayBill: IExpressWayBill;
  wechatExpressWayBill: IWechatExpressWayBill;
  itemPackList: IItemPack[];
  printers: IPrinter[];
  expressCompanies: IExpressCompany[] | IExpressDTO[];
  deposit: IGetDepositExpressRes;
  agreedSF: boolean;
  noPrinter: boolean;
  xdExpressWayBill: any;
  allowXdOrderUseElectronicForm: boolean;
  xdTakeOrderLoading: boolean;
}

const xdExpressLocalStorageKey = 'xd-express-way-bill-cache-express-id';

/**
 * 普通快递发货
 *
 * @class NormalExpress
 * @extends {React.Component}
 */
class NormalExpress extends React.Component<IProps, IState> {
  expressHelper: any;

  constructor(props) {
    super(props);
    this.state = {
      expressId: getDefaultExpressId(),
      expressName: '',
      expressNo: '',
      expressWayBill: {} as IExpressWayBill,
      wechatExpressWayBill: {
        weight: 0,
        expressId: 0,
        expressName: '',
        accountNo: '',
      } as IWechatExpressWayBill,
      itemPackList: [],
      printers: [],
      noPrinter: false,
      expressCompanies: [],
      deposit: {} as IGetDepositExpressRes,
      agreedSF: false,
      xdExpressWayBill: {},
      allowXdOrderUseElectronicForm: false,
      xdTakeOrderLoading: false,
    };
  }
  // 是否为新电子面单
  get isNewWayBill() {
    return +window._global.electronWayBillVersion === 2;
  }

  componentDidMount() {
    const { isFromVideoShop, XdExpressWaybillMode } = this.props;
    const allowXdOrderUseElectronicForm =
      XdExpressWaybillMode === XD_ELECTRONIC_FORM_SWITCH.onlyWaybill ||
      XdExpressWaybillMode === XD_ELECTRONIC_FORM_SWITCH.mix;
    this.setState({
      allowXdOrderUseElectronicForm,
    });
    if (isFromVideoShop && allowXdOrderUseElectronicForm) {
      // 减少不必要的请求
      return;
    }
    if (!this.isNewWayBill) {
      // 获取打印机
      this.fetchPrinterList();
      this.fetchDeliveryExpressCompanies();
      this.fetchDeposit();
      // 获取有赞寄件开通状态
      this.fetchYZShoppingInfo();
    }
  }

  async fetchPrinterList() {
    // 新电子面单-获取菜鸟打印机
    if (this.isNewWayBill) {
      return getPrinters().then((printers) => {
        this.setState({
          printers,
          noPrinter: !printers.length,
        });
        const printerId = getExpressPrinter();
        if (printerId && printers.find((item) => item.id === printerId)) {
          this.setState({
            expressWayBill: {
              ...this.state.expressWayBill,
              printerId,
            },
          });
        }
      });
    }
    return api.getPrinterList().then(({ items }) => {
      this.setState({
        printers: items || [],
        noPrinter: !items.length,
      });
    });
  }

  // isChangeForm - 是否修改表单，默认为true， 当仅更新服务商列表时，不修改表单
  fetchDeliveryExpressCompanies(isChangeForm = true) {
    const { auditNo = '', weight = '' } = this.state.expressWayBill;
    const fetchRequest = this.isNewWayBill
      ? api.getExpressByNewWayBill({
          auditNo,
          weight,
          orderNo: this.props.orderNo,
        })
      : api.getDeliveryExpressCompaniesV2();
    return fetchRequest.then((data) => {
      let expressCompanies = data || [];
      const defaultExpressKey = window.localStorage.getItem(expressLocalStorageKey) || '';
      // 默认选择上一次使用的物流公司，其次按照京东>申通>顺丰排序
      if (defaultExpressKey && expressCompanies.length > 0) {
        const [defaultExpressId, defaultPaymentType] = defaultExpressKey.split('_');
        const defaultExpress: any = expressCompanies.find(
          (item) =>
            item.expressId === +defaultExpressId &&
            item.paymentType === +defaultPaymentType &&
            item?.channelStatus !== 2
        );
        const companySortList = ['顺丰速运', '申通快递', '京东物流'];
        expressCompanies = expressCompanies.map(item => {
          const sortNum = companySortList.findIndex(company => company === item.expressName);
          item.sortNum = sortNum;
          // 默认选择上一次使用的物流公司，排序在第一个
          if (item.expressId === +defaultExpressId && item.paymentType === +defaultPaymentType) {
            item.sortNum = 100;
          }
          // 非有赞寄件推荐的，排序在最后
          if (!item.recommend) {
            item.sortNum = -2;
          }
          return item;
        }).sort((pre, next) => next.sortNum - pre.sortNum);
        // 缓存快递公司列表, 供有赞寄件小广告使用
        if (window._global) {
          window._global.expressCompanies = expressCompanies;
        }
        if (isChangeForm) {
          if (defaultExpress) {
            this.handleValueChange('expressWayBill', {
              ...this.state.expressWayBill,
              ...defaultExpress,
              expressWayBillType: defaultExpress.type,
            });
          }
        }
      }
      this.setState({
        expressCompanies,
      });
    });
  }

  fetchYZShoppingInfo() {
    api.getYZShoppingServiceInfo().then(({ serviceStatus, overdueFee, overdueLimitFee }) => {
      this.handleValueChange('expressWayBill', {
        ...this.state.expressWayBill,
        YZShoppingInfo: {
          waitJoin: serviceStatus === YZShippingStatusEnum.WAIT_JOIN,
          joined: serviceStatus === YZShippingStatusEnum.JOINED,
          suspended: serviceStatus === YZShippingStatusEnum.SUSPEND,
          overdueFee,
          overdueLimitFee,
        },
      });
    });
  }

  updateDeliveryExpress() {
    // 请求需要等setState生效，因此延迟一下
    setTimeout(() => {
      this.fetchDeliveryExpressCompanies(false);
    });
  }

  fetchDeposit() {
    api.getDepositExpress().then((data) => {
      this.setState({
        // @ts-ignore
        deposit: data || {},
      });
    });
  }

  handleSubmit = () => {
    const { deliveryType, onSubmit, selectedItems, isFromVideoShop } = this.props;
    const { expressId, expressName, expressNo, itemPackList, wechatExpressWayBill } = this.state;
    const isSingleGoodsMultiExpress = this.checkIsSingleGoodsMultiExpress();
    const deliveryInfo = {
      deliveryType,
    } as IDeliveryInfo;

    if (deliveryType === 12) {
      if (isSingleGoodsMultiExpress && itemPackList && itemPackList.length) {
        const total = itemPackList.reduce((count, item) => {
          return count + parseInt(item.num as string, 10);
        }, 0);
        const num = head(selectedItems)!.num;
        // 单品多运只能选择一个商品
        if (+num !== total) {
          return Notify.error(`每个包裹必须填写商品数量且包裹数量总和等于 ${num}`);
        }
        deliveryInfo.isSingleGoodsMultiExpress = true;
        deliveryInfo.itemPackList = itemPackList;
      } else {
        deliveryInfo.express = {
          expressId,
          expressName,
          expressNo,
        };
        setDefaultExpressId(expressId);
      }
    }
    if (deliveryType === 14 && !isFromVideoShop) {
      if (!this.isNewWayBill) {
        deliveryInfo.expressWayBill = this.getWayBillData();
      } else {
        deliveryInfo.expressWayBill = this.expressHelper.getExpressPostData();
        this.expressHelper.setExpressStorage();
      }
    }

    // 视频号小店订单 在线下单
    if (deliveryType === 14 && isFromVideoShop) {
      this.getXdWayBillData(deliveryInfo, onSubmit);
      return;
    }

    if (deliveryType === DELIVERY_MODE.weixinDelivery.value) {
      // @ts-ignore
      deliveryInfo.expressWayBill = wechatExpressWayBill;
    }
    // console.log('deliveryInfo', deliveryInfo);
    // return;

    return onSubmit && onSubmit(deliveryInfo);
  };

  // 电子面单发货
  getWayBillData = () => {
    const { expressWayBill, printers } = this.state;
    const { printerId, expressId, pickTime, expressWayBillType, weight, ...rest } = expressWayBill;

    const printer = printers.find((item) => item.id === printerId) || ({} as IPrinter);

    if (
      expressId === SF_EXPRESS_CODE &&
      (expressWayBillType === EXPRESS_WAY_BILL_TYPES.printAndCallCourier.value ||
        expressWayBillType === EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value)
    ) {
      const now = new Date();
      // 后端需要时间精确到秒
      const startAppointment =
        getTime(
          addDays(
            setHours(new Date(getYear(now), getMonth(now), getDate(now)), pickTime.time),
            pickTime.day as number
          )
        ) / 1000;
      rest.startAppointment = startAppointment;
      rest.endAppointment = startAppointment + 3600;
    }

    const waybillData = {
      ...rest,
      expressId,
      expressWayBillType,
      printerKey: printer.equipmentKey,
      printerDeviceNo: printer.equipmentNumber,
      printerChannel: printer.equipmentTypeId || 0,
      printerId,
      waybillVersion: this.isNewWayBill ? 2 : 1,
    } as IExpressWayBill;
    if (weight) {
      waybillData.weight = weight;
    }
    return waybillData;
  };

  // 小店电子面单发货
  getXdWayBillData = (deliveryInfo, onSubmit) => {
    const { selectedItems, orderNo, orderInfo } = this.props;
    const { xdExpressWayBill } = this.state;
    const {
      expressId,
      expressName,
      selectedAddress,
      mpId,
      outWaybillAccountId,
      outShopId,
      outSiteId,
      faceFormTemplateId,
      printerName,
      monthlyCardId,
      thirdProductTypeId,
    } = xdExpressWayBill;
    window.localStorage.setItem(xdExpressLocalStorageKey, xdExpressWayBill.expressId + '');

    // 判断是否是爱逛侧视频号小店订单，如果是的话 则需要传入爱逛视频号小店ID（默认兜底值为：wx0000000000000000）
    const _isFromGuangWxChannelOrder = isFromGuangWxChannelOrder(orderInfo);
    const guangWxChannelAppId = _isFromGuangWxChannelOrder
      ? get(orderInfo, 'tcExtra.GUANG_VIDEO_CHANNEL_APP_ID', 'wx0000000000000000')
      : undefined;

    // 流程1 取号 -> 打印面单
    // 流程2 取号 -> 发货

    // 取号
    const order = {
      orderNo,
      orderItemInfos: selectedItems.map((item) => {
        return {
          itemCnt: item.num,
          orderItemId: item.item_id,
        };
      }),
    };
    const telephone = selectedAddress.areaCode
      ? selectedAddress.areaCode + '-' + selectedAddress.telephone
      : selectedAddress.telephone;
    const receiver = {
      address: selectedAddress.address,
      city: selectedAddress.city,
      county: selectedAddress.county,
      mobile: selectedAddress.mobilePhone || telephone,
      name: selectedAddress.contactName,
      province: selectedAddress.province,
      street: selectedAddress.area,
    };
    const params = {
      expressId,
      mpId,
      order: JSON.stringify(order),
      receiver: JSON.stringify(receiver),
      sender: JSON.stringify(receiver),
      thirdAcctId: outWaybillAccountId,
      thirdShopId: outShopId,
      thirdSiteCode: outSiteId,
      guangWxChannelAppId,
      monthlyCardId,
      thirdProductTypeId,
    };
    this.setState({
      xdTakeOrderLoading: true,
    });
    api
      .getXdDeliveryFaceOrder(params)
      .then((res: any) => {
        const { expressNo, thirdWaybillId } = res;
        // 打印面单
        const printerParmas: any = {
          mpId,
          thirdWaybillId,
          thirdTemplateId: faceFormTemplateId,
          printType: 0,
          expressNo,
          expressId,
          printer: printerName,
          guangWxChannelAppId,
        };
        getManager()?.startPrinting(printerParmas);

        // 发货
        deliveryInfo.express = {
          expressId,
          expressName,
          expressNo,
        };
        deliveryInfo.distOrderExtension = {
          multiPlatExpressWaybill: 'wx_video_xiaodian',
          thirdWaybillId,
        };
        deliveryInfo.deliveryType = 12;

        this.setState({
          xdTakeOrderLoading: false,
        });
        return onSubmit && onSubmit(deliveryInfo);
      })
      .catch(() => {
        this.setState({
          xdTakeOrderLoading: false,
        });
      });
  };

  handleAddPack = () => {
    const { expressId, expressName, expressNo } = this.state;
    this.setState({
      itemPackList: [
        {
          express: {
            expressId,
            expressNo,
            expressName,
          },
          num: 1,
        },
        {
          express: {
            expressId: getDefaultExpressId(),
            expressNo: '',
            expressName: '',
          },
          num: 1,
        },
      ],
    });
  };

  handleCleanPackList = (val?: IItemPack) => {
    const { express } = val || ({ express: {} } as IItemPack);
    this.setState({
      itemPackList: [],
      expressId: express.expressId || getDefaultExpressId(),
      expressName: express.expressName || '',
      expressNo: express.expressNo || '',
    });
  };

  /**
   * 快递发货单品多运
   * 1.开启单品多件发货配置
   * 2.仅选择一种商品
   * 3.选择的这种商品数量大于1
   * 4.选择【自己联系物流】
   * 5.非周期购（周期购不支持单品多运）
   */
  checkIsSingleGoodsMultiExpress = () => {
    const { deliveryType, model, selectedItems } = this.props;
    const selectItem = head(selectedItems);
    const isSingleGoodsMultiExpress =
      model.open_multiple_delivery &&
      selectedItems.length === 1 &&
      selectItem &&
      selectItem.num > 1 &&
      deliveryType === 12 &&
      !model.multi_period_delivery_info;
    return isSingleGoodsMultiExpress;
  };

  getExceptionInfo = () => {
    const { model } = this.props;
    const desc = get(model, 'electronic_sheet_exception_info.electronic_sheet_exception_desc', '');
    const code = get(model, 'electronic_sheet_exception_info.electronic_sheet_exception_code', 0);
    return {
      desc,
      code,
    };
  };

  handleValueChange = (key: keyof IState, val: any) => {
    // @ts-ignore
    this.setState({
      [key]: val,
    });
    // 如果改动的是expressWayBill并且auditNo是'',则重置表单
    if (
      key === 'expressWayBill' &&
      val?.auditNo === '' &&
      val?.fakeId === '' &&
      !val.onlyExpressWayBillType
    ) {
      this.props.zentForm.setFieldsValue({ expressAddress: '' });
    }
  };

  handleExpressValueChange = (value, isCover = false) => {
    if (isCover) {
      this.setState({
        expressWayBill: value,
      });
    } else {
      this.setState((prevState) => ({
        expressWayBill: {
          ...prevState.expressWayBill,
          ...value,
        },
      }));
    }
  };


  handleGenerateHelper = (helper) => {
    this.expressHelper = helper;
  };

  checkCertification() {
    // 一年多的时间里该接口都是无效的决定废弃：https://doc.qima-inc.com/pages/viewpage.action?pageId=217951843
    // 如果有赞代扣，需要判断是否通过企业或者个人认证
    // if (certType < 2 || certType >= 10) {
    //   return false;
    // }
    return true;
  }

  checkDepositFee() {
    // 2023-06-14 推荐物流项目取消保证金校验，以后端返回的depositOffline为准
    const { expressWayBill, deposit } = this.state;
    return expressWayBill.depositOffline || deposit.depositAvl >= 100000;
  }

  // checkBalance() {
  //   const { balance } = this.props;
  //   const { expressWayBill } = this.state;
  //   const { expressFee = 0 } = expressWayBill;
  //   return balance - expressFee >= 0;
  // }

  checkExpressWayBillValid() {
    const { expressWayBill } = this.state;
    if (expressWayBill?.isPay) {
      return this.checkCertification() && this.checkDepositFee();
    }
    return true;
  }

  // 单品多送
  renderSingleGoodsMultiExpress() {
    const { itemPackList } = this.state;
    return (
      <SingleGoodsMultiExpress
        packItems={itemPackList}
        onCleanPackList={this.handleCleanPackList}
        onChange={(val) => this.handleValueChange('itemPackList', val)}
      />
    );
  }

  renderAddPack() {
    const { itemPackList } = this.state;
    const isSingleGoodsMultiExpress = this.checkIsSingleGoodsMultiExpress();
    if (isSingleGoodsMultiExpress && itemPackList.length === 0) {
      return (
        <div>
          一种包裹按数量拆分多包裹发送（仅针对一种单品）
          <a className="control-item" onClick={this.handleAddPack}>
            新增运单
          </a>
        </div>
      );
    }
  }

  // 普通快递发货
  renderNormal() {
    const { expressId, expressName, expressNo } = this.state;
    const { isExchange } = this.props.model;
    const { isFromVideoShop = false } = this.props;
    const isShowTips = !isExchange;

    return (
      <div className="delivery-content">
        <ExpressSelection
          expressId={expressId}
          expressName={expressName}
          expressNo={expressNo}
          onChange={this.handleValueChange}
        />
        {isShowTips && (
          <div className="gray">
            *请仔细填写物流公司及物流单号，发货后72小时内仅支持做一次更正，逾期不可修改
          </div>
        )}
        {!isFromVideoShop && this.renderAddPack()}
      </div>
    );
  }

  // 电子面单
  renderExpressWayBill() {
    const { expressWayBill, printers, expressCompanies, deposit, noPrinter } = this.state;
    const { orderNo } = this.props;
    if (this.isNewWayBill) {
      return (
        <div className="od-express-content">
          <ExpressExtraSystemCall
            orderNo={orderNo}
            express={expressWayBill}
            onExpressValueChange={this.handleExpressValueChange}
            waybillVersion={2}
            generateHelper={this.handleGenerateHelper}
          />
        </div>
      );
    }
    return (
      <>
        {expressCompanies?.length > 0 && (
          <ExpressWayBill
            orderNo={orderNo}
            expressWayBill={expressWayBill}
            onChange={(val) => this.handleValueChange('expressWayBill', val)}
            printers={printers}
            noPrinter={noPrinter}
            expressCompanies={expressCompanies as IExpressCompany[]}
            deposit={deposit}
            certValid={this.checkCertification()}
            depositValid={this.checkDepositFee()}
            exceptionInfo={this.getExceptionInfo()}
            fetchExpressList={this.fetchDeliveryExpressCompanies.bind(this)}
            updateExpressList={this.updateDeliveryExpress.bind(this)}
            fetchPrinters={this.fetchPrinterList.bind(this)}
            isNewWayBill={this.isNewWayBill}
          />
        )}
      </>
    );
  }

  // 电子面单
  renderXDExpressWayBill() {
    const { xdExpressWayBill } = this.state;
    const { orderNo, orderInfo, zentForm } = this.props;

    return (
      <>
        <XdExpressWayBill
          orderNo={orderNo}
          xdExpressWayBill={xdExpressWayBill}
          onChange={(val) => this.handleValueChange('xdExpressWayBill', val)}
          orderInfo={orderInfo}
          zentForm={zentForm}
        />
      </>
    );
  }

  renderSFExpressFooter = (onSubmit) => {
    const { submitting, zentForm } = this.props;
    const { agreedSF, expressWayBill } = this.state;
    const disabled =
      !agreedSF ||
      !zentForm.isValid() ||
      !this.checkExpressWayBillValid() ||
      (!expressWayBill.auditNo && !expressWayBill.fakeId) ||
      (expressWayBill.paymentType === PaymentTypeEnum.authority &&
        expressWayBill.YZShoppingInfo?.suspended);

    return (
      <div>
        <Checkbox
          checked={agreedSF}
          onChange={() => {
            this.setState({ agreedSF: !agreedSF });
          }}
        >
          同意
          <a
            href="https://bbs.youzan.com/forum.php?mod=viewthread&tid=670164"
            target="_blank"
            rel="noopener noreferrer"
          >
            《顺丰快件运单契约条款》
          </a>
        </Checkbox>
        <DeliveryBtn loading={submitting} disabled={disabled} onClick={onSubmit} />
      </div>
    );
  };

  renderWeixinDelivery = () => {
    const { wechatExpressWayBill } = this.state;
    const { availableWechatExpress } = this.props;
    return (
      <WeixinDelivery
        wechatExpressWayBill={wechatExpressWayBill}
        availableWechatExpress={availableWechatExpress}
        onChange={(val) => this.handleValueChange('wechatExpressWayBill', val)}
      />
    );
  };

  render() {
    const {
      deliveryType,
      submitting,
      handleSubmit,
      zentForm,
      isFromVideoShop,
      selectedItems = [],
    } = this.props;
    const {
      itemPackList,
      expressWayBill,
      xdExpressWayBill,
      allowXdOrderUseElectronicForm,
      xdTakeOrderLoading,
    } = this.state;
    const isFormValid = zentForm.isValid();
    let disabled = !isFormValid;
    let footer: ((func: any) => React.ReactNode) | null = null;

    const isSingleGoodsMultiExpress = this.checkIsSingleGoodsMultiExpress();
    // 打印电子面单
    let content: React.ReactNode = null;
    if (deliveryType === 14 && !isFromVideoShop) {
      const exceptionInfo = this.getExceptionInfo();
      disabled =
        disabled ||
        (!this.isNewWayBill && exceptionInfo.code > 0) ||
        this.expressHelper && !this.expressHelper.checkExpressValid() ||
        (!expressWayBill.auditNo && !expressWayBill.fakeId) ||
        (expressWayBill.paymentType === PaymentTypeEnum.authority &&
          expressWayBill.YZShoppingInfo?.waitJoin &&
          !expressWayBill.agreeProtocol) ||
        (expressWayBill.paymentType === PaymentTypeEnum.authority &&
          expressWayBill.YZShoppingInfo?.suspended);
      content = this.renderExpressWayBill();

      // 顺丰需要同意 《顺丰快递运单契约条款》
      if (expressWayBill.expressId === 7 && !this.isNewWayBill) {
        footer = this.renderSFExpressFooter;
      }
    }
    // 视频号小店在线下单电子面单发货单独逻辑
    if (deliveryType === 14 && isFromVideoShop && allowXdOrderUseElectronicForm) {
      // 加盟判断库存
      const isJoinDisabled = xdExpressWayBill.outSiteId && xdExpressWayBill.available === 0;
      // 加盟 或者 直营
      const hasValidLogistics =
        xdExpressWayBill.outSiteId ||
        (xdExpressWayBill.monthlyCardId && xdExpressWayBill.thirdProductTypeId);
      disabled =
        disabled ||
        selectedItems.length === 0 ||
        !xdExpressWayBill.addressId ||
        !hasValidLogistics ||
        isJoinDisabled ||
        !xdExpressWayBill.printerName ||
        !xdExpressWayBill.faceFormTemplateId;
      content = this.renderXDExpressWayBill();
    }

    // 选择自己联系快递，需要选择快递公司、填写运单号
    if (deliveryType === 12) {
      if (!isSingleGoodsMultiExpress || !itemPackList || itemPackList.length === 0) {
        content = this.renderNormal();
      }
      if (isSingleGoodsMultiExpress && itemPackList.length > 0) {
        content = this.renderSingleGoodsMultiExpress();
      }
    }

    if (deliveryType === DELIVERY_MODE.weixinDelivery.value) {
      content = this.renderWeixinDelivery();
    }
    const footerExtra = deliveryType === 14 && this.expressHelper && this.expressHelper.renderFooterExtra() || null;

    return (
      <WrapperWithFooter
        loading={submitting || xdTakeOrderLoading}
        onSubmit={this.handleSubmit}
        handleSubmit={handleSubmit}
        disabled={disabled}
        footer={footer}
        extra={footerExtra}
      >
        {content}
      </WrapperWithFooter>
    );
  }
}

export default NormalExpress;
