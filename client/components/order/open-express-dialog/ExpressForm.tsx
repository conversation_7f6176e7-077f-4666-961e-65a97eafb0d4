import React from 'react';
import { Notify, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Button, Notice } from 'zent';
import { Form } from '@zent/compat';
import { windowOpen } from '@youzan/url-utils';
import { openWXSmallShopDialog } from 'components/order/open-express-dialog';
import DeliveryInfo from './DeliveryInfo';
import DeliveryTypeSelection from './DeliveryTypeSelection';
import Intracity from './express/Intracity';
import NormalExpress from './express/NormalExpress';
import SelfPickup from './express/SelfPickup';
import SelfPickupVerify from './express/SelfPickupVerify';
import { OrderContext } from './order-context';
import { makeRequest } from './utils';
import {
  IModel,
  IDeliveryWindowItemDetailInfo,
  IDeliveryInfo,
  IExchangeGoodsRequest,
  BindStatus,
  IWechatDeliveryExpress,
} from './type';
import {
  WEIXIN_DELIVERY_HELPER,
  WEIXIN_AUTH,
  DELIVERY_MODE,
  XD_ELECTRONIC_FORM_SWITCH,
  ORDER_SEND_PROCESS,
  EXPRESS_WAY_BILL_TYPES,
  quickbackAdNextShowLocalStorageKey
} from './const';
import api from './api';
import { setDeliveryType, getDeliveryType } from './storage';
import { doPrint } from 'fns/cainiao-printer/printer';
import { WaybillVersionEnum } from 'constants/express';
import { bizLogger } from 'fns/skynet-monitor';
import { StageStatus } from '@youzan/web-biz-skynet-monitor/lib/constants';
import { beforeConfirmShipment } from '@youzan/order-domain-pc-components/es/delivery-dialog/components/express-extra-system-call/extra';

const { createForm } = Form;
const { openDialog, closeDialog } = Dialog;

const MULTI_PERIOD_DELIVERY_ID = 'multi_period_delivery_info_can_not_send';

const { XdExpressWaybillSwitch = '{}' } = _global;
const XdExpressWaybillMode =
  JSON.parse(XdExpressWaybillSwitch).mode || XD_ELECTRONIC_FORM_SWITCH.old;

interface IProps extends ZENTFORM<{}>, IZentCompatFormProps {
  model: IModel;
  selectedItems: IDeliveryWindowItemDetailInfo[];
  isWholeSend: boolean;
  handleExpressSuccess: (totalSent: number, ifOpenQuickBackAdDialog?: boolean) => void;
  orderNo: string;
  deliveryUrl: string;
  YZShoppingApplyUrl?: string;
  calculateFeeUrl: string;
  onClose: () => void;
  callSource: number;
  kdtId: number;
  isSmallStoreOrder: boolean;
  refundInfo?: any;
  callback?: () => void;
  isFromVideoShop: boolean;
  outOrderNo: string;
}

interface IState {
  deliveryType: number;
  balance: number;
  submitting: boolean;
  availableWechatExpress: IWechatDeliveryExpress[];
  isFromVideoShop: boolean;
}

class ExpressForm extends React.Component<IProps, IState> {
  static contextType = OrderContext;

  static defaultProps = {
    model: {},
  };

  isMultiPeriod: boolean;
  isNoramlExpress: boolean;
  isSelfPickup: boolean;
  isIntracity: boolean;
  constructor(props) {
    super(props);
    const { model = {}, isSmallStoreOrder, isTradeModuleV3Order, isFromVideoShop } = props;
    const {
      balance, // 店铺余额
      dist_type: distType,
    } = model;
    this.isMultiPeriod = !!model.multi_period_delivery_info;
    this.isNoramlExpress = +distType === 0 || this.isMultiPeriod;
    this.isSelfPickup = +distType === 1 && !this.isMultiPeriod;
    this.isIntracity = +distType === 2 && !this.isMultiPeriod;
    this.state = {
      deliveryType: this.getDefaultDeliveryType(model, {
        isSmallStoreOrder,
        isTradeModuleV3Order,
        isFromVideoShop,
      }),
      balance,
      submitting: false,
      availableWechatExpress: [],
      isFromVideoShop
    };
  }

  alertConfirm = (originType: number, isWxLogisticsAuthorized: boolean, wxExpressBind: boolean) => {
    if (!isWxLogisticsAuthorized) {
      Sweetalert.confirm({
        content: <p>小程序暂未获得“快递配送权限”，请先完成授权。</p>,
        closeBtn: true,
        maskClosable: true,
        confirmText: '去授权',
        onConfirm: () => {
          windowOpen(WEIXIN_AUTH, '_blank');
        },
        onClose: () => {
          this.setState({ deliveryType: originType });
        },
      });
    } else if (!wxExpressBind) {
      Sweetalert.confirm({
        content: <p>未完成快递公司账号绑定，请先完成快递公司账号绑定。</p>,
        closeBtn: true,
        maskClosable: true,
        confirmText: '去绑定',
        onConfirm: () => {
          windowOpen(WEIXIN_DELIVERY_HELPER, '_blank');
        },
        onClose: () => {
          this.setState({ deliveryType: originType });
        },
      });
    }
  };

  checkWxAuthAndBind = (originType: number) => {
    api
      .searchWechatDeliveryConfig({
        includePrinterInfo: false,
        includeAllSupportDeliveryAddress: false,
      })
      .then((result) => {
        if (result) {
          const expresses = result.wechatDeliveryExpressAggDTOS || [];
          const availableWechatExpress: IWechatDeliveryExpress[] = [];
          expresses.forEach((express) => {
            const accouts = express.deliveryBindAccountDTOS;
            for (let i = 0; i < accouts.length; i++) {
              const account = accouts[i];
              if (account.bindStatusCode === BindStatus.Success) {
                availableWechatExpress.push(express);
                break;
              }
            }
          });
          this.setState({ availableWechatExpress });
          this.alertConfirm(
            originType,
            result.isWxLogisticsAuthorized,
            availableWechatExpress.length > 0,
          );
        }
      })
      .catch((err) => {
        Notify.error(err);
        this.alertConfirm(originType, false, false);
      });
  };

  // 根据不同的 发货类型 选择默认的 配送方式
  getDefaultDeliveryType = (
    model: IModel,
    { isSmallStoreOrder, isTradeModuleV3Order, isFromVideoShop },
  ) => {
    /**
     * 发货方式 deliveryType
     * 12, "快递 - 商家呼叫快递"
     * 13, "快递 - 无需物流"
     * 14, "快递 - 电子面单"
     * 21, "同城送 - 商家呼叫三方配送"
     * 22, "同城送 - 商家自主配送"
     */
    let deliveryType;
    const isMultiPeriod = !!model.multi_period_delivery_info;
    const distType = +model.dist_type!;
    const { isExchange } = model;
    if (distType === 0 || isMultiPeriod) {
      // 发货方式 - 快递，默认为电子面单
      deliveryType = 14;
      if (isSmallStoreOrder) {
        deliveryType = DELIVERY_MODE.expressByMerchant.value;
      }
      // 微信视频号3.0订单暂不支持电子面单
      if (isTradeModuleV3Order) {
        deliveryType = DELIVERY_MODE.expressByMerchant.value;
      }

      // 视频号小店订单通过后端配置控制发货方式选择
      if (isFromVideoShop) {
        if (XdExpressWaybillMode === XD_ELECTRONIC_FORM_SWITCH.old) {
          deliveryType = DELIVERY_MODE.expressByMerchant.value;
        }
      }
    } else if (distType === 2 && !isMultiPeriod) {
      // 发货方式 - 同城，没有默认
      // 2023/05/31改为默认选中缓存的发货方式
      deliveryType = getDeliveryType() || -1;
    } else if (distType === 1) {
      // 发货方式 - 自提，默认为需要验证自提码
      deliveryType = 1;
      if (isSmallStoreOrder) {
        deliveryType = DELIVERY_MODE.noNeedVerify.value;
      }
    }

    if (isExchange) {
      // 换货, 默认为商家自己联系快递
      deliveryType = 12;
    }

    return deliveryType;
  };

  getSelectedItems = () => {
    return this.props.selectedItems.map((item) => {
      return {
        itemId: item.item_id, // 订单商品Id
        num: item.num, // 需要发货的商品数量
        weight: item.weight, // 需要发货的商品重量
      };
    });
  };

  handleDeliveryTypeChange = (type: number) => {
    const originType = this.state.deliveryType;
    this.setState({
      deliveryType: type,
    });
    // 如果是同城配送，缓存发货方式
    if (this.props.model.dist_type === 2) {
      setDeliveryType(type);
    }
    if (type === DELIVERY_MODE.weixinDelivery.value) {
      this.checkWxAuthAndBind(originType);
    }
  };

  getRefundItems = () => {
    const { selectedItems } = this.props;
    const refundItems = selectedItems.filter(
      (item) => item.refund_status_code === 'hadProcessRefund',
    );
    // 如果选择的商品中有退款中的商品，则弹窗提示
    return refundItems;
  };

  exchangeGoods(deliveryInfo: IDeliveryInfo) {
    const { express } = deliveryInfo;
    const { orderNo, handleExpressSuccess } = this.props;
    const { refundId, version, num } = this.props.refundInfo;
    let params: IExchangeGoodsRequest = {
      orderNo,
      refundId,
      version,
    };
    if (express) {
      const { expressNo, expressId } = express;
      params = {
        ...params,
        logisticsNo: expressNo,
        companyCode: expressId as number,
      };
    }
    this.setState({
      submitting: true,
    });
    api
      .deliveryExchangeGoods(params)
      .then(() => {
        Notify.success('发货成功');
        handleExpressSuccess(num);
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Finish,
          detail: {
            title: '订单发货-换货方式发货成功',
          },
        });
      })
      .catch((msg) => {
        Notify.error(msg);
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Error,
          detail: {
            title: '订单发货-换货方式发货失败',
            message: msg,
          },
        });
      })
      .finally(() => {
        this.setState({
          submitting: false,
        });
      });
  }

  handleOpenWXSmallShopDialog = (deliveryInfo: IDeliveryInfo) => {
    const { selectedItems } = this.props;
    const closeDialog = openWXSmallShopDialog({
      orderNo: this.props.orderNo,
      outOrderNo: this.props.outOrderNo,
      fromDialog: true,
      callback: (shouldRefresh) => {
        closeDialog();
        if (!shouldRefresh) {
          this.submitData(deliveryInfo);
        } else {
          this.props.handleExpressSuccess(selectedItems.length);
        }
      },
    });
  };

  handleSubmit = (deliveryInfo: IDeliveryInfo) => {
    const {
      isWholeSend,
      model: { isExchange },
      isSmallStoreOrder,
      isFromVideoShop,
    } = this.props;

    if (!isSmallStoreOrder) {
      bizLogger.start({ name: ORDER_SEND_PROCESS, timeout: 10000 });
    }

    if (isExchange) {
      // 如果是换货接口，调用换货方法
      this.exchangeGoods(deliveryInfo);
      return;
    }

    if (isWholeSend) {
      if (isSmallStoreOrder) {
        return this.handleOpenWXSmallShopDialog(deliveryInfo);
      }
      // 如果是整单发货，不需要校验是否选中商品
      this.submitData(deliveryInfo);
      return;
    }
    try {
      const { selectedItems, model = {} as IModel } = this.props;

      // 针对周期购订单
      if (this.isMultiPeriod) {
        const now = Date.now();
        const { last_estimate_delivery_time: lastDeliveryTime, period } =
          model.multi_period_delivery_info!;
        // 期数大于1期，且最近一期的预计后货时间大于当前时间，不能发货
        if (period > 1 && lastDeliveryTime) {
          const lastDeliveryTimestamp = new Date(lastDeliveryTime).getTime();
          if (lastDeliveryTimestamp > now) {
            this.showMultiPeriodNotInTimeWarning();
            bizLogger.end({
              name: ORDER_SEND_PROCESS,
              type: StageStatus.Finish,
              detail: {
                title: '订单发货-周期够订单正常业务流程中断',
              },
            });
            return;
          }
        }
      }

      if (selectedItems.length === 0) {
        Notify.error('请至少选择一件商品');
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Finish,
          detail: {
            title: '订单发货-没有选择商品正常业务中断',
          },
        });
        return;
      }

      if (this.state.deliveryType === -1) {
        Notify.error('请选择发货方式');
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Finish,
          detail: {
            title: '订单发货-没有选择发货方式正常业务中断',
          },
        });
        return;
      }

      const refundItems = this.getRefundItems();

      if (refundItems.length > 0 && !isFromVideoShop) {
        this.showHasRefundItemsAlert(refundItems, deliveryInfo);
        return;
      }

      if (isSmallStoreOrder) {
        return this.handleOpenWXSmallShopDialog(deliveryInfo);
      }

      this.submitData(deliveryInfo);
    } catch (error) {
      // console.log(error);
    }
  };

  // 发货成功后的处理函数
  afterSendExpressSuccess = (deliveryInfo: IDeliveryInfo, res) => {
    const printer = deliveryInfo.expressWayBill?.printerId;
    if (printer) {
      doPrint(printer, res)
        .then(() => {
          Notice.push(<Notice type="success" title="打印成功"></Notice>);
        })
        .catch((err) => {
          Notice.push(
            <Notice type="error" title="打印失败">
              {err.message || err.msg || err}
            </Notice>,
          );
        });
    }
  };

  // 是否为新电子面单
  get isNewWayBill() {
    return +window._global.electronWayBillVersion === 2;
  }

  async submitData(deliveryInfo: IDeliveryInfo) {
    const {
      deliveryUrl,
      selectedItems,
      handleExpressSuccess,
      orderNo,
      isWholeSend,
      callSource,
    } = this.props;
    const deliveryItems = this.getSelectedItems();
    // 对单品多运订单需要单独处理
    const { isSingleGoodsMultiExpress, itemPackList, ...rest } = deliveryInfo;
    if (isSingleGoodsMultiExpress && itemPackList!.length > 0) {
      // @ts-ignore
      deliveryItems[0].itemPackList = itemPackList;
    }

    const postData = {
      orderNo,
      deliveryItems,
      deliveryInfo: rest,
      wholeOrderDeliverTag: !!isWholeSend,
      callSource,
    };

    this.setState({
      submitting: true,
    });


    if (deliveryInfo.expressWayBill && this.isNewWayBill) {
      await beforeConfirmShipment(deliveryInfo.expressWayBill).catch(() => {
        this.setState({
          submitting: false,
        });
        return Promise.reject();
      })
    }

    const deliveryApi = makeRequest('POST', deliveryUrl, postData, {
      'Content-Type': 'application/json',
    });

    const quickBackAdApi = () => {
      const LS = Number(window.localStorage.getItem(quickbackAdNextShowLocalStorageKey));
      const currentTime = new Date().valueOf();
      if (!LS || LS <= currentTime) {
        return api.checkCanOpenQuickBackAd();
      }
      return Promise.resolve({
        showAdvertising: false
      })
    }
    
    Promise.all([ deliveryApi, quickBackAdApi() ])
      .then(([data, QuickBackAd]) => {
        if (!!QuickBackAd?.nextShowDay) {
          window.localStorage.setItem(
            quickbackAdNextShowLocalStorageKey,
            QuickBackAd?.nextShowDay
          );
        }
        if (!QuickBackAd?.showAdvertising) {
          Notify.success('发货成功');
        }
        handleExpressSuccess(selectedItems.length, !!QuickBackAd?.showAdvertising);
        // 在线下单模式 && 新电子面单模式 && 发货类型不是快递员上门打印面单
        if (
          deliveryInfo.deliveryType === 14 &&
          +window._global.electronWayBillVersion === WaybillVersionEnum.new &&
          deliveryInfo.expressWayBill?.expressWayBillType !==
            EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value
        ) {
          this.afterSendExpressSuccess(deliveryInfo, data);
        }
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Finish,
          detail: {
            title: '订单发货-发货成功',
          },
        });
      })
      .catch((msg = '') => {
        // 正常应该是通过指定code来区分错误 后端反馈目前code是在seller统一处理 返回的错误code也会出现在其他业务报错中
        /**
         * 1、微信小商店订单维权中
         * 2、微信小商店订单发货失败
         * 3、微信小商店订单发货失败：【xxx】等商品已经在小商店侧发布
         * 4、微信小商店订单仅支持自己联系快递方式发货
         * 5、微信小商店订单不支持修改物流
         */
        if (
          msg.indexOf('微信小商店订单维权中') >= 0 ||
          msg.indexOf('微信小商店订单发货失败') >= 0
        ) {
          let content = '订单已在小商店侧完成发货，点击“确认”后，本订单状态将更新为已发货';
          if (msg.indexOf('微信小商店订单维权中') >= 0) {
            content = '订单下处于正在售后中的商品，暂不支持发货；点击“确认”后，更新订单状态';
          }
          Sweetalert.alert({
            closeBtn: true,
            maskClosable: true,
            content,
            onConfirm: async () => {
              await api.wechatSmallShopOutOrderSync(this.props.outOrderNo);
              handleExpressSuccess(0);
            },
            confirmText: '刷新',
            parentComponent: this,
          });
        } else {
          Notify.error(msg || '系统繁忙，请稍后重试');
        }
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Error,
          detail: {
            title: '订单发货-发货失败',
            message: msg,
          },
        });
      })
      .finally(() => {
        this.setState({
          submitting: false,
        });
      });
  }

  showHasRefundItemsAlert(
    refundItems: IDeliveryWindowItemDetailInfo[],
    deliveryInfo: IDeliveryInfo,
  ) {
    Sweetalert.confirm({
      confirmType: 'danger',
      confirmText: '确定发货',
      cancelText: '取消',
      content: (
        <div>
          <div>发货后，以下商品的退款申请都将自动关闭，操作带来的后果由商家自行承担。</div>
          <div>是否确认发货？</div>
          <div className="gray" style={{ marginTop: '15px' }}>
            {refundItems.map((item) => {
              return <div key={item.item_id}>{item.name}</div>;
            })}
          </div>
        </div>
      ),
      title: '提醒',
      onConfirm: () => {
        this.submitData(deliveryInfo);
      },
      onCancel: () => {
        bizLogger.end({
          name: ORDER_SEND_PROCESS,
          type: StageStatus.Finish,
          detail: {
            title: '订单发货-主动取消发货正常业务中断',
          },
        });
      },
    });
  }

  showMultiPeriodNotInTimeWarning() {
    const { onClose, model } = this.props;
    onClose && onClose();
    const { last_estimate_delivery_time: lastDeliveryTime } = model.multi_period_delivery_info!;
    openDialog({
      title: '周期购发货提示',
      dialogId: MULTI_PERIOD_DELIVERY_ID,
      style: { width: 420 },
      children: (
        <div className="multi-period-limit-dialog">
          <div className="multi-period-limit-dialog--content">
            上一期发货预计 <span>{lastDeliveryTime}</span>{' '}
            送达。当天零点后，才能填写本期发货物流信息，完成发货。
          </div>
          <div className="multi-period-limit-dialog--footer">
            <Button
              type="primary"
              onClick={() => {
                closeDialog(MULTI_PERIOD_DELIVERY_ID);
              }}
            >
              稍后发货
            </Button>
            <Button onClick={() => closeDialog(MULTI_PERIOD_DELIVERY_ID)}>关闭</Button>
          </div>
        </div>
      ),
      footer: null,
    });
  }

  renderDeliveryInfo() {
    const model: any = this.props.model || {};
    const { orderNo, callback } = this.props;
    const {
      dist_type: distType,
      consignee_info: consigneeInfo, // 收货人信息
      dist_type_desc: distTypeDesc,
      self_fetch: selfFetchInfo,
      isExchange,
    } = model;
    // 之前自提订单不展示自提信息，有赞连锁项目改为展示，换货情况除外
    if (isExchange) {
      return null;
    }
    return (
      <DeliveryInfo
        distType={distType}
        distTypeDesc={distTypeDesc}
        consigneeInfo={consigneeInfo}
        selfFetchInfo={selfFetchInfo}
        orderNo={orderNo}
        callback={callback}
      />
    );
  }

  renderDeliveryTypeSelection() {
    const { deliveryType } = this.state;
    // eslint-disable-next-line @typescript-eslint/camelcase
    const { no_need_to_deliver, isExchange } = this.props.model;
    let type = 'express';
    if (this.isIntracity) {
      type = 'intracity';
    } else if (this.isSelfPickup) {
      type = 'selfPick';
    }
    return (
      <DeliveryTypeSelection
        type={type}
        deliveryType={deliveryType}
        // eslint-disable-next-line @typescript-eslint/camelcase
        noNeedToDeliver={no_need_to_deliver}
        onChange={this.handleDeliveryTypeChange}
        isExchange={isExchange}
        isMultiPeriod={this.isMultiPeriod}
        isSmallStoreOrder={this.props.isSmallStoreOrder}
        model={this.props.model}
        XdExpressWaybillMode={XdExpressWaybillMode}
      />
    );
  }

  renderRestFields() {
    const { deliveryType, balance, submitting, availableWechatExpress, isFromVideoShop } =
      this.state;
    const {
      model,
      orderNo,
      calculateFeeUrl,
      selectedItems,
      zentForm,
      handleSubmit,
      callSource,
      kdtId,
      isSmallStoreOrder,
    } = this.props;
    if (this.isNoramlExpress) {
      const { orderInfo } = this.context;
      // 普通快递发货
      return (
        <NormalExpress
          deliveryType={deliveryType}
          model={model}
          balance={balance}
          orderNo={orderNo}
          selectedItems={selectedItems}
          zentForm={zentForm}
          onSubmit={this.handleSubmit}
          handleSubmit={handleSubmit}
          submitting={submitting}
          availableWechatExpress={availableWechatExpress}
          isFromVideoShop={isFromVideoShop}
          XdExpressWaybillMode={XdExpressWaybillMode}
          orderInfo={orderInfo}
        />
      );
    }

    if (this.isIntracity) {
      // 同城送
      return (
        <Intracity
          deliveryType={deliveryType}
          model={model}
          balance={balance}
          orderNo={orderNo}
          calculateFeeUrl={calculateFeeUrl}
          callSource={callSource}
          zentForm={zentForm}
          handleSubmit={handleSubmit}
          onSubmit={this.handleSubmit}
          submitting={submitting}
          kdtId={kdtId}
          selectedItems={selectedItems}
          isSmallStoreOrder={isSmallStoreOrder}
        />
      );
    }

    if (this.isSelfPickup) {
      // 自提订单
      const { isExchange } = model;
      return isExchange ? (
        <SelfPickup
          deliveryType={deliveryType}
          model={model}
          orderNo={orderNo}
          zentForm={zentForm}
          handleSubmit={handleSubmit}
          onSubmit={this.handleSubmit}
          submitting={submitting}
        />
      ) : (
        <SelfPickupVerify
          deliveryType={deliveryType}
          model={model}
          orderNo={orderNo}
          zentForm={zentForm}
          handleSubmit={handleSubmit}
          onSubmit={this.handleSubmit}
          submitting={submitting}
        />
      );
    }
  }

  render() {
    return (
      <Form className="action-area" horizontal>
        {this.renderDeliveryInfo()}
        {this.renderDeliveryTypeSelection()}
        {this.renderRestFields()}
      </Form>
    );
  }
}

export default createForm()(ExpressForm);
