import ExpressDialog from './ExpressDialog';
import WXSmallShopDialog from './WXSmallShopDialog';
import QuickBackAdDialog from './QuickBackLoanAdDialog';
import openDialog from './open';

const DIALOG_ID = 'wsc-order-express-dialog';
const WX_SMALL_SHOP_DIALOG_ID = 'wx-small-shop-dialog-id';
const QUICKBACK_AD_DIALOG_ID = 'quickback-ad-dialog-id';

import './style.scss';

/**
 * 发货弹框
 */
export default function openExpressDialog(options) {
  return openDialog(
    {
      dialogId: DIALOG_ID,
      ...options,
    },
    ExpressDialog,
  );
}
// 微信小商店发货弹窗
export function openWXSmallShopDialog(options) {
  return openDialog(
    {
      dialogId: WX_SMALL_SHOP_DIALOG_ID,
      ...options,
    },
    WXSmallShopDialog,
  );
}

// 快速回款弹窗广告
export function openQuickBackAdDialogProps(options = {}) {
  return openDialog(
    {
      dialogId: QUICKBACK_AD_DIALOG_ID,
      ...options,
    },
    QuickBackAdDialog,
  );
}
