import { makeRequest } from './make-request';

/**
 * 无限获取小程序二维码
 *
 * PS: 从 shared/fns/fetch-weapp-code.js 迁移而来; 那边 assign 会报错
 *
 * @param {String | Number} id: 店铺id
 * @param {Object} params: 推广码需要携带的参数
 * @param {Boolean} useCommon: 是否为公共版小程序
 * @return Promise
 *
 * @example:
 *  const kdtId = 55;
 *	const params = {
 *     page: 'pages/home/<USER>/index' // 启动路径
 *     alias: pageAlias, // 其他参数
 *  }
 *  const useCommon = true;
 *  const hyaLine = false;
 *  getWeappCodeUltra(kdtId, config, useCommon, hyaLine).then(res => {})
 *
 * 文档: https://doc.qima-inc.com/pages/viewpage.action?pageId=47037959
 */
export function getWeappCodeUltra({ kdtId, guestKdtId = kdtId, config, hyaLine = false }) {
  // 默认请求参数
  const defaultData = {
    hyaLine,
    kdtId,
    page: 'pages/common/blank-page/index', // 落地页，写死
    params: {
      kdtId, // 店铺ID，必须与外部 kdtId 一致
      guestKdtId, // 真正的 kdtId
    },
  };

  // 实际请求参数，整合config参数, 具体见https://doc.qima-inc.com/pages/viewpage.action?pageId=47037959
  const data = {
    ...defaultData,
    params: {
      ...defaultData.params,
      ...config,
    },
  };

  return makeRequest('/v4/shop/api/weappCodeUltra', { params: JSON.stringify(data) });
}
