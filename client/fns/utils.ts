import formatDate from '@youzan/utils/date/formatDate';
import format from '@youzan/utils/money/format';
import args from '@youzan/utils/url/args';
import mobile from '@youzan/utils/validate/mobile';
import {
  isBeautyShop as IS_BEAUTY_STORE,
  isRetailHqStore,
  isUnifiedOnlineBranchStore,
} from '@youzan/utils-shop';
import isArray from 'lodash/isArray';

/**
 * 根据订单号获取订单详情url
 * @param {string} orderNo 订单号
 */
export const getOrderDetailUrl = (orderNo) => {
  if (IS_BEAUTY_STORE) {
    // 美业店铺
    return `/dashboard#/order/search/order/detail?orderNo=${orderNo}`;
  }
  if (_global.isSuperStore) {
    return `${_global.url.store}/order/order/orderdetail#/?order_no=${orderNo}`;
  }
  // 批发场景跳转 确保左侧为批发导航
  if (_global.isWholesale) {
    return `/v4/trade/wholesale/order/detail?orderNo=${orderNo}`;
  }

  return `/v4/trade/order/detail?orderNo=${orderNo}`;
};

/**
 * 获取退款详情url
 * @param {*} orderNo 订单号
 * @param {*} itemId 商品id
 */
export const getRefundDetailUrl = (orderNo, itemId?, refundId?) => {
  let result = `/v4/trade/refund/detail?orderNo=${orderNo}`;

  // 批发场景跳转 确保左侧为批发导航
  if (_global.isWholesale) {
    result = `/v4/trade/wholesale/refund/detail?orderNo=${orderNo}`;
  }
  if (itemId) {
    result = args.add(result, { itemId });
  }
  if (refundId) {
    result = args.add(result, { refundId });
  }
  return result;
};

/**
 * 将毫秒格式化成通用的格式
 * @param {*} ms 毫秒数
 */
export const formatMsTime = (ms: number) => {
  if (ms > 0) {
    return formatDate(ms, 'YYYY-MM-DD HH:mm:ss');
  }

  return '';
};

/**
 * 将分格式化成通用的格式
 * @param {*} ms 毫秒数
 */
export const formatCentMoney = (cent: number) => {
  return format(cent, true, false);
};

/**
 * 将json数组字符串转为数组
 * @param {string} jsonStr json字符串数组
 */

export const formatJsonStrToArr = <T>(jsonStr: T[] | string = []): T[] => {
  try {
    const arr = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
    return isArray(arr) ? arr : [];
  } catch {
    return [];
  }
};

export function generateSelectData(data) {
  const keys = Object.keys(data);
  return keys.map((key) => {
    return {
      value: key,
      text: data[key],
    };
  });
}

export function generateTabData(data) {
  const keys = Object.keys(data);
  return keys.map((key) => {
    return {
      key,
      title: data[key],
    };
  });
}

export function jsonParse(json, defaultValue = {}) {
  try {
    return JSON.parse(json);
  } catch (error) {
    return defaultValue;
  }
}

export function getBlurredPhone(data) {
  if (!mobile(data)) {
    return data;
  }
  return `${data.slice(0, 3)}****${data.slice(-4)}`;
}

export function matchDeliveryRouter() {
  const { pathname } = window.location;

  if (isUnifiedOnlineBranchStore || isRetailHqStore) {
    const matchExpress = pathname.includes('/v4/trade/express');

    return {
      matchExpress,
    };
  }

  return {};
}
