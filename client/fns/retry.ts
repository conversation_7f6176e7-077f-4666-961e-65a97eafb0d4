export interface RetryStrategy {
  shouldRetry(context: { error: unknown; attempt: number }): boolean;
  getDelay(context: { attempt: number }): number;
}

type RetryContext = {
  /** 已重试次数 */
  attempt: number;
  /** 重试延迟时间 */
  delay: number;
  /** 报错信息 */
  error: unknown;
};

type RetryOptions = {
  /** 重试策略 */
  strategy: RetryStrategy;
  /** 用于取消重试 */
  signal?: AbortSignal;
  /** 重试回调 */
  onRetry?: (context: RetryContext) => void;
};

/**
 * 重试函数
 * @param fn 需要重试的函数
 * @param options 重试选项
 * @returns 重试结果
 */
export async function retry<T>(fn: () => Promise<T>, options: RetryOptions): Promise<T> {
  const { strategy, signal, onRetry } = options;

  for (let attempt = 0; ; attempt++) {
    try {
      if (signal?.aborted) throw new Error('Retry aborted');
      // eslint-disable-next-line no-await-in-loop
      return await fn();
    } catch (error) {
      if (!strategy.shouldRetry({ error, attempt })) {
        throw error;
      }

      const delay = strategy.getDelay({ attempt });

      onRetry?.({ attempt: attempt + 1, delay, error });

      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(resolve, delay);
        signal?.addEventListener(
          'abort',
          () => {
            clearTimeout(timeout);
            reject(new Error('Retry aborted during delay'));
          },
          { once: true }
        );
      });
    }
  }
}

type ExponentialBackoffOptions = {
  baseDelay?: number;
  maxDelay?: number;
  jitter?: boolean;
  maxRetries?: number;
};

/**
 * 指数退避重试策略
 */
export class ExponentialBackoffStrategy implements RetryStrategy {
  private baseDelay: number;

  private maxDelay: number;

  private jitter: boolean;

  private maxRetries: number;

  constructor({
    baseDelay = 100,
    maxDelay = 10000,
    jitter = true,
    maxRetries = 5,
  }: ExponentialBackoffOptions = {}) {
    this.baseDelay = baseDelay;
    this.maxDelay = maxDelay;
    this.jitter = jitter;
    this.maxRetries = maxRetries;
  }

  shouldRetry({ attempt }: { attempt: number }): boolean {
    return attempt < this.maxRetries;
  }

  getDelay({ attempt }: { attempt: number }): number {
    let delay = this.baseDelay * 2 ** attempt;
    if (this.jitter) {
      delay = delay / 2 + Math.random() * (delay / 2);
    }
    return Math.min(delay, this.maxDelay);
  }
}

/**
 * 一个固定间隔的重试策略
 */
export class FixedIntervalStrategy implements RetryStrategy {
  private interval: number;

  private maxRetries: number;

  private jitter: boolean;

  constructor({ interval = 1000, maxRetries = 5, jitter = true } = {}) {
    this.interval = interval;
    this.maxRetries = maxRetries;
    this.jitter = jitter;
  }

  shouldRetry({ attempt }: { attempt: number }): boolean {
    return attempt < this.maxRetries;
  }

  getDelay(): number {
    let delay = this.interval;
    if (this.jitter) {
      delay = delay / 2 + Math.random() * (delay / 2);
    }
    return delay;
  }
}
