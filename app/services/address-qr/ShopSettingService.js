const BaseService = require('../base/BaseService');

/**
 * com.youzan.retail.trade.misc.api.service.ShopSettingService
 */
class ShopSettingService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.misc.api.service.ShopSettingService';
  }

  /**
   *  保存或更新寄件码配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1594231
   *
   *  @param {Object} request - 配置请求参数
   *  @param {string} request.configName - 寄件码名称
   *  @param {string} request.jumpType - 跳转方式编码 DIRECT ADD_ENTERPRISE_QW
   *  @param {string} request.backgroundColor - 背景颜色（十六进制代码，如#FFFFFF）
   *  @param {string} request.guideCopy - 引导文案
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.pageTitle - 页面标题
   *  @param {number} request.templateSize - 模板宽度
   *  @param {string} request.createdAt -
   *  @param {number} request.adminId - 操作人ID
   *  @param {number} request.liveCodeId -
   *  @param {number} request.id - 主键ID（空或<=0为新增，>0为修改）
   *  @param {string} request.backgroundType - 背景设置类型编码
   *  @param {string} request.updatedAt -
   *  @return {Promise}
   */
  async saveOrUpdateAddressConfig(request) {
    return this.invoke('saveOrUpdateAddressConfig', [request]);
  }

  /**
   *  分页查询寄件码配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1594232
   *
   *  @param {Object} request - 查询请求参数
   *  @param {string} request.configName - 配置名称（可选，用于模糊查询）
   *  @param {string} request.retailSource - 来源 如Android iOS web
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.pageNo - 页码，从1开始
   *  @param {string} request.requestId - 请求唯一标识
   *  @param {number} request.adminId - 卡门内部使用（操作人id）
   *  @param {number} request.pageSize - 每页大小
   *  @param {string} request.requestIp - 请求方ip
   *  @param {number} request.operatorId - 操作人ID
   *  @return {Promise}
   */
  async pageQueryConfig(request) {
    return this.invoke('pageQueryConfig', [request]);
  }

  /**
   *  删除寄件码配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1594233
   *
   *  @param {Object} request - 删除请求参数
   *  @param {string} request.retailSource - 来源 如Android iOS web
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.requestId - 请求唯一标识
   *  @param {number} request.adminId - 卡门内部使用（操作人id）
   *  @param {string} request.requestIp - 请求方ip
   *  @param {number} request.id - 配置ID
   *  @param {number} request.operatorId - 操作人ID
   *  @return {Promise}
   */
  async deleteAddressConfig(request) {
    return this.invoke('deleteAddressConfig', [request]);
  }

  /**
   * 根据ID查询寄件码配置
   * @param {Object} request - 查询请求参数
   */
  async getAddressConfigById(request) {
    return this.invoke('getAddressConfigById', [request]);
  }
}

module.exports = ShopSettingService;
