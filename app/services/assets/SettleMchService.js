const BaseService = require('./BasePaymentService');
const createPayGatewayService = require('@youzan/pay-gateway-plugin').default;

/**
 * 查询账户信息
 */
class SettleMchService extends BaseService {
  /**
   *  查询跨境结算商户
   * 响应里的certStatus:-1跨境服务未认证，暂不支持、0未开通、1申请中、2开通成功、3开通失败、4过期失效
   * settleChannel：HKCCB香港建行、BOCOM交通银行浙分
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1560204
   *  @param {Object} data - 跨境结算商户查询请求
   *  @param {string} data.payeeId - 商户号
   *  @return {Promise}
   */
  async queryMchCertInfo(data) {
    return this.payInvoke({
      service: 'youzan.pay.customs.declaration.api.service.SettleMchService',
      method: 'queryMchCertInfo',
      data,
    });
  }
}

module.exports = createPayGatewayService(SettleMchService);
