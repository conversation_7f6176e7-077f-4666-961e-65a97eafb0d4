const BaseService = require('../base/BaseService');

/**
 * com.youzan.channels.channel.core.api.service.ChannelCoreAccountService
 */
class ChannelCoreAccountService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.channels.channel.core.api.service.ChannelCoreAccountService';
  }

  /**
   * 根据kdtId查询微信公众号/微信小程序基础信息
   * 参数说明：accountType=1代表公众号；accountType=2代表小程序
   * kdtId 必填
   * accountType 必填
   * businessType 必填
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/502225
   *
   * @param {Object} param -
   * @param {number} param.kdtId - 店铺id
   * @param {string} param.appId - 外部唯一标识appId
   * @param {string} param.weixinId - 微信id
   * @param {number} param.accountType - 渠道类型:标识三方渠道类型
   * @param {number} param.businessType - 业务类型:标识三方渠道在有赞侧的业务用途
   * @param {number} param.mpId - 三方渠道在有赞的唯一标识mpId
   * @param {number} param.userId - 操作人id 写接口类必传,用于记录写操作的操作人
   * @return {Promise}
   */
  async queryMpAccountInfoByKdtId(param) {
    return this.invoke('queryMpAccountInfoByKdtId', [param]);
  }
}

module.exports = ChannelCoreAccountService;
