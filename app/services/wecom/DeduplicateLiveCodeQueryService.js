const BaseService = require('../base/BaseService');

/**
 * com.youzan.wecom.helper.api.operate.deduplicateLiveCode.DeduplicateLiveCodeQueryService
 */
class DeduplicateLiveCodeQueryService extends BaseService {
  SERVICE_NAME =
    'com.youzan.wecom.helper.api.operate.deduplicateLiveCode.DeduplicateLiveCodeQueryService';

  /**
   * 去重活码详情
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1351242
   * @param {Object} query
   * @return {Promise<Object>}
   */
  async getDetail(query) {
    return this.invoke('getDetail', [query]);
  }

  /**
   * 获取去重活码推广链接
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1351616
   * @param {Object} query
   * @return {Promise<Object>}
   */
  getPromotionUrl(query) {
    return this.invoke('getLiveCodeUrl', [query]);
  }
}

module.exports = DeduplicateLiveCodeQueryService;
