const BaseService = require('../base/BaseService');

/**
 * com.youzan.wecom.helper.core.api.corp.CorpCoreKVService
 */
class CorpCoreKVService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.wecom.helper.core.api.corp.CorpCoreKVService';
  }

  /**
   *  KV根据微商城id查询企微绑定的店铺id
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1281958
   *
   *  @param {Object} request -
   *  @param {number} request.wechatKdtId -
   *  @return {Promise}
   */
  async getWecomKdtIdByWechatMallId(request) {
    return this.invoke('getWecomKdtIdByWechatMallId', [request]);
  }
}

module.exports = CorpCoreKVService;
