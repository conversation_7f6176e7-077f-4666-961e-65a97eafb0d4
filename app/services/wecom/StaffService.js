const BaseService = require('../base/BaseService');

/**
 * com.youzan.wecom.helper.api.corp.baseservice.staff.StaffService
 */
class StaffService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.wecom.helper.api.corp.baseservice.staff.StaffService';
  }

  /**
   * 查询员工
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/985604
   *
   * @param {Object} request -
   * @param {string} request.wecomUserId -
   * @param {number} request.yzUserId -
   * @param {number} request.yzKdtId -
   * @param {Array.<Array>} request.fields[] - 要获取的字段。{@link StaffDTO}中{@link AggrField}注解字段默认不返回，需要显式在fields中包含
   * @param {number} request.staffId - staffId yzUserId wecomUserId三选一，staffId优先于yzUserId，yzUserId优先于wecomUserId
   * @param {Object} request.operator -
   * @param {number} request.status - 状态（0、正常，-1、删除，3、停用）
   * @return {Promise}
   */
  async get(request) {
    return this.invoke('get', [request]);
  }
}

module.exports = StaffService;
