const BaseService = require('../base/BaseService');

/**
 * com.youzan.wecom.helper.api.operate.deduplicateLiveCode.DeduplicateLiveCodeCommandService
 */
class DeduplicateLiveCodeCommandService extends BaseService {
  SERVICE_NAME =
    'com.youzan.wecom.helper.api.operate.deduplicateLiveCode.DeduplicateLiveCodeCommandService';

  /**
   *  创建去重活码
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1351238
   *
   *  @param {Object} command -
   *  @param {number} command.yzKdtId -
   *  @param {string} command.undertakeUrl - 老客承接跳转链接
   *  @param {Object} command.shift - 新客排班表
     后续建议使用 shifts
   *  @param {boolean} command.generalLiveCode - 创建去重活码：普通二维码
   *  @param {string} command.undertakeContentTitle - 老客承接引导标题
     后续使用 PageFurnishConfigDTO
   *  @param {string} command.remark - 客户备注
   *  @param {string} command.undertakeTitle - 老客承接页面标题
     后续使用 PageFurnishConfigDTO
   *  @param {Object} command.operator - 操作人信息
   *  @param {number} command.addLimitState - 添加人数上限状态：0-没有上限。1-有上限
   *  @param {string} command.undertakeStaffName - 老客承接员工展示名称
   *  @param {string} command.path - 小程序路径
   *  @param {number} command.tagScope - 标签范围，0：全部，1：仅新客
   *  @param {Object} command.newCustomizePageFurnish - 新客承页面接配置
   *  @param {Array.<Array>} command.wecomTagIds - 客户标签 id 列表
     最长 50 个标签
   *  @param {string} command.appId - 小程序 appId
   *  @param {Object} command.oldCustomizePageFurnish - 老客承接页面配置
   *  @param {Array.<Object>} command.shifts - 排班列表
     做多20个排班
                 *  @param {number} command.undertakeDistinguish - 新老客承接区分（0-区分、1-不区分）
   *  @param {Object} command.welcomeMsgConfig - 新客加好友欢迎语
   *  @param {number} command.undertakeMethod - 老客承接方式 1:显示已添加成员二维码 2：跳转指定链接
   *  @param {number} command.shiftType - 排班类型，1：全天在线，2：自动上下线
   *  @param {Array.<Object>} command.addLimits - 员工单日此渠道的加人上限
   *  @param {number} command.skipVerify - 自动通过好友验证，0：关闭，1：开启
   *  @param {string} command.qrCodeAvatarUrl - 二维码头像 url
   *  @param {string} command.undertakeContent - 老客承接引导文案
     后续使用 PageFurnishConfigDTO
   *  @param {number} command.contactWayGroupId - 所属分组ID
   *  @param {boolean} command.miniProgramLiveCode - 创建去重活码：小程序
   *  @param {number} command.hasDeduplicateRange - 是否有去重范围 0或null 否 1 是
   *  @param {string} command.name - 去重活码名称
   *  @param {number} command.liveCodeId - 去重活码ID
   *  @param {Object} command.deduplicateRange - 去重范围
   *  @param {number} command.contactWayType -
   *  @param {number} command.backupStaffId - 备用员工 id
   *  @return {Promise}
   */
  async create(command) {
    return this.invoke('create', [command]);
  }

  /**
   *  失效去重活码
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1351240
   *
   *  @param {Object} command -
   *  @param {number} command.yzKdtId -
   *  @param {number} command.liveCodeId -
   *  @param {Object} command.operator - 操作人信息
   *  @return {Promise}
   */
  async disable(command) {
    return this.invoke('disable', [command]);
  }
}

module.exports = DeduplicateLiveCodeCommandService;
