const BaseService = require('../base/BaseService');

/**
 * com.youzan.wecom.helper.core.api.open.corp.CorpOpenService
 */
class CorpOpenService extends BaseService {
  SERVICE_NAME = 'com.youzan.wecom.helper.core.api.open.corp.CorpOpenService';

  /**
   *  根据微商城店铺id查询绑定的企微店铺状态
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1412244
   *
   *  @param {number} mallKdtId - 微商城店铺ID
   *  @return {Promise}
   */
  async getBoundWecomStatus(mallKdtId) {
    return this.invoke('getBoundWecomStatus', [mallKdtId]);
  }
}

module.exports = CorpOpenService;
