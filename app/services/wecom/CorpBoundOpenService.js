const BaseService = require('../base/BaseService');

/**
 * com.youzan.wecom.helper.core.api.open.corp.CorpBoundOpenService
 */
class CorpBoundOpenService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.wecom.helper.core.api.open.corp.CorpBoundOpenService';
  }

  /**
   *  根据企微助手店铺ID查询最新绑定的微商城店铺ID（不支持连锁）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1190858
   *
   *  @param {number} wecomKdtId - 企微助手店铺ID
   *  @return {Promise}
   */
  async getBoundMallKdtId(wecomKdtId) {
    return this.invoke('getBoundMallKdtId', [wecomKdtId]);
  }
}

module.exports = CorpBoundOpenService;
