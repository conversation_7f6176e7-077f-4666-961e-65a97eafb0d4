const pluginPath = '/v4/trade/address-qr';
const pluginApiPath = `${pluginPath}/api`;
const controllerName = 'address-qr.IndexController';

/**
 * 含 /api 的路由就不需要以 .json 结尾
 */

module.exports = [
  /** 首页路由 */
  ['GET', pluginPath, controllerName, 'getIndexHtml'],

  /** 活动列表 */
  ['GET', `${pluginApiPath}/get-address-qr-list`, controllerName, 'getAddressQrList'],
  /** 查询活动详情 */
  ['GET', `${pluginApiPath}/get-address-qr-detail`, controllerName, 'getAddressQrDetail'],
  /** 创建/编辑活动 */
  [
    'POST',
    `${pluginApiPath}/create-or-update-address-qr`,
    controllerName,
    'createOrUpdateAddressQr',
  ],
  /** 删除活动 */
  ['POST', `${pluginApiPath}/delete-address-qr`, controllerName, 'deleteAddressQr'],
  /** 创建去重活码 */
  [
    'POST',
    `${pluginApiPath}/create-deduplicate-live-code`,
    controllerName,
    'createDeduplicateLiveCode',
  ],
  /** 失效去重活码 */
  [
    'POST',
    `${pluginApiPath}/invalid-deduplicate-live-code`,
    controllerName,
    'invalidDeduplicateLiveCode',
  ],
  /** 查询去重活码详情 */
  ['GET', `${pluginApiPath}/get-deduplicate-live-code-detail`, controllerName, 'getLiveCodeDetail'],
  /** 查询去重活码推广链接 */
  [
    'GET',
    `${pluginApiPath}/get-deduplicate-live-code-promotion-url`,
    controllerName,
    'getPromotionUrl',
  ],
  /** 路由兜底，默认进入 getIndexHtml */
  ['GET', `${pluginPath}/*`, controllerName, 'getIndexHtml'],
];
