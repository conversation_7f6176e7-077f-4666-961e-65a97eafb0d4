const BaseController = require('../base/BaseController');

const ShopSettingService = require('../../services/address-qr/ShopSettingService');
const CorpOpenService = require('../../services/wecom/CorpOpenService');
const DeduplicateLiveCodeCommandService = require('../../services/wecom/DeduplicateLiveCodeCommandService');
const DeduplicateLiveCodeQueryService = require('../../services/wecom/DeduplicateLiveCodeQueryService');
const ChannelCoreAccountService = require('../../services/weapp/ChannelCoreAccountService');
const StaffService = require('../../services/wecom/StaffService');
const CorpCoreKVService = require('../../services/wecom/CorpCoreKVService');
const CorpBoundOpenService = require('../../services/wecom/CorpBoundOpenService');

const { appName } = require('../../constants');

// staffService#get fields 可选参数; 传递后可查询对应字段
const STAFF_FIELDS = {
  ROLES: 'roles',
  DEPARTMENTS: 'departments',
};

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.serviceCache = {};
    this.ctx.biz = '扫码寄件';
  }

  getCacheService(ServiceClass, name) {
    const serviceName = name || ServiceClass.name;
    if (!this.serviceCache[serviceName]) {
      this.serviceCache[serviceName] = new ServiceClass(this.ctx);
    }
    return this.serviceCache[serviceName];
  }

  get shopSettingService() {
    return this.getCacheService(ShopSettingService);
  }

  get corpOpenService() {
    return this.getCacheService(CorpOpenService);
  }

  get deduplicateLiveCodeQueryService() {
    return this.getCacheService(DeduplicateLiveCodeQueryService);
  }

  get deduplicateLiveCodeCommandService() {
    return this.getCacheService(DeduplicateLiveCodeCommandService);
  }

  get staffService() {
    return this.getCacheService(StaffService);
  }

  get corpCoreKVService() {
    return this.getCacheService(CorpCoreKVService);
  }

  get corpBoundOpenService() {
    return this.getCacheService(CorpBoundOpenService);
  }

  getBaseParams(ctx) {
    return {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
    };
  }

  async initWeappId(ctx) {
    const WEXIN_APP = 2; // 微信小程序
    const WSC_BUSINESS = 1; // 商城
    const mpAccountInfo = await new ChannelCoreAccountService(ctx).queryMpAccountInfoByKdtId({
      kdtId: ctx.kdtId,
      accountType: WEXIN_APP,
      businessType: WSC_BUSINESS,
    });
    ctx.setGlobal('weappId', mpAccountInfo?.appId);
  }

  /**
   * 获取企微助手 kdtId
   */
  async getWecomKdtId(ctx) {
    return this.corpCoreKVService.getWecomKdtIdByWechatMallId({
      wechatKdtId: ctx.kdtId,
    });
  }

  async getWecomStaff(ctx, wecomKdtId) {
    return this.staffService.get({
      fields: [STAFF_FIELDS.ROLES],
      yzKdtId: wecomKdtId,
      yzUserId: ctx.userId,
      operator: {
        yzUserId: ctx.userId,
        clientIp: ctx.firstXff,
        appName,
      },
    });
  }

  // 获取打通企助的信息
  async initWecomStatus(ctx, wecomKdtId) {
    try {
      const wecomStatus = await this.corpOpenService.getBoundWecomStatus(
        /**
         * 获取绑定企微助手的店铺 kdtId.
         *
         * Why?
         * 主要连锁下, 当前店铺和绑定店铺可能不一致.
         *
         * 场景: 总部 A, 门店 B, 企微助手店铺 C, 企微助手店铺 C 绑定在门店 B 下
         * - 在总部 A 时, 如果直接传总部 kdtId 给 `getBoundWecomStatus` 接口, 查询为空
         * - 需要经过 `getKdtIdThatBindedWecom` 的逻辑才能拿到正确的店铺 kdtId(即门店 B kdtId)
         */
        await this.corpBoundOpenService.getBoundMallKdtId(wecomKdtId)
      );
      ctx.setGlobal('wecomStatus', wecomStatus);
      return wecomStatus;
    } catch (error) {
      ctx.setGlobal('wecomStatus', {});
      ctx.logger.warn(error.message);
    }
  }

  async initWecomStaff(ctx, wecomKdtId) {
    let staff = null;
    try {
      staff = await this.getWecomStaff(ctx, wecomKdtId);
    } catch (error) {
      ctx.logger.error(error.message);
    }
    ctx.setGlobal('wecomStaff', staff);
  }

  async getIndexHtml(ctx) {
    await Promise.all([
      // 初始化小程序部分，如果不用可以干掉
      this.initWeappStatusInfo(),
      this.initWeappVersion(),
      this.initWeappId(ctx),

      this.getWecomKdtId(ctx).then((wecomKdtId) => {
        return Promise.all([
          this.initWecomStatus(ctx, wecomKdtId),
          this.initWecomStaff(ctx, wecomKdtId),
        ]);
      }),
    ]);

    await ctx.render('address-qr/index.html');
  }

  /** 活动列表 */
  async getAddressQrList(ctx) {
    const result = await this.shopSettingService.pageQueryConfig({
      ...ctx.query,
      ...this.getBaseParams(ctx),
    });
    ctx.successRes(result);
  }

  async createOrUpdateAddressQr(ctx) {
    const result = await this.shopSettingService.saveOrUpdateAddressConfig({
      ...ctx.request.body,
      ...this.getBaseParams(ctx),
    });
    ctx.successRes(result);
  }

  async deleteAddressQr(ctx) {
    const result = await this.shopSettingService.deleteAddressConfig({
      ...ctx.request.body,
      ...this.getBaseParams(ctx),
    });
    ctx.successRes(result);
  }

  async getAddressQrDetail(ctx) {
    const result = await this.shopSettingService.getAddressConfigById({
      ...ctx.query,
      ...this.getBaseParams(ctx),
    });

    if (result.liveCodeId) {
      try {
        const data = await this.deduplicateLiveCodeQueryService.getDetail({
          liveCodeId: result.liveCodeId,
          yzKdtId: await this.getWecomKdtId(ctx),
        });

        result.weassDeduplicateLiveCode =
          data && data.state === /** 启用中 */ 1
            ? {
                name: data.name,
                liveCodeId: data.liveCodeId,
              }
            : null;
      } catch (err) {
        ctx.logger.error(err);
        result.weassDeduplicateLiveCode = null;
      }
    }

    ctx.successRes(result);
  }

  async createDeduplicateLiveCode(ctx) {
    const body = ctx.request.body;

    const wecomKdtId = await this.getWecomKdtId(ctx);

    const staff = await this.getWecomStaff(ctx, wecomKdtId);

    const result = await this.deduplicateLiveCodeCommandService.create({
      name: body.name,
      undertakeMethod: body.undertakeMethod,
      undertakeUrl: body.undertakeUrl,
      undertakeTitle: body.undertakeTitle,
      undertakeContentTitle: body.undertakeContentTitle,
      undertakeContent: body.undertakeContent,
      qrCodeAvatarUrl: body.qrCodeAvatarUrl,
      skipVerify: body.skipVerify,
      welcomeMsgConfig: body.welcomeMsgConfig,
      generalLiveCode: body.generalLiveCode,
      miniProgramLiveCode: body.miniProgramLiveCode,
      shiftType: body.shiftType,
      shifts: body.shifts,
      backupStaffId: body.backupStaffId,
      wecomTagIds: body.wecomTagIds,
      operator: {
        clientIp: ctx.firstXff,
        staffId: staff.staffId,
        appName,
      },
      tagScope: body.tagScope,
      yzKdtId: wecomKdtId,
      appId: body.appId,
      path: body.path,
      undertakeStaffName: body.undertakeStaffName,
      remark: body.remark,
      deduplicateRange: body.deduplicateRange,
      hasDeduplicateRange: body.hasDeduplicateRange,
      newCustomizePageFurnish: body.newCustomizePageFurnish,
      oldCustomizePageFurnish: body.oldCustomizePageFurnish,
      addLimits: body.addLimits,
      contactWayGroupId: body.contactWayGroupId,
    });
    ctx.successRes(result);
  }

  async invalidDeduplicateLiveCode(ctx) {
    const body = ctx.request.body;

    const wecomKdtId = await this.getWecomKdtId(ctx);

    const staff = await this.getWecomStaff(ctx, wecomKdtId);

    const result = await this.deduplicateLiveCodeCommandService.disable({
      yzKdtId: wecomKdtId,
      liveCodeId: body.liveCodeId,
      operator: {
        clientIp: ctx.firstXff,
        staffId: staff.staffId,
        appName,
      },
    });
    ctx.successRes(result);
  }

  async getLiveCodeDetail(ctx) {
    const wecomKdtId = await this.getWecomKdtId(ctx);

    const res = await this.deduplicateLiveCodeQueryService.getDetail({
      liveCodeId: ctx.query.liveCodeId,
      yzKdtId: wecomKdtId,
    });
    ctx.successRes(res);
  }

  async getPromotionUrl(ctx) {
    const wecomKdtId = await this.getWecomKdtId(ctx);

    const res = await this.deduplicateLiveCodeQueryService.getPromotionUrl({
      liveCodeId: ctx.query.liveCodeId,
      yzKdtId: wecomKdtId,
    });
    ctx.successRes(res);
  }
}

module.exports = IndexController;
