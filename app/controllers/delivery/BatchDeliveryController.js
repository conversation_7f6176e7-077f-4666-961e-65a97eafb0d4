const DeliveryBaseController = require('./DeliveryBaseController');
const BatchDeliveryService = require('../../services/delivery/BatchDeliveryService');
const OrderSearchService = require('../../services/search/OrderSearchService');
const ShopConfigReadService = require('../../services/shop/ShopConfigReadService');
const _ = require('lodash');
const utilsShop = require('@youzan/utils-shop');
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

class BatchDeliveryController extends DeliveryBaseController {
  get isRetailChainStore() {
    const { shopType, shopRole } = this.ctx.getState('shopInfo');
    return shopType === 7 && shopRole !== 0;
  }

  getBatchResource(isSplit = false) {
    const { isSuperStore } = this.ctx;
    const shopInfo = this.ctx.getState('shopInfo');
    if (
      utilsShop.checkRetailMinimalistBranchStore(shopInfo) ||
      utilsShop.checkEduBranchStoreV4(shopInfo)
    ) {
      return 'WSC';
    }
    if (this.isRetailChainStore) {
      return isSplit ? 'RETAIL_CHAIN_SPLIT' : 'RETAIL_CHAIN';
    }
    if (isSuperStore) {
      return isSplit ? 'RETAIL_SPLIT' : 'RETAIL';
    }
    return 'WSC';
  }

  // 该版本在原基础上 支持了微商城单店的多物流单号能力
  // PRD: https://qima.feishu.cn/wiki/wikcn1GGEjMm5aH8nWWQNjm5nrd?sheet=BHTyie
  getBatchResourceV2(isSplit = false, isWscSplit = false) {
    return isWscSplit ? 'WSC_SPLIT' : this.getBatchResource(isSplit);
  }

  getVersion(isWscSplit = false) {
    return isWscSplit ? 'THIRD' : 'FIRST';
  }

  async getIndexHtml(ctx) {
    const shopSupplyMode = await this.queryShopSupplyMode(ctx);
    ctx.setGlobal({ shopSupplyMode: +shopSupplyMode.value });
    ctx.setGlobal('operator', this.operator.operatorName);

    await Promise.all([this.getRetailShopRoles(ctx), this.initWxShopVersion(ctx)]);

    const shopInfo = ctx.getState('shopInfo');
    
    // 该店铺为零售店铺
    const isRetailShop = utilsShop.checkRetailShop(shopInfo);
    const isPureWscSingleStore = utilsShop.checkPureWscSingleStore(shopInfo);

    // 如果是微商城单店或者零售店铺，走新版批量发货
    if (isRetailShop || isPureWscSingleStore) {
      await ctx.render('delivery/batch-v2.html', {
        isSuperStore: ctx.isSuperStore,
      });
      return;
    }
    await ctx.render('delivery/batch.html', {
      isSuperStore: ctx.isSuperStore,
    });
  }

  async initWxShopVersion(ctx) {
    const { kdtId } = ctx;
    const config = await new ShopConfigReadService(ctx).queryShopConfig(
      kdtId,
      'is_upgraded_wx_shop',
    );
    ctx.setGlobal('is_upgraded_wx_shop', !!+config.value);
  }

  async getList(ctx) {
    const kdtId = +ctx.kdtId;
    const { batchStatus, operator, startDate, endDate, page, pageSize, batchBiz, deliveryPointId } = ctx.query;
    let postData = {
      kdtId,
      batchStatus: +batchStatus,
      operator,
      startDate: +startDate,
      endDate: +endDate,
      page: +page,
      pageSize: +pageSize,
      batchBiz,
    };
    // 有值才传
    if (deliveryPointId) {
      postData.deliveryPointId = +deliveryPointId;
    }
    // 过滤掉空值
    postData = _.pickBy(postData, val => val);
    const data = await new BatchDeliveryService(ctx).getBatchDetails(postData);
    ctx.json(0, 'ok', data);
  }

  async getUploadToken(ctx) {
    const { kdtId } = ctx;
    const token = await new BatchDeliveryService(ctx).getUploadToken({
      kdtId,
      operator: this.operator.operatorName,
      operatorId: `${this.operator.operatorId}`,
    });
    ctx.json(0, 'ok', token);
  }

  async uploadBatch(ctx) {
    const kdtId = +ctx.kdtId;
    const { filePath, isSplit, isWscSplit } = ctx.request.body;
    const shopInfo = ctx.getState('shopInfo');
    /**
     * 如果是总部（未加购供应链/过期，则按照订单模版，供应链能力生效，则按发货单模版）
     * 如果是门店/网店（ 铺货是按订单模版，供货是按发货单模版）
     *
     * BATCH_RETAIL_FULFILL_DELIVERY 发货单模版
     * BATCH_DELIVERY 订单模版
     **/
    let batchBiz = 'BATCH_DELIVERY'; // 订单模版

    const SupplyMode = await this.queryShopSupplyMode(ctx);
    // 是否有供应链能力
    const hasSupplyChainAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      kdtId,
      keys: [utilsShop.ShopAbility.SupplyChainAbility],
      mode: 'every',
    });
    if (utilsShop.checkUnifiedShop(shopInfo)) {
      if (utilsShop.checkHqStore(shopInfo) || utilsShop.checkPartnerStore(shopInfo)) {
        // 总店或合伙人
        if (hasSupplyChainAbility) {
          // 供应链能力生效
          batchBiz = 'BATCH_RETAIL_FULFILL_DELIVERY';
        } // 否则还是订单模版
      } else {
        /** 库存同步模式：0:供货，1:铺货 */
        if (SupplyMode.value === '0') {
          // 供货是按发货单模版
          batchBiz = 'BATCH_RETAIL_FULFILL_DELIVERY';
        } // 否则还是订单模版
      }
    }
    const result = await new BatchDeliveryService(ctx).access({
      kdtId,
      operator: this.operator.operatorName,
      operatorId: `${this.operator.operatorId}`,
      batchSource: this.getBatchResourceV2(isSplit, isWscSplit),
      batchBiz,
      batchParamDTO: {
        filePath,
      },
      version: this.getVersion(isWscSplit),
    });
    ctx.json(0, 'ok', result.batchNo);
  }

  async modifyBatch(ctx) {
    const kdtId = +ctx.kdtId;
    const { filePath, isWscSplit } = ctx.request.body;
    const result = await new BatchDeliveryService(ctx).access({
      kdtId,
      operator: this.operator.operatorName,
      operatorId: `${this.operator.operatorId}`,
      batchSource: this.getBatchResourceV2(false, isWscSplit),
      batchBiz: 'BATCH_MODIFY_EXPRESS',
      batchParamDTO: {
        filePath,
      },
      version: this.getVersion(isWscSplit),
    });
    ctx.json(0, 'ok', result.batchNo);
  }

  async queryProgressByNo(ctx) {
    const kdtId = +ctx.kdtId;
    const { batchNo } = ctx.query;
    const result = await new BatchDeliveryService(ctx).getBatchProgress(kdtId, batchNo);
    ctx.json(0, 'ok', result);
  }

  async batchDeliveryDetail(ctx) {
    const { batchNo, itemStatus, pageNum: page, pageSize, queryPrintItem } = ctx.query;
    const { kdtId } = ctx;
    const result = await new BatchDeliveryService(ctx).batchDeliveryDetail({
      batchNo,
      itemStatus,
      kdtId,
      page: +page,
      pageSize: +pageSize,
      queryPrintItem,
    });
    ctx.json(0, 'ok', result);
  }

  async lightOrderSearch(ctx) {
    const {
      startTime,
      endTime,
      sellerRemark,
      buyerMemo,
      goodsTitle,
      order,
      page,
      pageSize,
      orderby,
      expressType,
      type,
      excludeOrderType,
      excludeOrderSource,
      feedback,
      state,
      star,
      starNum,
    } = ctx.request.body;
    const { kdtId } = ctx;
    const keyword = {
      startTime,
      endTime,
      sellerRemark,
      buyerMemo,
    };
    const result = await new OrderSearchService(ctx).lightOrderSearch({
      keyword,
      goodsTitle,
      expressType,
      type,
      star,
      starNum,
      order,
      kdtId,
      page,
      pageSize,
      excludeOrderType,
      excludeOrderSource,
      feedback,
      state,
      orderby,
    });
    ctx.json(0, 'ok', result);
  }

  async batchDelivery(ctx) {
    const { kdtId } = ctx;
    const { orderNos } = ctx.request.body;
    const result = await new BatchDeliveryService(ctx).batchDelivery({
      kdtId,
      orderNos,
      operator: this.operator,
    });
    ctx.json(0, 'ok', result);
  }

  // 批量打单发货任务创建
  async batchDeliveryPrintCreate(ctx) {
    const { kdtId } = ctx;
    const { expressWayBill, orderNos, deliveryPointId } = ctx.getPostData();
    const params = {
      kdtId,
      expressWayBill,
      orderNos,
      operator: this.operator,
    };
    // 该店铺为零售店铺
    const shopInfo = ctx.getState('shopInfo');
    const isRetailShop = utilsShop.checkRetailShop(shopInfo);
    // 零售店铺打标
    if (isRetailShop) {
      params.batchBiz = 'BATCH_DELIVERY_PRINT';
      params.batchSource = 'RETAIL';
    }
    // 总部才会传deliveryPointId，透传给后端
    if (deliveryPointId) {
      params.deliveryPointId = +deliveryPointId;
    }
    const result = await new BatchDeliveryService(ctx).batchDeliveryPrintAccess(params);
    ctx.json(0, 'ok', result);
  }

  // 获取批量打单发货的打印报文
  async getBatchDeliveryPrintData(ctx) {
    const { batchNo, batchItemNo, templateUrl } = ctx.query;
    const result = await new BatchDeliveryService(ctx).queryBatchDeliveryPrintData({
      batchNo,
      batchItemNo,
      templateUrl,
    });
    ctx.json(0, 'ok', result);
  }

  // 更新批量任务打印状态
  async updateBatchPrintStatus(ctx) {
    const {
      batchNo,
      batchItemNo,
      failMsg,
      printSuccess,
      resetInterruptTask = false,
    } = ctx.getPostData();
    const result = await new BatchDeliveryService(ctx).updateBatchItemPrintStatus({
      batchNo,
      batchItemNo,
      failMsg,
      printSuccess,
      resetInterruptTask,
    });
    ctx.json(0, 'ok', result);
  }

  // 渲染批量打单任务详情
  async getBatchDeliveryPrintDetailHtml(ctx) {
    await ctx.render('delivery/batch-v2-detail.html', {
      filePath: 'batch-delivery-v2-detail/batch-delivery-print',
      pageName: '批量打单发货详情',
    });
  }

  // 批量打单发货前置校验
  async preCheckBatchDeliveryPrint(ctx) {
    const { kdtId } = ctx;
    const { orderNos } = ctx.request.body;
    const params = {
      kdtId,
      orderNos,
    };
    const result = await new BatchDeliveryService(ctx).preCheckBatchDeliveryPrint(params);
    ctx.json(0, 'ok', result);
  }
}

module.exports = BatchDeliveryController;
