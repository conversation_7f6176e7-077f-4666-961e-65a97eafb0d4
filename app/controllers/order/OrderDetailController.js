const BaseController = require('./OrderBaseController');
const RefundQueryService = require('../../services/fx/RefundQueryService');
const RefundOrderQueryService = require('../../services/safeguard/RefundOrderQueryService');
const OrderQueryService = require('../../services/order/OrderQueryService');
const OrderDeliveryService = require('../../services/delivery/OrderDeliveryService');
const OwlOrderInfoFacade = require('../../services/owl/pc/order/OwlOrderInfoFacade');
const RefundFacade = require('../../services/owl/pc/refund/RefundFacade');
const SellerRefundService = require('../../services/ebiz/SellerRefundService');
const RefundService = require('../../services/refund/RefundService');
const BuyGivePresentFacade = require('../../services/owl/pc/buygive/BuyGivePresentFacade');
const ShopConfigReadService = require('../../services/shop/ShopConfigReadService');
const { checkPureWscSingleStore } = require('@youzan/utils-shop');
const WeappTradeApiService = require('../../services/ebiz/WeappTradeApiService');
const InvoiceProviderQueryService = require('../../services/invoice/InvoiceProviderQueryService');
const FixDataService = require('../../services/delivery/FixDataService');
const lodash = require('lodash');

const { appName } = require('../../constants');

class OrderDetailController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '订单详情';
  }

  async getIndexHtml(ctx) {
    const [, , , , , isWholesale, isQttShop] = await Promise.all([
      this.initStoreId(),
      this.initTeamAdmin(),
      this.initVersionStatus(),
      // 初始化多网点开关状态
      this.initIsShowMultiStore(),
      this.initWechatDeliveryWhiteList(ctx),
      this.isInWholesaleWhiteList(ctx),
      this.checkIsQttShop(ctx),
      this.initYzyScriptInjection(ctx),
      this.checkHasQttFxAbility(),
      this.initWxShopVersion(ctx),
      this.initShowSalesmanWhiteList(ctx)
    ]);
    const apolloClient = this.ctx.apolloClient;
    const apolloConfig = await apolloClient.getConfig({
      appId: 'wsc-pc-goods',
      namespace: 'wsc-pc-goods.goods-migrate',
    });
    ctx.setGlobal(apolloConfig);
    ctx.setGlobal('isQttShop', isQttShop);

    const XdExpressWaybillSwitch = await apolloClient.getConfig({
      appId: 'video-channels-trade',
      namespace: 'video-channels-trade.share',
      key: 'xd_express_waybill_switch',
    });
    ctx.setGlobal({
      XdExpressWaybillSwitch,
    });
    await Promise.all([this.setInvoiceConfigByOrderNo(ctx), this.setSupportInvoiceProvider(ctx)]);

    const shopCheck = (shopInfo, _) => {
      // 新版订单列表仅支持微商城单店,且不支持批发店铺
      if (checkPureWscSingleStore(shopInfo) && !isWholesale) {
        return true;
      }
      return false;
    };

    const useNewOrderList = await this.useNewOrderStyle('new-order-list', shopCheck);
    const template = !useNewOrderList ? 'order/detail.html' : 'order/detail-v2.html';

    const backHref = '/v4/trade/order/index';

    await ctx.render(template, { href: backHref });
  }

  async initShowSalesmanWhiteList(ctx) {
    const { kdtId } = ctx;
    const showSalesman = await this.grayRelease('show_wsc_order_detail_salesman', kdtId);
    ctx.setGlobal({ showSalesman });
  }

  async setSupportInvoiceProvider(ctx) {
    const { kdtId } = ctx;
    try {
      const list = await new InvoiceProviderQueryService(ctx).queryInvoiceProviderList({ kdtId });
      ctx.setGlobal('supportInvoiceProviderList', list);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('err', err);
    }
  }

  async initWxShopVersion(ctx) {
    const { kdtId } = ctx;
    const config = await new ShopConfigReadService(ctx).queryShopConfig(
      kdtId,
      'is_upgraded_wx_shop',
    );
    ctx.setGlobal('is_upgraded_wx_shop', !!+config.value);
  }

  // apollo维护了各个服务商不同的属性，比如是否支持专票。
  async setInvoiceConfigByOrderNo(ctx) {
    const invoiceConfig = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-trade',
      namespace: 'wsc-pc-trade.application',
      key: 'invoice_config',
    });
    const invoiceProviderCode = await this.getInvoiceProvider(ctx);
    const invoiceProviderConfig = invoiceConfig[invoiceProviderCode] || invoiceConfig.DEFAULT;
    ctx.setGlobal('invoiceProviderConfig', invoiceProviderConfig);
  }

  /**
   * 发票服务商获取逻辑：
   * 1. 首先获取当前订单号是否有有效发票信息，如果有则拿到该发票的服务商
   * 2. 如果没有，则查找当前店铺的生效的发票服务商
   */
  async getInvoiceProvider(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;
    try {
      // @copy by OrderController.listInvoice
      const invoiceList = await new OrderQueryService(ctx).listInvoice({ orderNo, kdtId });
      // 把蓝字的筛出来，蓝字的只有一个（目前是这样的）
      const result = lodash.find(invoiceList || [], item => item.invoiceType === 2);
      if (result?.providerCode) {
        return result?.providerCode;
      }
      const { providerCode } = await new InvoiceProviderQueryService(ctx).queryInvoiceProvider({
        kdtId,
      });
      return providerCode;
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('err', err);
      return 'YOUZAN';
    }
  }

  async getShareOfRefundFee(ctx) {
    const { kdtId } = ctx;
    const { orderNo, orderItemId, refundFee } = ctx.query;
    this.validator.required(orderNo, '订单编号不能为空');
    this.validator.required(orderItemId, '采购单商品 orderItemId 不能为空');
    const data = await new RefundQueryService(ctx).getFxRefundableFeeByPurchaseOrder({
      kdtId,
      orderItemId,
      orderNo,
      refundFee: +refundFee,
    });

    return ctx.successRes(data);
  }

  // 获取订单详情数据
  async getOrderDetail(ctx) {
    const { kdtId } = ctx;
    const { orderNo, withCustomInfo = false, storeId = 0 } = ctx.query;

    const params = {
      kdtId,
      orderNo,
      withChildInfo: true,
      withRemark: true,
      withItemInfo: true,
      withPaymentInfo: true,
      withSourceInfo: true,
      withOrderAddressInfo: true,
      withBuyerInfo: true,
      withMainOrderInfo: true,
      withCustomInfo,
    };

    // 网店管理员的shopId
    if (storeId > 0) {
      params.shopId = storeId;
    }

    const userPrivacyState = await this.queryUserPrivacy(ctx);
    const execFunc = userPrivacyState ? 'getOrderDetailFormat' : 'getOrderDetailFormatBlurred';

    const result = await new OrderQueryService(ctx)[execFunc](params);

    return result;
  }

  async getOrderDetailJson(ctx) {
    const result = await this.getOrderDetail(ctx);
    return ctx.successRes(result);
  }

  // 订单退款信息
  async getRefundListJson(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;

    const result = await new RefundOrderQueryService(ctx).getByOrderNo({
      kdtId,
      orderNo,
    });
    return ctx.successRes(result);
  }

  // 获取收货地址
  async getLogistics(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;
    const params = {
      kdtId,
      orderNo,
    };

    const result = await new OrderDeliveryService(ctx).logisticsQueryByOrderNo(params);
    return ctx.successRes(result);
  }

  getBaseLogisticsParams(ctx) {
    const { kdtId } = ctx;
    const {
      receiverName,
      deliveryStreet,
      deliveryProvince,
      deliveryPostalCode,
      deliveryCountry,
      deliveryDistrict,
      orderNo,
      deliveryCity,
      receiverTel,
    } = ctx.request.body;
    return {
      kdtId,
      orderNo,
      deliveryStreet,
      deliveryProvince,
      deliveryPostalCode,
      deliveryCountry,
      deliveryDistrict,
      deliveryCity,
      receiverTel,
      receiverName,
      operator: this.operator,
    };
  }

  // 收货地址修改接口
  async updateLogistics(ctx) {
    const result = await new OrderDeliveryService(ctx).updateLogistics(
      this.getBaseLogisticsParams(ctx),
    );
    return ctx.successRes(result);
  }

  // 收货地址修改接口
  async updateLogisticsV2(ctx) {
    const baseParams = this.getBaseLogisticsParams(ctx);
    const { modifyWaybill } = ctx.request.body;

    const result = await new OrderDeliveryService(ctx).updateLogisticsV2({
      ...baseParams,
      modifyWaybill: modifyWaybill && modifyWaybill === 'true', // 接口为表单数据，拿到的是string格式
    });
    return ctx.successRes(result);
  }

  // 收货地址修改接口--交易组件3.0订单
  async updateSellerAddress(ctx) {
    const { kdtId } = ctx;
    const {
      receiverName,
      deliveryStreet,
      deliveryProvince,
      deliveryPostalCode,
      deliveryCountry,
      deliveryDistrict,
      orderNo,
      deliveryCity,
      receiverTel,
    } = ctx.request.body;

    const result = await new WeappTradeApiService(ctx).updateSellerAddress({
      kdtId,
      orderNo,
      deliveryStreet,
      deliveryProvince,
      deliveryPostalCode,
      deliveryCountry,
      deliveryDistrict,
      deliveryCity,
      receiverTel,
      receiverName,
      operator: this.operator,
    });
    return ctx.successRes(result);
  }

  // 发货单列表信息
  async getDeliveryList(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;
    const result = await new OrderDeliveryService(ctx).listDistOrderByOrderNo({
      kdtId,
      orderNo,
    });
    return ctx.successRes(result);
  }

  // 知识课程类订单详情专用接口
  async getEduDetailByOrderNo(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;
    const result = await new OwlOrderInfoFacade(ctx).getByOrderNo(kdtId, orderNo, {
      includeMain: true,
    });
    return ctx.successRes(result);
  }

  // 退课
  async refund(ctx) {
    const refundCommand = ctx.request.body; // safe done 入参无 kdtId
    refundCommand.operatorId = this.operator.operatorId;
    refundCommand.operatorName = this.operator.operatorName;
    const result = await new RefundFacade(ctx).refundV2(ctx.kdtId, refundCommand);
    return ctx.successRes(result);
  }

  // 赠品列表
  async findBuyGivePresentPageByCondition(ctx) {
    const { orderNo, pageNumber = 1, pageSize = 10 } = ctx.getQueryData();
    const query = {
      orderNo,
    };
    const pageRequest = {
      pageNumber,
      pageSize,
      sort: {
        orders: [],
      },
    };
    const result = await new BuyGivePresentFacade(ctx).findPageByCondition(
      ctx.kdtId,
      query,
      pageRequest,
    );
    return ctx.successRes(result);
  }

  /**
   * 获取卡券列表
   * @param ctx
   * @returns {Promise<void>}
   */
  async queryTicketsList(ctx) {
    const { kdtId } = ctx;
    const { orderNo, page, pageSize } = ctx.getQueryData();

    const result = await new SellerRefundService(ctx).queryTicketsList({
      kdtId,
      orderNo,
      page,
      pageSize,
    });
    return ctx.successRes(result);
  }

  /**
   * 获取订单实际可退金额
   * @param {Context} ctx
   * @returns {Promise<void>}
   */
  async getRefundableFee(ctx) {
    const { kdtId } = ctx;
    const { itemId, orderNo } = ctx.getQueryData();

    this.injectRefundScContext(orderNo);

    const result = await new SellerRefundService(ctx).getRefundableFee({
      itemId,
      kdtId,
      orderNo,
    });
    return ctx.successRes(result);
  }

  /**
   * 主动退款查询订单是否需要取消同城送
   * @param {Context} ctx
   */
  async queryCancelDistOrderInfo(ctx) {
    const { kdtId } = ctx;
    const { itemId, orderNo } = ctx.getQueryData();
    const result = await new SellerRefundService(ctx).queryCancelDistOrderInfo({
      itemId,
      kdtId,
      orderNo,
    });
    return ctx.successRes(result);
  }

  /**
   * 商家主动退款or标记退款
   * @param {Context} ctx
   * @returns {Promise<void>}
   */
  async activeRefundBySeller(ctx) {
    const { kdtId } = ctx;
    const { operatorId } = this.operator;
    const {
      orderNo,
      itemId,
      refundFee,
      disabledTicketCount,
      couponIds,
      distId,
      needCancelDistOrder,
      refundNum,
      refundItems,
      extension,
      requestId,
      followRefundPresents,
      remark,
    } = ctx.request.body;
    const source = {
      clientIp: ctx.firstXff,
      from: appName,
    };

    const param = {
      kdtId,
      operatorId,
      source,
      orderNo,
      itemId,
      refundFee,
      disabledTicketCount,
      couponIds,
      distId,
      needCancelDistOrder,
      refundNum,
      extension,
      requestId,
      scOperatorStr: this.scOperatorStr,
      followRefundPresents,
      remark,
    };

    // 如果wechat_complaint_check是字符串，需要转换一下成boolean类型
    if (typeof param.extension?.wechat_complaint_check === 'string') {
      param.extension.wechat_complaint_check = extension.wechat_complaint_check === 'true';
    }

    if (refundItems) {
      try {
        param.refundItems = JSON.parse(refundItems);
      } catch (e) {
        ctx.logger.warn('商家主动退款整单参数错误');
      }
    }

    this.injectRefundScContext(orderNo);

    const result = await new SellerRefundService(ctx).activeRefundBySeller(param);
    return ctx.successRes(result);
  }

  /**
   * 获取店铺的单个配置
   * @param ctx
   * @returns {Promise<void>}
   */
  async getShopConfig(ctx) {
    const { kdtId } = ctx;
    const { key } = ctx.query;
    const result = await new ShopConfigReadService(ctx).queryShopConfig(kdtId, key);
    return ctx.successRes(result);
  }

  /**
   * 兼容云业务身份 - 该逻辑由 iron 移植过来
   */
  injectRefundScContext(orderNo) {
    const { ctx } = this;
    const userInfo = ctx.getLocalSession('userInfo');

    try {
      ctx.registerServiceChain('business_identity', {
        kdtId: ctx.kdtId,
        userId: userInfo.id,
        clientType: 'pc',
        pageName: 'orderRefund',
        orderNo,
      });
    } catch (e) {
      ctx.logger.warn('主动退款注入 sc 上下文失败：injectRefundScContext');
    }
  }

  async queryNotMeetConditionPresents(ctx) {
    const { refundItems, orderNo } = ctx.request.body || {};
    const result = await new RefundService(this.ctx).queryNotMeetConditionPresents({
      refundItems,
      kdtId: ctx.kdtId,
      orderNo,
    });
    ctx.json(0, 'ok', result);
  }

  // 更新物流状态
  async updateDeliveryState(ctx) {
    const { expressNo } = ctx.request.body || {};
    const result = await new FixDataService(this.ctx).tryQuerySfTrace(expressNo);
    ctx.json(0, 'ok', result);
  }

  async changeDeliveryArrived(ctx) {
    const { kdtId } = ctx;
    const { orderNo, packId } = ctx.request.body || {};
    const params = {
      kdtId,
      orderNo,
      packId,
      operator: this.operator
    };
    const result = await new OrderDeliveryService(this.ctx).deliveryArrived(params);
    ctx.json(0, 'ok', result);
  }
  
}

module.exports = OrderDetailController;
